#pragma once
#include <windows.h>
#include <string>
#include <vector>

// Enums and structures
enum class InjectionMethod {
    LOADLIBRARY,
    MANUAL_MAP,
    THREAD_HIJACKING,
    PROCESS_HOLLOWING
};

struct InjectionConfig {
    InjectionMethod method = InjectionMethod::MANUAL_MAP;
    bool randomizeHeaders = true;
    bool eraseHeaders = true;
    bool hideFromPEB = true;
    bool antiDebug = true;
    bool delayExecution = false;
    DWORD delayMs = 0;
    bool scrambleImports = true;
    bool useNtApi = true;
};

// Constants
constexpr DWORD INJECTION_TIMEOUT = 10000; // 10 seconds
constexpr DWORD MAX_RETRIES = 3;
constexpr DWORD SLEEP_INTERVAL = 100; // milliseconds

class Injector {
public:
    static bool Initialize();
    static void Shutdown();
    
    // Main injection methods
    static bool InjectDLL(DWORD processId, const std::wstring& dllPath, InjectionMethod method = InjectionMethod::MANUAL_MAP);
    static bool InjectDLL(const std::wstring& processName, const std::wstring& dllPath, InjectionMethod method = InjectionMethod::MANUAL_MAP);
    
    // Specific injection techniques
    static bool LoadLibraryInjection(HANDLE hProcess, const std::wstring& dllPath);
    static bool ManualMapInjection(HANDLE hProcess, const std::wstring& dllPath);
    static bool ThreadHijackingInjection(HANDLE hProcess, const std::wstring& dllPath);
    static bool ProcessHollowingInjection(const std::wstring& targetPath, const std::wstring& dllPath);
    
    // Utility functions
    static bool ValidateDLL(const std::wstring& dllPath);
    static bool CheckCompatibility(HANDLE hProcess, const std::wstring& dllPath);
    static bool WaitForInjection(HANDLE hProcess, DWORD timeout = INJECTION_TIMEOUT);
    
    // Evasion techniques
    static bool RandomizeHeaders(LPVOID imageBase, SIZE_T imageSize);
    static bool EraseHeaders(HANDLE hProcess, LPVOID imageBase);
    static bool HideFromPEB(HANDLE hProcess, LPVOID imageBase);
    static bool ScrambleImports(HANDLE hProcess, LPVOID imageBase);
    
    // Anti-debug measures
    static bool EnableAntiDebug(HANDLE hProcess);
    static bool CheckForDebuggers();
    static bool IsProcessBeingDebugged(HANDLE hProcess);
    
    // Configuration
    static void SetConfig(const InjectionConfig& config);
    static InjectionConfig GetConfig();
    static void SetVerbose(bool verbose);

    // Memory operations (public for ManualMapper access)
    static HANDLE OpenTargetProcess(DWORD processId);
    static LPVOID AllocateMemory(HANDLE hProcess, SIZE_T size, DWORD protection = PAGE_EXECUTE_READWRITE);
    static bool WriteMemory(HANDLE hProcess, LPVOID address, LPCVOID data, SIZE_T size);
    static bool ProtectMemory(HANDLE hProcess, LPVOID address, SIZE_T size, DWORD newProtection, DWORD* oldProtection = nullptr);
    static HANDLE CreateRemoteThread(HANDLE hProcess, LPTHREAD_START_ROUTINE startAddress, LPVOID parameter);
    static std::vector<BYTE> CreateLoadLibraryShellcode(const std::wstring& dllPath);

private:
    static bool initialized;
    static HMODULE ntdllModule;
    
    // Internal helper functions
    
    // Advanced techniques
    static bool InjectViaSetWindowsHookEx(DWORD processId, const std::wstring& dllPath);
    static bool InjectViaQueueUserAPC(HANDLE hProcess, const std::wstring& dllPath);
    static bool InjectViaNtCreateThreadEx(HANDLE hProcess, LPTHREAD_START_ROUTINE startAddress, LPVOID parameter);
    
    // Stealth functions
    static bool RandomDelay(DWORD minMs = 100, DWORD maxMs = 1000);
    static bool ObfuscateStrings();
    static bool CheckVirtualMachine();
    static bool CheckSandbox();
    
    // Error handling
    static void LogError(const std::string& function, DWORD errorCode = GetLastError());
    static std::string GetErrorString(DWORD errorCode);
};

// Callback types
typedef bool(*InjectionCallback)(const std::string& status, int progress);

// Advanced injection class with callbacks
class AdvancedInjector : public Injector {
public:
    static bool InjectWithCallback(DWORD processId, const std::wstring& dllPath, InjectionCallback callback);
    static bool InjectStealth(DWORD processId, const std::wstring& dllPath);
    static bool InjectReflective(HANDLE hProcess, const std::vector<BYTE>& dllData);
    
private:
    static InjectionCallback currentCallback;
    static bool ReportProgress(const std::string& status, int progress);
};
