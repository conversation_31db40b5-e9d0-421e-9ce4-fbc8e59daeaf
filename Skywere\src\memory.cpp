#include "pch.h"

// Static member definitions
bool Memory::initialized = false;
std::unordered_map<std::wstring, uintptr_t> Memory::moduleCache;
std::mutex Memory::cacheMutex;
uintptr_t Memory::clientBase = 0;
uintptr_t Memory::engine2Base = 0;
uintptr_t Memory::inputSystemBase = 0;

bool Memory::Initialize() {
    if (initialized) return true;
    
    LOG("Initializing memory system...");
    
    // Cache important modules
    clientBase = GetModuleBase(CLIENT_DLL);
    engine2Base = GetModuleBase(ENGINE2_DLL);
    inputSystemBase = GetModuleBase(INPUTSYSTEM_DLL);
    
    if (!clientBase) {
        LOG("Failed to get client.dll base address!");
        return false;
    }
    
    if (!engine2Base) {
        LOG("Failed to get engine2.dll base address!");
        return false;
    }
    
    LOGF("client.dll base: 0x%llX", clientBase);
    LOGF("engine2.dll base: 0x%llX", engine2Base);
    LOGF("inputsystem.dll base: 0x%llX", inputSystemBase);
    
    initialized = true;
    LOG("Memory system initialized successfully!");
    return true;
}

void Memory::Shutdown() {
    if (!initialized) return;
    
    std::lock_guard<std::mutex> lock(cacheMutex);
    moduleCache.clear();
    initialized = false;
    
    LOG("Memory system shutdown complete.");
}

uintptr_t Memory::GetModuleBase(const wchar_t* moduleName) {
    std::lock_guard<std::mutex> lock(cacheMutex);
    
    // Check cache first
    auto it = moduleCache.find(moduleName);
    if (it != moduleCache.end()) {
        return it->second;
    }
    
    // Get module handle
    HMODULE hModule = GetModuleHandle(moduleName);
    if (!hModule) {
        return 0;
    }
    
    uintptr_t base = reinterpret_cast<uintptr_t>(hModule);
    moduleCache[moduleName] = base;
    
    return base;
}

uintptr_t Memory::GetModuleSize(const wchar_t* moduleName) {
    HMODULE hModule = GetModuleHandle(moduleName);
    if (!hModule) return 0;
    
    // Simplified - return a default size
    return 0x1000000; // 16MB default
}

uintptr_t Memory::PatternScan(const wchar_t* moduleName, const char* pattern, const char* mask) {
    uintptr_t base = GetModuleBase(moduleName);
    if (!base) return 0;
    
    uintptr_t size = GetModuleSize(moduleName);
    if (!size) return 0;
    
    return PatternScan(base, size, pattern, mask);
}

uintptr_t Memory::PatternScan(uintptr_t start, size_t size, const char* pattern, const char* mask) {
    if (!IsValidAddress(start) || !pattern || !mask) return 0;
    
    size_t patternLength = strlen(mask);
    if (patternLength == 0) return 0;
    
    for (size_t i = 0; i <= size - patternLength; ++i) {
        if (ComparePattern(reinterpret_cast<const char*>(start + i), pattern, mask)) {
            return start + i;
        }
    }
    
    return 0;
}

bool Memory::IsValidAddress(uintptr_t address) {
    if (address == 0) return false;
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.State == MEM_COMMIT) && 
           (mbi.Protect & (PAGE_READONLY | PAGE_READWRITE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}

bool Memory::IsValidCodePtr(uintptr_t address) {
    if (!IsValidAddress(address)) return false;
    
    MEMORY_BASIC_INFORMATION mbi;
    if (VirtualQuery(reinterpret_cast<LPCVOID>(address), &mbi, sizeof(mbi)) == 0) {
        return false;
    }
    
    return (mbi.Protect & (PAGE_EXECUTE | PAGE_EXECUTE_READ | PAGE_EXECUTE_READWRITE));
}

std::string Memory::ReadString(uintptr_t address, size_t maxLength) {
    if (!IsValidAddress(address)) return "";
    
    std::string result;
    result.reserve(maxLength);
    
    for (size_t i = 0; i < maxLength; ++i) {
        char c = Read<char>(address + i);
        if (c == '\0') break;
        result += c;
    }
    
    return result;
}

std::wstring Memory::ReadWString(uintptr_t address, size_t maxLength) {
    if (!IsValidAddress(address)) return L"";
    
    std::wstring result;
    result.reserve(maxLength);
    
    for (size_t i = 0; i < maxLength; ++i) {
        wchar_t c = Read<wchar_t>(address + i * sizeof(wchar_t));
        if (c == L'\0') break;
        result += c;
    }
    
    return result;
}

bool Memory::CacheModule(const wchar_t* moduleName) {
    uintptr_t base = GetModuleBase(moduleName);
    return base != 0;
}

bool Memory::ComparePattern(const char* data, const char* pattern, const char* mask) {
    if (!data || !pattern || !mask) return false;
    
    for (; *mask; ++mask, ++data, ++pattern) {
        if (*mask == 'x' && *data != *pattern) {
            return false;
        }
    }
    
    return true;
}
