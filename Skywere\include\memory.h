#pragma once

#include "types.h"

class Memory {
public:
    static bool Initialize();
    static void Shutdown();

    // Module handling
    static uintptr_t GetModuleBase(const wchar_t* moduleName);
    static uintptr_t GetModuleSize(const wchar_t* moduleName);

    // Memory reading/writing
    template<typename T>
    static T Read(uintptr_t address) {
        if (!IsValidAddress(address)) return T{};
        __try {
            return *reinterpret_cast<T*>(address);
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return T{};
        }
    }

    template<typename T>
    static bool Write(uintptr_t address, const T& value) {
        if (!IsValidAddress(address)) return false;
        __try {
            *reinterpret_cast<T*>(address) = value;
            return true;
        }
        __except (EXCEPTION_EXECUTE_HANDLER) {
            return false;
        }
    }

    template<typename T>
    static T ReadChain(uintptr_t base, const std::vector<uintptr_t>& offsets) {
        uintptr_t address = base;
        for (size_t i = 0; i < offsets.size() - 1; ++i) {
            address = Read<uintptr_t>(address + offsets[i]);
            if (!IsValidAddress(address)) return T{};
        }
        return Read<T>(address + offsets.back());
    }

    // Pattern scanning
    static uintptr_t PatternScan(const wchar_t* moduleName, const char* pattern, const char* mask);
    static uintptr_t PatternScan(uintptr_t start, size_t size, const char* pattern, const char* mask);

    // Address validation
    static bool IsValidAddress(uintptr_t address);
    static bool IsValidCodePtr(uintptr_t address);

    // Utility functions
    static std::string ReadString(uintptr_t address, size_t maxLength = 256);
    static std::wstring ReadWString(uintptr_t address, size_t maxLength = 256);

    // Module bases (cached)
    static uintptr_t clientBase;
    static uintptr_t engine2Base;
    static uintptr_t inputSystemBase;

private:
    static bool initialized;
    static std::unordered_map<std::wstring, uintptr_t> moduleCache;
    static std::mutex cacheMutex;

    // Internal helpers
    static bool CacheModule(const wchar_t* moduleName);
    static bool ComparePattern(const char* data, const char* pattern, const char* mask);
};

// Convenience macros
#define READ_MEMORY(type, address) Memory::Read<type>(address)
#define WRITE_MEMORY(address, value) Memory::Write(address, value)
#define READ_CHAIN(type, base, ...) Memory::ReadChain<type>(base, {__VA_ARGS__})

// Types are now defined in types.h
