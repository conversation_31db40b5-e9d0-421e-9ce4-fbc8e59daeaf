@echo off
setlocal enabledelayedexpansion

echo.
echo ===============================================
echo    Skywere Advanced DLL Injector Build Script
echo ===============================================
echo.

:: Check for Visual Studio
set "VSWHERE=%ProgramFiles(x86)%\Microsoft Visual Studio\Installer\vswhere.exe"
if not exist "%VSWHERE%" (
    echo ERROR: Visual Studio not found!
    echo Please install Visual Studio 2022 or later.
    pause
    exit /b 1
)

:: Find Visual Studio installation
for /f "usebackq tokens=*" %%i in (`"%VSWHERE%" -latest -products * -requires Microsoft.VisualStudio.Component.VC.Tools.x86.x64 -property installationPath`) do (
    set "VSINSTALLDIR=%%i"
)

if not defined VSINSTALLDIR (
    echo ERROR: Visual Studio with C++ tools not found!
    echo Please install Visual Studio with C++ development tools.
    pause
    exit /b 1
)

:: Setup build environment
set "VCVARSALL=%VSINSTALLDIR%\VC\Auxiliary\Build\vcvarsall.bat"
if not exist "%VCVARSALL%" (
    echo ERROR: vcvarsall.bat not found!
    pause
    exit /b 1
)

echo Setting up build environment...
call "%VCVARSALL%" x64 >nul 2>&1

:: Check if MSBuild is available
where msbuild >nul 2>&1
if errorlevel 1 (
    echo ERROR: MSBuild not found in PATH!
    pause
    exit /b 1
)

:: Clean previous builds
echo Cleaning previous builds...
if exist "x64" rmdir /s /q "x64"
if exist "Debug" rmdir /s /q "Debug"
if exist "Release" rmdir /s /q "Release"

:: Build Debug configuration
echo.
echo Building Debug configuration...
msbuild Injector.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:PlatformToolset=v143 /m
if errorlevel 1 (
    echo ERROR: Debug build failed!
    pause
    exit /b 1
)

:: Build Release configuration
echo.
echo Building Release configuration...
msbuild Injector.vcxproj /p:Configuration=Release /p:Platform=x64 /p:PlatformToolset=v143 /m
if errorlevel 1 (
    echo ERROR: Release build failed!
    pause
    exit /b 1
)

:: Check if executables were created
if not exist "x64\Debug\Injector.exe" (
    echo ERROR: Debug executable not found!
    pause
    exit /b 1
)

if not exist "x64\Release\Injector.exe" (
    echo ERROR: Release executable not found!
    pause
    exit /b 1
)

:: Copy to output directory
echo.
echo Copying files to output directory...
if not exist "output" mkdir "output"

copy "x64\Release\Injector.exe" "output\Injector.exe" >nul
copy "README.md" "output\README.md" >nul

:: Create default configuration
echo Creating default configuration...
(
echo {
echo     "injection_method": "manualmap",
echo     "randomize_headers": true,
echo     "erase_headers": true,
echo     "hide_from_peb": true,
echo     "anti_debug": true,
echo     "delay_execution": false,
echo     "delay_ms": 0,
echo     "scramble_imports": true,
echo     "use_nt_api": true
echo }
) > "output\config.json"

:: Create usage examples
echo Creating usage examples...
(
echo @echo off
echo echo Skywere Injector Usage Examples
echo echo.
echo echo Basic injection:
echo echo Injector.exe cs2.exe Skywere.dll
echo echo.
echo echo Manual map with stealth:
echo echo Injector.exe -m manualmap -s cs2.exe Skywere.dll
echo echo.
echo echo Injection by PID with delay:
echo echo Injector.exe -p 1234 -d 5000 Skywere.dll
echo echo.
echo echo Wait for process:
echo echo Injector.exe -w -v notepad.exe test.dll
echo echo.
echo pause
) > "output\examples.bat"

:: Get file sizes
for %%f in ("x64\Debug\Injector.exe") do set "DEBUG_SIZE=%%~zf"
for %%f in ("x64\Release\Injector.exe") do set "RELEASE_SIZE=%%~zf"

:: Display build summary
echo.
echo ===============================================
echo                BUILD SUCCESSFUL
echo ===============================================
echo.
echo Debug build:   x64\Debug\Injector.exe   (!DEBUG_SIZE! bytes)
echo Release build: x64\Release\Injector.exe (!RELEASE_SIZE! bytes)
echo.
echo Output directory: output\
echo - Injector.exe (Release build)
echo - README.md
echo - config.json (default configuration)
echo - examples.bat (usage examples)
echo.
echo Build completed successfully!
echo.

:: Test the executable
echo Testing executable...
"x64\Release\Injector.exe" --help >nul 2>&1
if errorlevel 1 (
    echo WARNING: Executable test failed!
    echo The build may have issues.
) else (
    echo Executable test passed!
)

echo.
echo Ready to use! Run 'output\Injector.exe --help' for usage information.
echo.
pause
