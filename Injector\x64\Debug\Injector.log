﻿  main.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\main.cpp(62,30): error C2039: ' GetProcessId': não é um membro de 'Utils'
      C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\utils.h(10,7):
      consulte a declaração de 'Utils'
  
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\main.cpp(62,30): error C2664: 'DWORD GetProcessId(HANDLE)': não é possível converter um argumento 1 de 'std::wstring' em 'HANDLE'
      C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\main.cpp(62,43):
      Nenhum operador de conversão definida pelo usuário disponível que possa realizar esta conversão, ou o operador não pode ser chamado
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\processthreadsapi.h(621,1):
      consulte a declaração de 'GetProcessId'
      C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\main.cpp(62,30):
      ao tentar corresponder a lista de argumentos '(std::wstring)'
  
