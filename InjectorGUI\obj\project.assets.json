{"version": 3, "targets": {"net6.0-windows7.0": {"Guna.UI2.WinForms/*******": {"type": "package", "dependencies": {"System.Management": "6.0.0"}, "compile": {"lib/net6.0-windows7.0/Guna.UI2.dll": {}}, "runtime": {"lib/net6.0-windows7.0/Guna.UI2.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/7.0.2": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}}}, "libraries": {"Guna.UI2.WinForms/*******": {"sha512": "NYzSJp7e47Cz3NgGCHvzadash8ycmxxUnpLg4Rr/MSYG9WDw0HOoTI4SkbDW+HbACrf6tsWYCuzWkgvjuM/O6w==", "type": "package", "path": "guna.ui2.winforms/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "guna.ui2.winforms.*******.nupkg.sha512", "guna.ui2.winforms.nuspec", "icon.png", "lib/net40/Guna.UI2.dll", "lib/net45/Guna.UI2.dll", "lib/net461/Guna.UI2.dll", "lib/net472/Guna.UI2.dll", "lib/net48/Guna.UI2.dll", "lib/net6.0-windows7.0/Guna.UI2.dll", "lib/net7.0-windows7.0/Guna.UI2.dll", "lib/netcoreapp3.1/Guna.UI2.dll"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.2": {"sha512": "/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "type": "package", "path": "system.management/7.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.2.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {"net6.0-windows7.0": ["Guna.UI2.WinForms >= *******", "System.Management >= 7.0.2"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "F:\\SDK VISUAL ESTUDIO\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\teste hack cs2 real\\InjectorGUI\\InjectorGUI.csproj", "projectName": "InjectorGUI", "projectPath": "C:\\Users\\<USER>\\Desktop\\teste hack cs2 real\\InjectorGUI\\InjectorGUI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\teste hack cs2 real\\InjectorGUI\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["F:\\SDK VISUAL ESTUDIO\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net6.0-windows7.0": {"targetAlias": "net6.0-windows", "dependencies": {"Guna.UI2.WinForms": {"target": "Package", "version": "[*******, )"}, "System.Management": {"target": "Package", "version": "[7.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[6.0.36, 6.0.36]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[6.0.36, 6.0.36]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}