#pragma once

namespace Offsets {
    // Client.dll offsets (updated 2025-06-30)
    namespace Client {
        constexpr std::ptrdiff_t dwCSGOInput = 0x1A75250;
        constexpr std::ptrdiff_t dwEntityList = 0x1A020A8;
        constexpr std::ptrdiff_t dwGameEntitySystem = 0x1B25BD8;
        constexpr std::ptrdiff_t dwGameEntitySystem_highestEntityIndex = 0x20F0;
        constexpr std::ptrdiff_t dwGameRules = 0x1A66B38;
        constexpr std::ptrdiff_t dwGlobalVars = 0x1849EB0;
        constexpr std::ptrdiff_t dwGlowManager = 0x1A66270;
        constexpr std::ptrdiff_t dwLocalPlayerController = 0x1A50AD0;
        constexpr std::ptrdiff_t dwLocalPlayerPawn = 0x18560D0;
        constexpr std::ptrdiff_t dwPlantedC4 = 0x1A702F8;
        constexpr std::ptrdiff_t dwPrediction = 0x1855F50;
        constexpr std::ptrdiff_t dwSensitivity = 0x1A67858;
        constexpr std::ptrdiff_t dwSensitivity_sensitivity = 0x40;
        constexpr std::ptrdiff_t dwViewAngles = 0x1A75620;
        constexpr std::ptrdiff_t dwViewMatrix = 0x1A6B230;
        constexpr std::ptrdiff_t dwViewRender = 0x1A6BB80;
        constexpr std::ptrdiff_t dwWeaponC4 = 0x1A04590;
    }

    // Engine2.dll offsets
    namespace Engine2 {
        constexpr std::ptrdiff_t dwBuildNumber = 0x540BE4;
        constexpr std::ptrdiff_t dwNetworkGameClient = 0x53FCE0;
        constexpr std::ptrdiff_t dwNetworkGameClient_clientTickCount = 0x368;
        constexpr std::ptrdiff_t dwNetworkGameClient_deltaTick = 0x27C;
        constexpr std::ptrdiff_t dwNetworkGameClient_isBackgroundMap = 0x281447;
        constexpr std::ptrdiff_t dwNetworkGameClient_localPlayer = 0xF0;
        constexpr std::ptrdiff_t dwNetworkGameClient_maxClients = 0x238;
        constexpr std::ptrdiff_t dwNetworkGameClient_serverTickCount = 0x36C;
        constexpr std::ptrdiff_t dwNetworkGameClient_signOnState = 0x228;
        constexpr std::ptrdiff_t dwWindowHeight = 0x623564;
        constexpr std::ptrdiff_t dwWindowWidth = 0x623560;
    }

    // Buttons
    namespace Buttons {
        constexpr std::ptrdiff_t attack = 0x184E8F0;
        constexpr std::ptrdiff_t attack2 = 0x184E980;
        constexpr std::ptrdiff_t back = 0x184EBC0;
        constexpr std::ptrdiff_t duck = 0x184EE90;
        constexpr std::ptrdiff_t forward = 0x184EB30;
        constexpr std::ptrdiff_t jump = 0x184EE00;
        constexpr std::ptrdiff_t left = 0x184EC50;
        constexpr std::ptrdiff_t lookatweapon = 0x1A75170;
        constexpr std::ptrdiff_t reload = 0x184E860;
        constexpr std::ptrdiff_t right = 0x184ECE0;
        constexpr std::ptrdiff_t showscores = 0x1A75050;
        constexpr std::ptrdiff_t sprint = 0x184E7D0;
        constexpr std::ptrdiff_t turnleft = 0x184EA10;
        constexpr std::ptrdiff_t turnright = 0x184EAA0;
        constexpr std::ptrdiff_t use = 0x184ED70;
        constexpr std::ptrdiff_t zoom = 0x1A750E0;
    }

    // Entity offsets
    namespace Entity {
        // C_BaseEntity
        constexpr std::ptrdiff_t m_pGameSceneNode = 0x328;
        constexpr std::ptrdiff_t m_iHealth = 0x344;
        constexpr std::ptrdiff_t m_iMaxHealth = 0x340;
        constexpr std::ptrdiff_t m_lifeState = 0x348;
        constexpr std::ptrdiff_t m_hOwnerEntity = 0x440;
        constexpr std::ptrdiff_t m_vecVelocity = 0x400;
        constexpr std::ptrdiff_t m_vecBaseVelocity = 0x430;
        constexpr std::ptrdiff_t m_MoveType = 0x445;
        constexpr std::ptrdiff_t m_nTeamNum = 0x3E3;
        constexpr std::ptrdiff_t m_fFlags = 0x3EC;

        // C_BasePlayerPawn
        constexpr std::ptrdiff_t m_hController = 0x1264;
        constexpr std::ptrdiff_t m_pWeaponServices = 0x11A8;
        constexpr std::ptrdiff_t m_pItemServices = 0x11B0;
        constexpr std::ptrdiff_t m_pCameraServices = 0x11E0;
        constexpr std::ptrdiff_t m_pMovementServices = 0x11E8;

        // C_CSPlayerPawn
        constexpr std::ptrdiff_t m_bIsScoped = 0x23A0;
        constexpr std::ptrdiff_t m_bIsDefusing = 0x23A1;
        constexpr std::ptrdiff_t m_bIsGrabbingHostage = 0x23A2;
        constexpr std::ptrdiff_t m_iShotsFired = 0x23A4;
        constexpr std::ptrdiff_t m_flFlashDuration = 0x1478;
        constexpr std::ptrdiff_t m_flFlashMaxAlpha = 0x147C;
        constexpr std::ptrdiff_t m_ArmorValue = 0x23B4;
        constexpr std::ptrdiff_t m_bHasDefuser = 0x23BC;
        constexpr std::ptrdiff_t m_bHasHelmet = 0x23BD;
        constexpr std::ptrdiff_t m_bInBombZone = 0x23C0;
        constexpr std::ptrdiff_t m_bInBuyZone = 0x23C1;

        // CGameSceneNode
        constexpr std::ptrdiff_t m_vecOrigin = 0x80;
        constexpr std::ptrdiff_t m_angRotation = 0x8C;
        constexpr std::ptrdiff_t m_vecAbsOrigin = 0xD0;
        constexpr std::ptrdiff_t m_angAbsRotation = 0xDC;
        constexpr std::ptrdiff_t m_flScale = 0xA8;
        constexpr std::ptrdiff_t m_bDormant = 0xEF;

        // CCSPlayerController
        constexpr std::ptrdiff_t m_sSanitizedPlayerName = 0x768;
        constexpr std::ptrdiff_t m_hPlayerPawn = 0x824;
        constexpr std::ptrdiff_t m_bPawnIsAlive = 0x82C;
        constexpr std::ptrdiff_t m_iPawnHealth = 0x830;
        constexpr std::ptrdiff_t m_iPawnArmor = 0x834;
        constexpr std::ptrdiff_t m_bPawnHasDefuser = 0x838;
        constexpr std::ptrdiff_t m_bPawnHasHelmet = 0x839;

        // C_CSWeaponBase
        constexpr std::ptrdiff_t m_nNextPrimaryAttackTick = 0x1648;
        constexpr std::ptrdiff_t m_flNextPrimaryAttackTickRatio = 0x164C;
        constexpr std::ptrdiff_t m_nNextSecondaryAttackTick = 0x1650;
        constexpr std::ptrdiff_t m_flNextSecondaryAttackTickRatio = 0x1654;
        constexpr std::ptrdiff_t m_iClip1 = 0x1658;
        constexpr std::ptrdiff_t m_iClip2 = 0x165C;
        constexpr std::ptrdiff_t m_pReserveAmmo = 0x1660;
    }
}

// Module names
#define CLIENT_DLL L"client.dll"
#define ENGINE2_DLL L"engine2.dll"
#define INPUTSYSTEM_DLL L"inputsystem.dll"
