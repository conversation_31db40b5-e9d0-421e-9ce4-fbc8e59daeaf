#pragma once

// Windows Headers
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dwmapi.h>

// Standard Library
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <cmath>
#include <array>

// ImGui
#include "../external/imgui/imgui.h"
#include "../external/imgui/imgui_impl_win32.h"
#include "../external/imgui/imgui_impl_dx11.h"

// DirectX
#include <d3d11.h>
#include <dxgi.h>

// Project Headers
#include "types.h"
#include "offsets.h"
#include "memory.h"
#include "math_utils.h"
#include "entity.h"
#include "features.h"
#include "gui.h"
#include "config.h"

// Macros
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }

// Global Variables
extern HMODULE g_hModule;
extern bool g_bRunning;
extern std::atomic<bool> g_bShowMenu;

// Function Declarations
DWORD WINAPI MainThread(LPVOID lpParam);
void Cleanup();
bool HookDirectX();
LRESULT __stdcall hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam);

// Utility Macros
#define LOG(msg) OutputDebugStringA(("[SKYWERE] " + std::string(msg) + "\n").c_str())
#define LOGF(fmt, ...) { char buf[256]; sprintf_s(buf, fmt, __VA_ARGS__); LOG(buf); }
