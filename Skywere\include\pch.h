#pragma once

// Windows Headers
#include <windows.h>
#include <d3d11.h>
#include <dxgi.h>
#include <dwmapi.h>

// Standard Library
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <thread>
#include <chrono>
#include <mutex>
#include <atomic>
#include <unordered_map>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <cmath>
#include <array>

// ImGui
#include "../external/imgui/imgui.h"
#include "../external/imgui/imgui_internal.h"

// ImGui API macro
#ifndef IMGUI_IMPL_API
#define IMGUI_IMPL_API
#endif

// Forward declarations
struct ImDrawData;

// ImGui backends declarations
bool ImGui_ImplWin32_Init(void* hwnd);
void ImGui_ImplWin32_Shutdown();
void ImGui_ImplWin32_NewFrame();
LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

bool ImGui_ImplDX11_Init(ID3D11Device* device, ID3D11DeviceContext* device_context);
void ImGui_ImplDX11_Shutdown();
void ImGui_ImplDX11_NewFrame();
void ImGui_ImplDX11_RenderDrawData(ImDrawData* draw_data);

// Project Headers
#include "types.h"
#include "offsets.h"
#include "memory.h"
#include "math_utils.h"
#include "entity.h"
#include "features.h"
#include "gui.h"
#include "config.h"

// Macros
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p) = nullptr; } }

// Global Variables
extern HMODULE g_hModule;
extern bool g_bRunning;
extern std::atomic<bool> g_bShowMenu;

// Function Declarations
DWORD WINAPI MainThread(LPVOID lpParam);
void Cleanup();

// Utility Macros
#define LOG(msg) OutputDebugStringA(("[SKYWERE] " + std::string(msg) + "\n").c_str())
#define LOGF(fmt, ...) { char buf[256]; sprintf_s(buf, fmt, __VA_ARGS__); LOG(buf); }
