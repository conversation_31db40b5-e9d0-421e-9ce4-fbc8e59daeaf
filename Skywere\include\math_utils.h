#pragma once

#include <windows.h>
#include <cmath>
#include <math.h>
#include <random>
#include <vector>
#include "types.h"

// Forward declarations
class C_CSPlayerPawn;

// Math constants
constexpr float M_PI = 3.14159265358979323846f;
constexpr float M_PI_2 = M_PI / 2.0f;
constexpr float M_2PI = M_PI * 2.0f;
constexpr float DEG2RAD = M_PI / 180.0f;
constexpr float RAD2DEG = 180.0f / M_PI;

// Math utility class
class MathUtils {
public:
    // Angle normalization
    static void NormalizeAngles(QAngle& angles) {
        while (angles.x > 89.0f) angles.x -= 180.0f;
        while (angles.x < -89.0f) angles.x += 180.0f;
        while (angles.y > 180.0f) angles.y -= 360.0f;
        while (angles.y < -180.0f) angles.y += 360.0f;
        angles.z = 0.0f;
    }
    
    static float NormalizeAngle(float angle) {
        while (angle > 180.0f) angle -= 360.0f;
        while (angle < -180.0f) angle += 360.0f;
        return angle;
    }
    
    static float AngleDifference(float a, float b) {
        float diff = a - b;
        return NormalizeAngle(diff);
    }
    
    // Vector/Angle conversion
    static Vector3 VectorAngles(const Vector3& forward) {
        float pitch = atan2f(-forward.z, forward.Length2D()) * RAD2DEG;
        float yaw = atan2f(forward.y, forward.x) * RAD2DEG;
        return Vector3(pitch, yaw, 0.0f);
    }
    
    static QAngle CalcAngle(const Vector3& from, const Vector3& to) {
        Vector3 delta = to - from;
        return VectorAngles(delta);
    }
    
    // Interpolation
    static float Lerp(float a, float b, float t) {
        return a + t * (b - a);
    }
    
    static Vector3 LerpVector(const Vector3& a, const Vector3& b, float t) {
        return Vector3(
            Lerp(a.x, b.x, t),
            Lerp(a.y, b.y, t),
            Lerp(a.z, b.z, t)
        );
    }
    
    // Clamping
    static float Clamp(float value, float min, float max) {
        if (value < min) return min;
        if (value > max) return max;
        return value;
    }
    
    static void ClampAngles(QAngle& angles) {
        angles.x = Clamp(angles.x, -89.0f, 89.0f);
        angles.y = Clamp(angles.y, -180.0f, 180.0f);
        angles.z = 0.0f;
    }
    
    // Random
    static float RandomFloat(float min, float max) {
        static std::random_device rd;
        static std::mt19937 gen(rd());
        std::uniform_real_distribution<float> dis(min, max);
        return dis(gen);
    }
    
    static Vector3 RandomVector(float min, float max) {
        return Vector3(
            RandomFloat(min, max),
            RandomFloat(min, max),
            RandomFloat(min, max)
        );
    }
    
    // Conversion
    static float DegToRad(float degrees) { return degrees * DEG2RAD; }
    static float RadToDeg(float radians) { return radians * RAD2DEG; }

    // Angle to vector conversion
    static Vector3 AngleToVector(const QAngle& angles) {
        float pitch = DegToRad(angles.x);
        float yaw = DegToRad(angles.y);

        float cosPitch = cosf(pitch);
        float sinPitch = sinf(pitch);
        float cosYaw = cosf(yaw);
        float sinYaw = sinf(yaw);

        return Vector3(
            cosPitch * cosYaw,
            cosPitch * sinYaw,
            -sinPitch
        );
    }
};

// World to screen projection
class WorldToScreen {
public:
    static bool Initialize() { return true; }
    static bool Project(const Vector3& world, Vector2& screen) { return false; }
    static Vector2 GetScreenSize() { return Vector2(1920, 1080); }
    static bool IsOnScreen(const Vector2& screen) { return true; }
};

// Aimbot utilities
class AimUtils {
public:
    static float GetFOV(const QAngle& viewAngles, const QAngle& aimAngles) {
        QAngle delta = QAngle(
            MathUtils::AngleDifference(viewAngles.x, aimAngles.x),
            MathUtils::AngleDifference(viewAngles.y, aimAngles.y),
            0.0f
        );
        return sqrtf(delta.x * delta.x + delta.y * delta.y);
    }
    
    static QAngle SmoothAim(const QAngle& current, const QAngle& target, float smoothing) {
        if (smoothing <= 0.0f) return target;
        
        QAngle delta = QAngle(
            MathUtils::AngleDifference(target.x, current.x),
            MathUtils::AngleDifference(target.y, current.y),
            0.0f
        );
        
        float factor = 1.0f / smoothing;
        return QAngle(
            current.x + delta.x * factor,
            current.y + delta.y * factor,
            0.0f
        );
    }
    
    static Vector3 PredictPosition(const Vector3& position, const Vector3& velocity, float time) {
        return position + velocity * time;
    }
    
    static Vector3 GetHitboxPosition(C_CSPlayerPawn* player, int hitbox) {
        // Placeholder implementation
        return Vector3();
    }
    
    static int GetBestHitbox(C_CSPlayerPawn* player, const std::vector<Vector3>& hitboxes) {
        // Placeholder implementation
        return 0;
    }

    static bool IsVisible(const Vector3& from, const Vector3& to) {
        // Simplified visibility check - always return true for now
        return true;
    }

    static float GetFOV(const QAngle& viewAngles, const Vector3& from, const Vector3& to) {
        Vector3 direction = (to - from).Normalized();
        Vector3 forward = MathUtils::AngleToVector(viewAngles);
        float dot = forward.Dot(direction);
        return MathUtils::RadToDeg(acos(dot));
    }
};

// Trigonometry namespace for advanced math
namespace Trig {
    inline float Sin(float angle) { return sinf(angle); }
    inline float Cos(float angle) { return cosf(angle); }
    inline float Tan(float angle) { return tanf(angle); }
    inline float ASin(float value) { return asinf(value); }
    inline float ACos(float value) { return acosf(value); }
    inline float ATan(float value) { return atanf(value); }
    inline float ATan2(float y, float x) { return atan2f(y, x); }
    inline float Sqrt(float value) { return sqrtf(value); }
    inline float Pow(float base, float exp) { return powf(base, exp); }
    inline float Abs(float value) { return fabsf(value); }
    inline float Floor(float value) { return floorf(value); }
    inline float Ceil(float value) { return ceilf(value); }
    inline float Round(float value) { return roundf(value); }
    inline float Mod(float a, float b) { return fmodf(a, b); }
}

// Matrix4x4 for transformations
struct Matrix4x4 {
    float m[4][4];
    
    Matrix4x4() {
        memset(m, 0, sizeof(m));
        m[0][0] = m[1][1] = m[2][2] = m[3][3] = 1.0f;
    }
    
    Vector3 Transform(const Vector3& point) const {
        return Vector3(
            point.x * m[0][0] + point.y * m[0][1] + point.z * m[0][2] + m[0][3],
            point.x * m[1][0] + point.y * m[1][1] + point.z * m[1][2] + m[1][3],
            point.x * m[2][0] + point.y * m[2][1] + point.z * m[2][2] + m[2][3]
        );
    }
};

// Geometry utilities
namespace Geometry {
    struct Ray {
        Vector3 origin;
        Vector3 direction;
        
        Ray(const Vector3& o, const Vector3& d) : origin(o), direction(d.Normalized()) {}
    };
    
    struct Plane {
        Vector3 normal;
        float distance;
        
        Plane(const Vector3& n, float d) : normal(n.Normalized()), distance(d) {}
    };
    
    inline bool RayPlaneIntersect(const Ray& ray, const Plane& plane, Vector3& intersection) {
        float denom = ray.direction.Dot(plane.normal);
        if (fabsf(denom) < 0.0001f) return false;
        
        float t = (plane.distance - ray.origin.Dot(plane.normal)) / denom;
        if (t < 0) return false;
        
        intersection = ray.origin + ray.direction * t;
        return true;
    }
}
