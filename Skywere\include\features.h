#pragma once

// Feature base class
class Feature {
public:
    Feature(const std::string& name) : name(name), enabled(false) {}
    virtual ~Feature() = default;
    
    virtual void Update() = 0;
    virtual void Render() {}
    virtual void Reset() {}
    
    bool IsEnabled() const { return enabled; }
    void SetEnabled(bool state) { enabled = state; }
    const std::string& GetName() const { return name; }
    
protected:
    std::string name;
    bool enabled;
};

// Aimbot feature
class Aimbot : public Feature {
public:
    struct Config {
        bool enabled = false;
        float fov = 5.0f;
        float smoothness = 1.0f;
        int hitbox = BONE_HEAD;
        bool autoShoot = false;
        bool rcs = true;
        bool prediction = true;
        bool visibilityCheck = true;
        bool teamCheck = true;
        int key = VK_LBUTTON;
        bool keyMode = false; // false = hold, true = toggle
    } config;
    
    Aimbot();
    void Update() override;
    void Render() override;
    
private:
    C_CSPlayerPawn* GetBestTarget();
    Vector3 GetAimPosition(C_CSPlayerPawn* target);
    QAngle GetRecoilCompensation();
    bool ShouldAim();
    void DoAim(const QAngle& targetAngles);
    
    C_CSPlayerPawn* currentTarget;
    QAngle lastAngles;
    bool isAiming;
    std::chrono::steady_clock::time_point lastShotTime;

    static bool CanShoot();
};

// ESP (Wallhack) feature
class ESP : public Feature {
public:
    struct Config {
        bool enabled = false;
        bool players = true;
        bool weapons = false;
        bool bombs = true;
        bool boxes = true;
        bool names = true;
        bool health = true;
        bool armor = true;
        bool distance = true;
        bool skeleton = false;
        bool snaplines = false;
        bool visibilityCheck = false;
        bool teamCheck = true;
        
        // Colors
        ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 1.0f);
        ImVec4 teamColor = ImVec4(0.0f, 1.0f, 0.0f, 1.0f);
        ImVec4 weaponColor = ImVec4(1.0f, 1.0f, 0.0f, 1.0f);
        ImVec4 bombColor = ImVec4(1.0f, 0.5f, 0.0f, 1.0f);
        
        // Sizes
        float boxThickness = 1.0f;
        float skeletonThickness = 1.0f;
        float snaplineThickness = 1.0f;
        float fontSize = 12.0f;
    } config;
    
    ESP();
    void Update() override;
    void Render() override;
    
private:
    void RenderPlayer(C_CSPlayerPawn* player);
    void RenderPlayerBox(C_CSPlayerPawn* player, const Vector2& head, const Vector2& feet);
    void RenderPlayerInfo(C_CSPlayerPawn* player, const Vector2& head, const Vector2& feet);
    void RenderPlayerSkeleton(C_CSPlayerPawn* player);
    void RenderSnapline(const Vector2& screen);
    void RenderWeapons();
    void RenderBombs();
    
    ImVec4 GetPlayerColor(C_CSPlayerPawn* player);
    bool ShouldRenderPlayer(C_CSPlayerPawn* player);
};

// Triggerbot feature
class Triggerbot : public Feature {
public:
    struct Config {
        bool enabled = false;
        float delay = 0.0f;
        bool visibilityCheck = true;
        bool teamCheck = true;
        int key = VK_LMENU;
        bool keyMode = false; // false = hold, true = toggle
        bool autoWall = false;
        float minDamage = 1.0f;
    } config;
    
    Triggerbot();
    void Update() override;
    
private:
    bool ShouldShoot();
    C_CSPlayerPawn* GetCrosshairTarget();
    void Shoot();
    
    std::chrono::steady_clock::time_point lastShotTime;
    bool isTriggering;
};

// Misc features
class Misc : public Feature {
public:
    struct Config {
        bool enabled = false;
        bool bunnyHop = false;
        bool autoStrafe = false;
        bool noFlash = false;
        bool noSmoke = false;
        bool radarHack = false;
        bool spectatorList = true;
        bool watermark = true;
        bool fpsCounter = true;
        
        // Movement
        int bhopKey = VK_SPACE;
        float strafeSmooth = 1.0f;
        
        // Visual
        float flashAlpha = 0.0f;
        bool removeSmokeEffects = false;
    } config;
    
    Misc();
    void Update() override;
    void Render() override;
    
private:
    void UpdateBunnyHop();
    void UpdateAutoStrafe();
    void UpdateNoFlash();
    void UpdateNoSmoke();
    void UpdateRadarHack();
    void RenderSpectatorList();
    void RenderWatermark();
    void RenderFPSCounter();
    
    bool wasOnGround;
    std::vector<std::string> spectators;
    float currentFPS;
    std::chrono::steady_clock::time_point lastFPSUpdate;
    int frameCount;
};

// Glow feature
class Glow : public Feature {
public:
    struct Config {
        bool enabled = false;
        bool players = true;
        bool weapons = false;
        bool bombs = true;
        bool teamCheck = true;
        
        // Colors
        ImVec4 enemyColor = ImVec4(1.0f, 0.0f, 0.0f, 0.8f);
        ImVec4 teamColor = ImVec4(0.0f, 1.0f, 0.0f, 0.8f);
        ImVec4 weaponColor = ImVec4(1.0f, 1.0f, 0.0f, 0.8f);
        ImVec4 bombColor = ImVec4(1.0f, 0.5f, 0.0f, 0.8f);
        
        // Settings
        float brightness = 1.0f;
        int style = 0; // 0 = outline, 1 = full, 2 = pulse
    } config;
    
    Glow();
    void Update() override;
    
private:
    void ApplyGlow(C_BaseEntity* entity, const ImVec4& color);
    void RemoveGlow(C_BaseEntity* entity);
    ImVec4 GetEntityColor(C_BaseEntity* entity);
    bool ShouldGlow(C_BaseEntity* entity);
};

// Feature manager
class FeatureManager {
public:
    static bool Initialize();
    static void Shutdown();
    static void Update();
    static void Render();
    static void Reset();
    
    // Feature access
    static Aimbot* GetAimbot() { return aimbot.get(); }
    static ESP* GetESP() { return esp.get(); }
    static Triggerbot* GetTriggerbot() { return triggerbot.get(); }
    static Misc* GetMisc() { return misc.get(); }
    static Glow* GetGlow() { return glow.get(); }
    
    // Global settings
    static bool panicMode;
    static int panicKey;
    
private:
    static std::unique_ptr<Aimbot> aimbot;
    static std::unique_ptr<ESP> esp;
    static std::unique_ptr<Triggerbot> triggerbot;
    static std::unique_ptr<Misc> misc;
    static std::unique_ptr<Glow> glow;
    
    static void CheckPanicMode();
};

// Input handling
class Input {
public:
    static bool Initialize();
    static void Update();
    
    static bool IsKeyDown(int key);
    static bool IsKeyPressed(int key);
    static bool IsKeyReleased(int key);
    static bool IsKeyToggled(int key);
    
private:
    static std::array<bool, 256> keyStates;
    static std::array<bool, 256> prevKeyStates;
    static std::array<bool, 256> toggleStates;
    
    static void UpdateKeyStates();
};
