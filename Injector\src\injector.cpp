#include "pch.h"
#include "injector.h"
#include "manual_map.h"
#include "process.h"
#include "utils.h"

// Static member definitions
bool Injector::initialized = false;
HMODULE Injector::ntdllModule = nullptr;
InjectionCallback AdvancedInjector::currentCallback = nullptr;

bool Injector::Initialize() {
    if (initialized) return true;
    
    LOG("Initializing injector...");
    
    // Enable debug privilege
    if (!ProcessManager::EnablePrivilege(L"SeDebugPrivilege")) {
        ERROR("Failed to enable debug privilege");
        return false;
    }
    
    // Load NTDLL
    ntdllModule = GetModuleHandleW(L"ntdll.dll");
    if (!ntdllModule) {
        ERROR("Failed to get NTDLL handle");
        return false;
    }
    
    // Perform environment checks
    if (CheckForDebuggers()) {
        if (g_verbose) LOG("Debugger detected during initialization");
    }
    
    if (CheckVirtualMachine()) {
        if (g_verbose) LOG("Virtual machine detected");
    }
    
    if (CheckSandbox()) {
        if (g_verbose) LOG("Sandbox environment detected");
    }
    
    initialized = true;
    LOG("Injector initialized successfully");
    return true;
}

void Injector::Shutdown() {
    if (!initialized) return;
    
    LOG("Shutting down injector...");
    
    ntdllModule = nullptr;
    initialized = false;
    
    LOG("Injector shutdown complete");
}

bool Injector::InjectDLL(DWORD processId, const std::wstring& dllPath, InjectionMethod method) {
    if (!initialized) {
        ERROR("Injector not initialized");
        return false;
    }
    
    if (!ValidateDLL(dllPath)) {
        ERROR("DLL validation failed");
        return false;
    }
    
    HANDLE hProcess = OpenTargetProcess(processId);
    if (!hProcess) {
        LogError("OpenTargetProcess", GetLastError());
        return false;
    }
    
    if (!CheckCompatibility(hProcess, dllPath)) {
        ERROR("DLL compatibility check failed");
        CloseHandle(hProcess);
        return false;
    }
    
    bool success = false;
    
    // Apply pre-injection delays and randomization
    if (g_config.delayExecution) {
        RandomDelay(g_config.delayMs / 2, g_config.delayMs);
    }
    
    switch (method) {
    case InjectionMethod::LOADLIBRARY:
        success = LoadLibraryInjection(hProcess, dllPath);
        break;
    case InjectionMethod::MANUAL_MAP:
        success = ManualMapInjection(hProcess, dllPath);
        break;
    case InjectionMethod::THREAD_HIJACKING:
        success = ThreadHijackingInjection(hProcess, dllPath);
        break;
    case InjectionMethod::PROCESS_HOLLOWING:
        // Process hollowing requires different parameters
        ERROR("Process hollowing not supported with this method signature");
        success = false;
        break;
    default:
        ERROR("Unknown injection method");
        success = false;
        break;
    }
    
    if (success) {
        // Apply post-injection evasion techniques
        if (g_config.antiDebug) {
            EnableAntiDebug(hProcess);
        }
        
        // Wait for injection to complete
        if (!WaitForInjection(hProcess)) {
            if (g_verbose) LOG("Injection wait timeout (may still be successful)");
        }
    }
    
    CloseHandle(hProcess);
    return success;
}

bool Injector::InjectDLL(const std::wstring& processName, const std::wstring& dllPath, InjectionMethod method) {
    auto processIds = ProcessManager::FindProcessByName(processName);
    if (processIds.empty()) {
        ERROR("Process not found: " + Utils::WStringToString(processName));
        return false;
    }
    
    // Use the first found process
    return InjectDLL(processIds[0], dllPath, method);
}

bool Injector::LoadLibraryInjection(HANDLE hProcess, const std::wstring& dllPath) {
    if (g_verbose) LOG("Using LoadLibrary injection method");
    
    // Get LoadLibraryW address
    HMODULE kernel32 = GetModuleHandleW(L"kernel32.dll");
    if (!kernel32) {
        LogError("GetModuleHandle(kernel32)");
        return false;
    }
    
    LPTHREAD_START_ROUTINE loadLibraryAddr = (LPTHREAD_START_ROUTINE)GetProcAddress(kernel32, "LoadLibraryW");
    if (!loadLibraryAddr) {
        LogError("GetProcAddress(LoadLibraryW)");
        return false;
    }
    
    // Allocate memory for DLL path
    SIZE_T pathSize = (dllPath.length() + 1) * sizeof(wchar_t);
    LPVOID pathAddr = AllocateMemory(hProcess, pathSize, PAGE_READWRITE);
    if (!pathAddr) {
        LogError("AllocateMemory");
        return false;
    }
    
    // Write DLL path to target process
    if (!WriteMemory(hProcess, pathAddr, dllPath.c_str(), pathSize)) {
        LogError("WriteMemory");
        return false;
    }
    
    // Create remote thread
    HANDLE hThread = CreateRemoteThread(hProcess, loadLibraryAddr, pathAddr);
    if (!hThread) {
        LogError("CreateRemoteThread");
        return false;
    }
    
    // Wait for thread completion
    DWORD waitResult = WaitForSingleObject(hThread, INJECTION_TIMEOUT);
    CloseHandle(hThread);
    
    if (waitResult != WAIT_OBJECT_0) {
        ERROR("LoadLibrary injection timeout or failed");
        return false;
    }
    
    if (g_verbose) LOG("LoadLibrary injection completed");
    return true;
}

bool Injector::ManualMapInjection(HANDLE hProcess, const std::wstring& dllPath) {
    if (g_verbose) LOG("Using Manual Map injection method");
    
    return ManualMapper::MapDLL(hProcess, dllPath);
}

bool Injector::ThreadHijackingInjection(HANDLE hProcess, const std::wstring& dllPath) {
    if (g_verbose) LOG("Using Thread Hijacking injection method");
    
    DWORD processId = GetProcessId(hProcess);
    auto threadIds = ProcessManager::GetProcessThreads(processId);
    
    if (threadIds.empty()) {
        ERROR("No threads found in target process");
        return false;
    }
    
    // Use the first thread
    HANDLE hThread = ProcessManager::OpenThread(threadIds[0], THREAD_ALL_ACCESS);
    if (!hThread) {
        LogError("OpenThread");
        return false;
    }
    
    // Suspend thread
    if (SuspendThread(hThread) == -1) {
        LogError("SuspendThread");
        CloseHandle(hThread);
        return false;
    }
    
    // Get thread context
    CONTEXT ctx = {};
    ctx.ContextFlags = CONTEXT_FULL;
    if (!GetThreadContext(hThread, &ctx)) {
        LogError("GetThreadContext");
        ResumeThread(hThread);
        CloseHandle(hThread);
        return false;
    }
    
    // Allocate shellcode
    std::vector<BYTE> shellcode = CreateLoadLibraryShellcode(dllPath);
    LPVOID shellcodeAddr = AllocateMemory(hProcess, shellcode.size());
    if (!shellcodeAddr) {
        LogError("AllocateMemory for shellcode");
        ResumeThread(hThread);
        CloseHandle(hThread);
        return false;
    }
    
    // Write shellcode
    if (!WriteMemory(hProcess, shellcodeAddr, shellcode.data(), shellcode.size())) {
        LogError("WriteMemory shellcode");
        ResumeThread(hThread);
        CloseHandle(hThread);
        return false;
    }
    
    // Modify thread context to execute shellcode
#ifdef _WIN64
    ctx.Rip = (DWORD64)shellcodeAddr;
#else
    ctx.Eip = (DWORD)shellcodeAddr;
#endif
    
    if (!SetThreadContext(hThread, &ctx)) {
        LogError("SetThreadContext");
        ResumeThread(hThread);
        CloseHandle(hThread);
        return false;
    }
    
    // Resume thread
    if (ResumeThread(hThread) == -1) {
        LogError("ResumeThread");
        CloseHandle(hThread);
        return false;
    }
    
    CloseHandle(hThread);
    
    if (g_verbose) LOG("Thread hijacking injection completed");
    return true;
}

bool Injector::ValidateDLL(const std::wstring& dllPath) {
    if (!Utils::FileExists(dllPath)) {
        ERROR("DLL file does not exist: " + Utils::WStringToString(dllPath));
        return false;
    }
    
    // Read and validate PE structure
    auto dllData = Utils::ReadFile(dllPath);
    if (dllData.empty()) {
        ERROR("Failed to read DLL file");
        return false;
    }
    
    // Check DOS header
    if (dllData.size() < sizeof(IMAGE_DOS_HEADER)) {
        ERROR("Invalid DLL: too small");
        return false;
    }
    
    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)dllData.data();
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
        ERROR("Invalid DLL: bad DOS signature");
        return false;
    }
    
    // Check NT headers
    if (dllData.size() < dosHeader->e_lfanew + sizeof(IMAGE_NT_HEADERS)) {
        ERROR("Invalid DLL: bad NT headers offset");
        return false;
    }
    
    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)(dllData.data() + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
        ERROR("Invalid DLL: bad NT signature");
        return false;
    }
    
    // Check if it's a DLL
    if (!(ntHeaders->FileHeader.Characteristics & IMAGE_FILE_DLL)) {
        ERROR("File is not a DLL");
        return false;
    }
    
    // Check architecture
#ifdef _WIN64
    if (ntHeaders->FileHeader.Machine != IMAGE_FILE_MACHINE_AMD64) {
        ERROR("DLL architecture mismatch: expected x64");
        return false;
    }
#else
    if (ntHeaders->FileHeader.Machine != IMAGE_FILE_MACHINE_I386) {
        ERROR("DLL architecture mismatch: expected x86");
        return false;
    }
#endif
    
    if (g_verbose) LOG("DLL validation passed");
    return true;
}

bool Injector::CheckCompatibility(HANDLE hProcess, const std::wstring& dllPath) {
    ProcessArchitecture processArch = ProcessManager::GetProcessArchitecture(hProcess);
    
#ifdef _WIN64
    if (processArch != ProcessArchitecture::X64) {
        ERROR("Process architecture mismatch: injector is x64, target is not");
        return false;
    }
#else
    if (processArch != ProcessArchitecture::X86) {
        ERROR("Process architecture mismatch: injector is x86, target is not");
        return false;
    }
#endif
    
    return true;
}

bool Injector::WaitForInjection(HANDLE hProcess, DWORD timeout) {
    // Simple implementation - wait for a short period
    Utils::Sleep(1000);
    return true;
}

HANDLE Injector::OpenTargetProcess(DWORD processId) {
    HANDLE hProcess = OpenProcess(PROCESS_ALL_ACCESS, FALSE, processId);
    if (!hProcess) {
        // Try with limited access
        hProcess = OpenProcess(PROCESS_VM_OPERATION | PROCESS_VM_WRITE | PROCESS_VM_READ |
                              PROCESS_CREATE_THREAD | PROCESS_QUERY_INFORMATION, FALSE, processId);
    }
    return hProcess;
}

LPVOID Injector::AllocateMemory(HANDLE hProcess, SIZE_T size, DWORD protection) {
    if (g_config.useNtApi) {
        LPVOID baseAddress = nullptr;
        SIZE_T regionSize = size;

        NTSTATUS status = NtAllocateVirtualMemory(hProcess, &baseAddress, 0, &regionSize,
                                                 MEM_COMMIT | MEM_RESERVE, protection);

        if (status == 0) {
            return baseAddress;
        }
    }

    return VirtualAllocEx(hProcess, nullptr, size, MEM_COMMIT | MEM_RESERVE, protection);
}

bool Injector::WriteMemory(HANDLE hProcess, LPVOID address, LPCVOID data, SIZE_T size) {
    if (g_config.useNtApi) {
        SIZE_T bytesWritten = 0;
        NTSTATUS status = NtWriteVirtualMemory(hProcess, address, (PVOID)data, size, &bytesWritten);
        return status == 0 && bytesWritten == size;
    }

    SIZE_T bytesWritten = 0;
    return WriteProcessMemory(hProcess, address, data, size, &bytesWritten) && bytesWritten == size;
}

bool Injector::ProtectMemory(HANDLE hProcess, LPVOID address, SIZE_T size, DWORD newProtection, DWORD* oldProtection) {
    if (g_config.useNtApi) {
        LPVOID baseAddress = address;
        SIZE_T regionSize = size;
        ULONG oldProtect = 0;

        NTSTATUS status = NtProtectVirtualMemory(hProcess, &baseAddress, &regionSize, newProtection, &oldProtect);

        if (oldProtection) {
            *oldProtection = oldProtect;
        }

        return status == 0;
    }

    return VirtualProtectEx(hProcess, address, size, newProtection, oldProtection);
}

HANDLE Injector::CreateRemoteThread(HANDLE hProcess, LPTHREAD_START_ROUTINE startAddress, LPVOID parameter) {
    if (g_config.useNtApi) {
        HANDLE hThread = nullptr;

        NTSTATUS status = NtCreateThreadEx(&hThread, THREAD_ALL_ACCESS, nullptr, hProcess,
                                          startAddress, parameter, 0, 0, 0, 0, nullptr);

        if (status == 0) {
            return hThread;
        }
    }

    return ::CreateRemoteThread(hProcess, nullptr, 0, startAddress, parameter, 0, nullptr);
}

std::vector<BYTE> Injector::CreateLoadLibraryShellcode(const std::wstring& dllPath) {
    std::vector<BYTE> shellcode;

    // This is a simplified shellcode generator
    // In a real implementation, you'd create proper x64/x86 shellcode

#ifdef _WIN64
    // x64 shellcode template for LoadLibraryW
    BYTE template_code[] = {
        0x48, 0x83, 0xEC, 0x28,                     // sub rsp, 28h
        0x48, 0xB9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rcx, dllPath
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, LoadLibraryW
        0xFF, 0xD0,                                 // call rax
        0x48, 0x83, 0xC4, 0x28,                     // add rsp, 28h
        0xC3                                        // ret
    };
#else
    // x86 shellcode template for LoadLibraryW
    BYTE template_code[] = {
        0x68, 0x00, 0x00, 0x00, 0x00,               // push dllPath
        0xB8, 0x00, 0x00, 0x00, 0x00,               // mov eax, LoadLibraryW
        0xFF, 0xD0,                                 // call eax
        0xC3                                        // ret
    };
#endif

    shellcode.assign(template_code, template_code + sizeof(template_code));
    return shellcode;
}

bool Injector::RandomizeHeaders(LPVOID imageBase, SIZE_T imageSize) {
    if (!g_config.randomizeHeaders) return true;

    // Randomize DOS header
    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)imageBase;
    auto randomBytes = Utils::GenerateRandomBytes(sizeof(IMAGE_DOS_HEADER) - 4);

    // Keep magic and e_lfanew, randomize the rest
    memcpy((BYTE*)dosHeader + 2, randomBytes.data(), randomBytes.size());

    return true;
}

bool Injector::EraseHeaders(HANDLE hProcess, LPVOID imageBase) {
    if (!g_config.eraseHeaders) return true;

    // Zero out DOS header and NT headers
    SIZE_T headerSize = 0x1000; // First page
    std::vector<BYTE> zeroBuffer(headerSize, 0);

    return WriteMemory(hProcess, imageBase, zeroBuffer.data(), headerSize);
}

bool Injector::HideFromPEB(HANDLE hProcess, LPVOID imageBase) {
    if (!g_config.hideFromPEB) return true;

    // This would require complex PEB manipulation
    // Simplified implementation for now
    return true;
}

bool Injector::ScrambleImports(HANDLE hProcess, LPVOID imageBase) {
    if (!g_config.scrambleImports) return true;

    // This would require IAT manipulation
    // Simplified implementation for now
    return true;
}

bool Injector::EnableAntiDebug(HANDLE hProcess) {
    if (!g_config.antiDebug) return true;

    // Set debug flags in PEB
    // This is a simplified implementation
    return true;
}

bool Injector::CheckForDebuggers() {
    return IsDebuggerPresent() || Utils::IsBeingDebugged();
}

bool Injector::IsProcessBeingDebugged(HANDLE hProcess) {
    BOOL isDebugged = FALSE;
    CheckRemoteDebuggerPresent(hProcess, &isDebugged);
    return isDebugged;
}

bool Injector::RandomDelay(DWORD minMs, DWORD maxMs) {
    if (minMs >= maxMs) return false;

    DWORD delay = minMs + (Utils::GenerateRandomDWORD() % (maxMs - minMs));
    Utils::Sleep(delay);
    return true;
}

bool Injector::CheckVirtualMachine() {
    return Utils::IsRunningInVM();
}

bool Injector::CheckSandbox() {
    return Utils::IsRunningInSandbox();
}

void Injector::SetConfig(const InjectionConfig& config) {
    g_config = config;
}

InjectionConfig Injector::GetConfig() {
    return g_config;
}

void Injector::SetVerbose(bool verbose) {
    g_verbose = verbose;
}

void Injector::LogError(const std::string& function, DWORD errorCode) {
    std::string errorMsg = function + " failed with error: " + std::to_string(errorCode) +
                          " (" + Utils::GetErrorString(errorCode) + ")";
    ERROR(errorMsg);
}

std::string Injector::GetErrorString(DWORD errorCode) {
    return Utils::GetErrorString(errorCode);
}
