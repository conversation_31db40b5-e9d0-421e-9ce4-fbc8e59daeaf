#include "pch.h"

// DirectX Hook Variables
typedef HRESULT(__stdcall* Present_t)(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
Present_t oPresent = nullptr;

// D3D11 Objects
ID3D11Device* g_pd3dDevice = nullptr;
ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
IDXGISwapChain* g_pSwapChain = nullptr;
ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;
HWND g_hWnd = nullptr;

// ImGui State
bool g_bImGuiInitialized = false;
bool g_bHooksInstalled = false;

// Hook structure
struct Hook {
    void* target;
    void* detour;
    BYTE original[14];
    bool installed;
};

// Forward declaration for ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Simple hook using Microsoft Detours-like approach
bool InstallHook(Hook* hook) {
    if (hook->installed) return true;

    DWORD oldProtect;
    if (!VirtualProtect(hook->target, 14, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        return false;
    }

    // Save original bytes
    memcpy(hook->original, hook->target, 14);

    // Create absolute jump (x64)
    BYTE jmp[14] = {
        0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, // mov rax, address
        0xFF, 0xE0,                                                 // jmp rax
        0x90, 0x90                                                  // nop nop
    };
    *(uintptr_t*)(jmp + 2) = (uintptr_t)hook->detour;

    // Install hook
    memcpy(hook->target, jmp, 14);

    VirtualProtect(hook->target, 14, oldProtect, &oldProtect);
    hook->installed = true;
    return true;
}

bool RemoveHook(Hook* hook) {
    if (!hook->installed) return true;

    DWORD oldProtect;
    if (!VirtualProtect(hook->target, 14, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        return false;
    }

    // Restore original bytes
    memcpy(hook->target, hook->original, 14);

    VirtualProtect(hook->target, 14, oldProtect, &oldProtect);
    hook->installed = false;
    return true;
}

// Window procedure hook for input
WNDPROC oWndProc = nullptr;
LRESULT CALLBACK hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (g_bImGuiInitialized) {
        // Handle ImGui input first
        if (ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam)) {
            return true;
        }

        // Handle our hotkeys
        if (uMsg == WM_KEYDOWN || uMsg == WM_SYSKEYDOWN) {
            if (wParam == VK_INSERT) {
                bool currentState = g_bShowMenu.load();
                g_bShowMenu.store(!currentState);
                LOG(currentState ? "Menu hidden" : "Menu shown");

                // Show feedback in a separate thread to avoid blocking
                CreateThread(nullptr, 0, [](LPVOID param) -> DWORD {
                    bool menuState = *(bool*)param;
                    if (menuState) {
                        MessageBoxA(nullptr,
                            "Skywere Menu Opened!\n\n"
                            "Use mouse to navigate the menu.\n"
                            "Press INSERT again to close.",
                            "Skywere Menu",
                            MB_OK | MB_TOPMOST | MB_ICONINFORMATION);
                    }
                    return 0;
                }, &currentState, 0, nullptr);

                return 0;
            }
            else if (wParam == VK_END) {
                LOG("Unload key pressed!");
                MessageBoxA(nullptr, "Skywere CS2 Cheat Unloading...", "Skywere", MB_OK | MB_TOPMOST);
                g_bRunning = false;
                return 0;
            }
        }

        // Block input to game when menu is open
        if (g_bShowMenu.load() && (ImGui::GetIO().WantCaptureMouse || ImGui::GetIO().WantCaptureKeyboard)) {
            return 0;
        }
    }

    return CallWindowProc(oWndProc, hWnd, uMsg, wParam, lParam);
}

// Present Hook - This is where we render our overlay
HRESULT __stdcall hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags) {
    static bool init = false;
    static bool showedMessage = false;

    if (!init) {
        LOG("Present hook called! Initializing...");

        // Get device and context
        if (SUCCEEDED(pSwapChain->GetDevice(__uuidof(ID3D11Device), (void**)&g_pd3dDevice))) {
            g_pd3dDevice->GetImmediateContext(&g_pd3dDeviceContext);
            g_pSwapChain = pSwapChain;

            // Get window handle
            DXGI_SWAP_CHAIN_DESC sd;
            pSwapChain->GetDesc(&sd);
            g_hWnd = sd.OutputWindow;

            LOG("Game window found: 0x%p", g_hWnd);

            // Initialize ImGui
            ImGui::CreateContext();
            ImGuiIO& io = ImGui::GetIO();
            io.ConfigFlags |= ImGuiConfigFlags_NoMouseCursorChange;
            io.IniFilename = nullptr; // Disable imgui.ini

            // Setup ImGui style
            ImGui::StyleColorsDark();

            // Initialize ImGui backends
            if (ImGui_ImplWin32_Init(g_hWnd) && ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext)) {
                LOG("ImGui initialized successfully");

                // Hook window procedure
                oWndProc = (WNDPROC)SetWindowLongPtr(g_hWnd, GWLP_WNDPROC, (LONG_PTR)hkWndProc);
                if (oWndProc) {
                    LOG("Window procedure hooked successfully");
                    g_bImGuiInitialized = true;
                    init = true;

                    LOG("DirectX hook initialized successfully!");
                } else {
                    LOG("Failed to hook window procedure");
                }
            } else {
                LOG("Failed to initialize ImGui backends");
            }
        } else {
            LOG("Failed to get D3D11 device from swap chain");
        }
    }

    // Show feedback message once
    if (init && !showedMessage) {
        showedMessage = true;
        // Use a separate thread for the message box to avoid blocking
        CreateThread(nullptr, 0, [](LPVOID) -> DWORD {
            MessageBoxA(nullptr,
                "Skywere CS2 Cheat Successfully Loaded!\n\n"
                "Controls:\n"
                "INSERT - Toggle Menu\n"
                "END - Exit Cheat\n\n"
                "The cheat is now active!",
                "Skywere CS2 - Ready!",
                MB_OK | MB_TOPMOST | MB_ICONINFORMATION);
            return 0;
        }, nullptr, 0, nullptr);
    }

    if (init && g_bImGuiInitialized) {
        // Create render target if needed
        if (!g_mainRenderTargetView) {
            ID3D11Texture2D* pBackBuffer;
            pSwapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (LPVOID*)&pBackBuffer);
            if (pBackBuffer) {
                g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
                pBackBuffer->Release();
                LOG("Render target view created");
            }
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update and render features
        FeatureManager::Update();
        FeatureManager::Render();

        // Always show a small indicator that the cheat is loaded
        ImGui::SetNextWindowPos(ImVec2(10, 10), ImGuiCond_Always);
        ImGui::SetNextWindowSize(ImVec2(200, 50), ImGuiCond_Always);
        ImGui::Begin("Skywere Status", nullptr,
            ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoResize |
            ImGuiWindowFlags_NoMove | ImGuiWindowFlags_NoCollapse |
            ImGuiWindowFlags_AlwaysAutoResize);

        ImGui::Text("Skywere CS2 Active");
        ImGui::Text("Press INSERT for menu");
        ImGui::End();

        // Render main menu
        if (g_bShowMenu.load()) {
            ImGui::SetNextWindowSize(ImVec2(650, 550), ImGuiCond_FirstUseEver);
            ImGui::SetNextWindowPos(ImVec2(100, 100), ImGuiCond_FirstUseEver);

            ImGui::Begin("Skywere CS2 Cheat - Main Menu", nullptr, ImGuiWindowFlags_NoCollapse);

            ImGui::Text("Skywere CS2 Internal Cheat v1.0");
            ImGui::Separator();

            if (ImGui::CollapsingHeader("Aimbot", ImGuiTreeNodeFlags_DefaultOpen)) {
                static bool aimbotEnabled = false;
                static float aimbotFov = 5.0f;
                static float aimbotSmooth = 1.0f;
                static int aimbotKey = 1; // Left mouse button

                ImGui::Checkbox("Enable Aimbot", &aimbotEnabled);
                ImGui::SliderFloat("FOV", &aimbotFov, 1.0f, 20.0f, "%.1f");
                ImGui::SliderFloat("Smooth", &aimbotSmooth, 0.1f, 10.0f, "%.1f");
                ImGui::SliderInt("Aim Key", &aimbotKey, 1, 5);
                ImGui::Text("1=LMB, 2=RMB, 3=MMB, 4=X1, 5=X2");
            }

            if (ImGui::CollapsingHeader("ESP", ImGuiTreeNodeFlags_DefaultOpen)) {
                static bool espEnabled = false;
                static bool espBox = false;
                static bool espName = false;
                static bool espHealth = false;
                static bool espDistance = false;

                ImGui::Checkbox("Enable ESP", &espEnabled);
                ImGui::Checkbox("Box ESP", &espBox);
                ImGui::Checkbox("Name ESP", &espName);
                ImGui::Checkbox("Health ESP", &espHealth);
                ImGui::Checkbox("Distance ESP", &espDistance);
            }

            if (ImGui::CollapsingHeader("Triggerbot", ImGuiTreeNodeFlags_DefaultOpen)) {
                static bool triggerbotEnabled = false;
                static float triggerbotDelay = 50.0f;
                static int triggerbotKey = 1;

                ImGui::Checkbox("Enable Triggerbot", &triggerbotEnabled);
                ImGui::SliderFloat("Delay (ms)", &triggerbotDelay, 0.0f, 500.0f, "%.0f");
                ImGui::SliderInt("Trigger Key", &triggerbotKey, 1, 5);
            }

            if (ImGui::CollapsingHeader("Misc", ImGuiTreeNodeFlags_DefaultOpen)) {
                static bool bhopEnabled = false;
                static bool noFlash = false;
                static bool noSmoke = false;
                static bool noRecoil = false;

                ImGui::Checkbox("Bunny Hop", &bhopEnabled);
                ImGui::Checkbox("No Flash", &noFlash);
                ImGui::Checkbox("No Smoke", &noSmoke);
                ImGui::Checkbox("No Recoil", &noRecoil);
            }

            ImGui::Separator();
            ImGui::Text("Controls:");
            ImGui::Text("INSERT - Toggle this menu");
            ImGui::Text("END - Exit cheat");

            ImGui::End();
        }

        // Render ImGui
        ImGui::Render();
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
    }

    return oPresent(pSwapChain, SyncInterval, Flags);
}

// Find DirectX functions using a more robust method
bool FindDirectXFunctions() {
    LOG("Finding DirectX functions...");

    // Try to find an existing window first
    HWND tempWindow = CreateWindowA("BUTTON", "TempWindow", WS_SYSMENU | WS_MINIMIZEBOX,
                                   CW_USEDEFAULT, CW_USEDEFAULT, 300, 300,
                                   NULL, NULL, GetModuleHandle(NULL), NULL);

    if (!tempWindow) {
        LOG("Failed to create temporary window");
        return false;
    }

    // Create temporary D3D11 device to get function addresses
    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 800;
    sd.BufferDesc.Height = 600;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = tempWindow;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

    ID3D11Device* pDevice = nullptr;
    ID3D11DeviceContext* pContext = nullptr;
    IDXGISwapChain* pSwapChain = nullptr;

    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        createDeviceFlags,
        featureLevelArray,
        2,
        D3D11_SDK_VERSION,
        &sd,
        &pSwapChain,
        &pDevice,
        &featureLevel,
        &pContext
    );

    if (FAILED(hr)) {
        LOG("Failed to create temporary D3D11 device");
        DestroyWindow(tempWindow);
        return false;
    }

    // Get Present function address from VTable
    void** pSwapChainVTable = *(void***)pSwapChain;
    oPresent = (Present_t)pSwapChainVTable[8]; // Present is at index 8

    LOG("Present function found at: 0x%p", oPresent);

    // Cleanup temporary objects
    pSwapChain->Release();
    pDevice->Release();
    pContext->Release();
    DestroyWindow(tempWindow);

    LOG("DirectX functions found successfully");
    return true;
}

// Install DirectX hooks
bool InstallDirectXHooks() {
    LOG("Installing DirectX hooks...");

    // Find DirectX functions
    if (!FindDirectXFunctions()) {
        LOG("Failed to find DirectX functions");
        return false;
    }

    // Create hook for Present function
    static Hook presentHook = {};
    presentHook.target = (void*)oPresent;
    presentHook.detour = (void*)hkPresent;

    if (!InstallHook(&presentHook)) {
        LOG("Failed to install Present hook");
        return false;
    }

    g_bHooksInstalled = true;
    LOG("DirectX hooks installed successfully!");
    return true;
}

// Main thread function
DWORD WINAPI MainThread(LPVOID lpParam) {
    LOG("Skywere CS2 Cheat - Initializing...");

    // Wait for game to load
    while (!GetModuleHandle(CLIENT_DLL)) {
        Sleep(100);
    }

    LOG("Game modules detected, initializing systems...");

    // Initialize memory system
    if (!Memory::Initialize()) {
        LOG("Failed to initialize memory system!");
        return 1;
    }

    // Initialize entity system
    if (!EntityManager::Initialize()) {
        LOG("Failed to initialize entity system!");
        return 1;
    }

    // Initialize features
    if (!FeatureManager::Initialize()) {
        LOG("Failed to initialize features!");
        return 1;
    }

    // Initialize config system
    if (!Config::Initialize()) {
        LOG("Failed to initialize config system!");
        return 1;
    }

    // Load default config
    Config::Load("default");

    // Initialize input system
    if (!Input::Initialize()) {
        LOG("Failed to initialize input system!");
        return 1;
    }

    LOG("Skywere initialized successfully!");

    // Install DirectX hooks
    if (!InstallDirectXHooks()) {
        LOG("Failed to install DirectX hooks!");
        return 1;
    }

    LOG("DirectX hooks installed successfully!");

    // Main loop
    while (g_bRunning) {
        // Update input
        Input::Update();

        // Update features (ESP, Aimbot, etc.)
        FeatureManager::Update();

        // Update config auto-save
        Config::UpdateAutoSave();

        // Small sleep to prevent high CPU usage
        Sleep(1);
    }
    
    LOG("Shutting down Skywere...");
    Cleanup();
    
    // Unload DLL
    FreeLibraryAndExitThread(g_hModule, 0);
    return 0;
}

void Cleanup() {
    LOG("Starting cleanup...");

    // Shutdown systems
    FeatureManager::Shutdown();
    Config::Shutdown();
    Memory::Shutdown();

    // Cleanup ImGui
    if (g_bImGuiInitialized) {
        ImGui_ImplDX11_Shutdown();
        ImGui_ImplWin32_Shutdown();
        ImGui::DestroyContext();
        g_bImGuiInitialized = false;
    }

    // Cleanup D3D11 objects
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }

    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = nullptr;
    }

    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = nullptr;
    }

    // Restore window procedure
    if (oWndProc && g_hWnd) {
        SetWindowLongPtr(g_hWnd, GWLP_WNDPROC, (LONG_PTR)oWndProc);
    }

    LOG("Skywere shutdown complete.");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_bRunning = false;
        break;
    }
    return TRUE;
}
