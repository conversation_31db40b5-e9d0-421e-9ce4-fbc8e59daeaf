#include "pch.h"

// DirectX Hook Variables
typedef HRESULT(__stdcall* Present_t)(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
typedef HRESULT(__stdcall* ResizeBuffers_t)(IDXGISwapChain* pSwapChain, UINT BufferCount, UINT Width, UINT Height, DXGI_FORMAT NewFormat, UINT SwapChainFlags);

Present_t oPresent = nullptr;
ResizeBuffers_t oResizeBuffers = nullptr;
WNDPROC oWndProc = nullptr;

// D3D11 Objects
ID3D11Device* g_pd3dDevice = nullptr;
ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
IDXGISwapChain* g_pSwapChain = nullptr;
ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;
HWND g_hWnd = nullptr;

// ImGui State
bool g_bImGuiInitialized = false;
bool g_bHooksInstalled = false;

// Simple hook structure for manual hooking
struct SimpleHook {
    void* target;
    void* detour;
    BYTE original[12];
    bool installed;
};

// Forward declaration for ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Simple hooking functions
bool InstallHook(SimpleHook* hook) {
    if (hook->installed) return true;

    DWORD oldProtect;
    if (!VirtualProtect(hook->target, 12, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        return false;
    }

    // Save original bytes
    memcpy(hook->original, hook->target, 12);

    // Create jump instruction (x64)
    BYTE jmp[12] = { 0x48, 0xB8, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0xE0 };
    *(uintptr_t*)(jmp + 2) = (uintptr_t)hook->detour;

    // Install hook
    memcpy(hook->target, jmp, 12);

    VirtualProtect(hook->target, 12, oldProtect, &oldProtect);
    hook->installed = true;
    return true;
}

bool RemoveHook(SimpleHook* hook) {
    if (!hook->installed) return true;

    DWORD oldProtect;
    if (!VirtualProtect(hook->target, 12, PAGE_EXECUTE_READWRITE, &oldProtect)) {
        return false;
    }

    // Restore original bytes
    memcpy(hook->target, hook->original, 12);

    VirtualProtect(hook->target, 12, oldProtect, &oldProtect);
    hook->installed = false;
    return true;
}

// Window Procedure Hook
LRESULT __stdcall hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (g_bImGuiInitialized) {
        // Handle ImGui input
        ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam);

        // Handle our hotkeys
        if (uMsg == WM_KEYDOWN) {
            if (wParam == VK_INSERT) {
                bool currentState = g_bShowMenu.load();
                g_bShowMenu.store(!currentState);
                LOG(currentState ? "Menu hidden" : "Menu shown");

                // Show a simple message box to confirm the cheat is working
                if (!currentState) {
                    MessageBoxA(nullptr,
                        "Skywere CS2 Cheat Active!\n\n"
                        "Features:\n"
                        "- Aimbot\n"
                        "- ESP\n"
                        "- Triggerbot\n"
                        "- Misc Features\n\n"
                        "Press INSERT again to hide\n"
                        "Press END to exit cheat",
                        "Skywere CS2 - Menu Active",
                        MB_OK | MB_TOPMOST);
                }
                return 0;
            }
            else if (wParam == VK_END) {
                LOG("Unload key pressed!");
                MessageBoxA(nullptr, "Skywere CS2 Cheat Unloading...", "Skywere CS2", MB_OK | MB_TOPMOST);
                g_bRunning = false;
                return 0;
            }
        }

        // Block input to game when menu is open
        if (g_bShowMenu.load() && (ImGui::GetIO().WantCaptureMouse || ImGui::GetIO().WantCaptureKeyboard)) {
            return 0;
        }
    }

    return CallWindowProc(oWndProc, hWnd, uMsg, wParam, lParam);
}

// Present Hook - This is where we render our overlay
HRESULT __stdcall hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags) {
    static bool init = false;

    if (!init) {
        LOG("Initializing DirectX hook...");

        // Get device and context
        if (SUCCEEDED(pSwapChain->GetDevice(__uuidof(ID3D11Device), (void**)&g_pd3dDevice))) {
            g_pd3dDevice->GetImmediateContext(&g_pd3dDeviceContext);
            g_pSwapChain = pSwapChain;

            // Get window handle
            DXGI_SWAP_CHAIN_DESC sd;
            pSwapChain->GetDesc(&sd);
            g_hWnd = sd.OutputWindow;

            // Initialize ImGui
            ImGui::CreateContext();
            ImGuiIO& io = ImGui::GetIO();
            io.ConfigFlags |= ImGuiConfigFlags_NoMouseCursorChange;
            io.IniFilename = nullptr; // Disable imgui.ini

            // Setup ImGui style
            ImGui::StyleColorsDark();

            // Initialize ImGui backends
            ImGui_ImplWin32_Init(g_hWnd);
            ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

            // Hook window procedure
            oWndProc = (WNDPROC)SetWindowLongPtr(g_hWnd, GWLP_WNDPROC, (LONG_PTR)hkWndProc);

            g_bImGuiInitialized = true;
            init = true;

            LOG("DirectX hook initialized successfully!");
        }
    }

    if (init && g_bImGuiInitialized) {
        // Create render target if needed
        if (!g_mainRenderTargetView) {
            ID3D11Texture2D* pBackBuffer;
            pSwapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (LPVOID*)&pBackBuffer);
            if (pBackBuffer) {
                g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
                pBackBuffer->Release();
            }
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update and render features
        FeatureManager::Update();
        FeatureManager::Render();

        // Render our GUI
        if (g_bShowMenu.load()) {
            ImGui::SetNextWindowSize(ImVec2(500, 400), ImGuiCond_FirstUseEver);
            ImGui::Begin("Skywere CS2 Cheat", nullptr, ImGuiWindowFlags_NoCollapse);

            ImGui::Text("Skywere CS2 Internal Cheat");
            ImGui::Separator();

            if (ImGui::CollapsingHeader("Aimbot", ImGuiTreeNodeFlags_DefaultOpen)) {
                ImGui::Text("Aimbot settings will be here...");
                static bool aimbotEnabled = false;
                ImGui::Checkbox("Enable Aimbot", &aimbotEnabled);
            }

            if (ImGui::CollapsingHeader("ESP", ImGuiTreeNodeFlags_DefaultOpen)) {
                ImGui::Text("ESP settings will be here...");
                static bool espEnabled = false;
                ImGui::Checkbox("Enable ESP", &espEnabled);
            }

            if (ImGui::CollapsingHeader("Triggerbot")) {
                ImGui::Text("Triggerbot settings will be here...");
            }

            if (ImGui::CollapsingHeader("Misc")) {
                ImGui::Text("Misc settings will be here...");
            }

            ImGui::Separator();
            ImGui::Text("Press INSERT to toggle menu");
            ImGui::Text("Press END to unload cheat");

            ImGui::End();
        }

        // Render ImGui
        ImGui::Render();

        // Set render target and render ImGui
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
    }

    return oPresent(pSwapChain, SyncInterval, Flags);
}

// ResizeBuffers Hook - Clean up render target when window is resized
HRESULT __stdcall hkResizeBuffers(IDXGISwapChain* pSwapChain, UINT BufferCount, UINT Width, UINT Height, DXGI_FORMAT NewFormat, UINT SwapChainFlags) {
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }

    return oResizeBuffers(pSwapChain, BufferCount, Width, Height, NewFormat, SwapChainFlags);
}

// Hook installation function
bool InstallDirectXHooks() {
    LOG("Installing DirectX hooks...");

    // Get the game's main window - try multiple methods
    HWND gameWindow = nullptr;

    // Method 1: Try to find CS2 window by class name
    gameWindow = FindWindowA("SDL_app", nullptr);
    if (!gameWindow) {
        // Method 2: Try to find by window title
        gameWindow = FindWindowA(nullptr, "Counter-Strike 2");
    }
    if (!gameWindow) {
        // Method 3: Use foreground window as fallback
        gameWindow = GetForegroundWindow();
    }

    if (!gameWindow) {
        LOG("Failed to find game window");
        return false;
    }

    g_hWnd = gameWindow;
    LOG("Found game window: 0x%p", gameWindow);

    // Hook the window procedure to capture input
    oWndProc = (WNDPROC)SetWindowLongPtrA(gameWindow, GWLP_WNDPROC, (LONG_PTR)hkWndProc);
    if (!oWndProc) {
        LOG("Failed to hook window procedure");
        return false;
    }

    LOG("Window procedure hooked successfully");

    // Initialize ImGui immediately
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO();
    io.ConfigFlags |= ImGuiConfigFlags_NoMouseCursorChange;
    io.IniFilename = nullptr; // Disable imgui.ini

    // Setup ImGui style
    ImGui::StyleColorsDark();

    // Initialize Win32 backend
    if (!ImGui_ImplWin32_Init(gameWindow)) {
        LOG("Failed to initialize ImGui Win32 backend");
        return false;
    }

    LOG("ImGui initialized successfully");
    g_bImGuiInitialized = true;
    g_bHooksInstalled = true;

    LOG("DirectX hooks installed successfully!");
    return true;
}

// Main thread function
DWORD WINAPI MainThread(LPVOID lpParam) {
    LOG("Skywere CS2 Cheat - Initializing...");
    
    // Wait for game to load
    while (!GetModuleHandle(CLIENT_DLL)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG("Game modules detected, initializing systems...");
    
    // Initialize memory system
    if (!Memory::Initialize()) {
        LOG("Failed to initialize memory system!");
        return 1;
    }
    
    // Initialize entity system
    if (!EntityManager::Initialize()) {
        LOG("Failed to initialize entity system!");
        return 1;
    }
    
    // Initialize features
    if (!FeatureManager::Initialize()) {
        LOG("Failed to initialize features!");
        return 1;
    }
    
    // Initialize config system
    if (!Config::Initialize()) {
        LOG("Failed to initialize config system!");
        return 1;
    }
    
    // Load default config
    Config::Load("default");
    
    // Initialize input system
    if (!Input::Initialize()) {
        LOG("Failed to initialize input system!");
        return 1;
    }
    
    LOG("Skywere initialized successfully!");

    // Install DirectX hooks
    if (!InstallDirectXHooks()) {
        LOG("Failed to install DirectX hooks!");
        return 1;
    }

    LOG("DirectX hooks installed successfully!");

    // Main loop
    while (g_bRunning) {
        // Update input
        Input::Update();

        // Update features (ESP, Aimbot, etc.)
        FeatureManager::Update();

        // Update config auto-save
        Config::UpdateAutoSave();

        // Small sleep to prevent high CPU usage
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    LOG("Shutting down Skywere...");
    Cleanup();
    
    // Unload DLL
    FreeLibraryAndExitThread(g_hModule, 0);
    return 0;
}

void Cleanup() {
    LOG("Starting cleanup...");

    // Shutdown systems
    FeatureManager::Shutdown();
    Config::Shutdown();
    Memory::Shutdown();

    // Cleanup ImGui
    if (g_bImGuiInitialized) {
        ImGui_ImplDX11_Shutdown();
        ImGui_ImplWin32_Shutdown();
        ImGui::DestroyContext();
        g_bImGuiInitialized = false;
    }

    // Cleanup D3D11 objects
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }

    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = nullptr;
    }

    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = nullptr;
    }

    // Restore window procedure
    if (oWndProc && g_hWnd) {
        SetWindowLongPtr(g_hWnd, GWLP_WNDPROC, (LONG_PTR)oWndProc);
    }

    LOG("Skywere shutdown complete.");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_bRunning = false;
        break;
    }
    return TRUE;
}
