#include "pch.h"
#include <d3d11.h>
#include <dxgi.h>

// Hook for Present to render our overlay
typedef HRESULT(__stdcall* Present_t)(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags);
Present_t oPresent = nullptr;

// Window procedure hook
WNDPROC oWndProc = nullptr;

// D3D11 objects
ID3D11Device* g_pd3dDevice = nullptr;
ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
IDXGISwapChain* g_pSwapChain = nullptr;
ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

// ImGui initialization flag
bool g_bImGuiInitialized = false;

// Hook functions
HRESULT __stdcall hkPresent(IDXGISwapChain* pSwapChain, UINT SyncInterval, UINT Flags) {
    static bool init = false;

    if (!init) {
        LOG("Initializing D3D11 hook...");

        if (SUCCEEDED(pSwapChain->GetDevice(__uuidof(ID3D11Device), (void**)&g_pd3dDevice))) {
            g_pd3dDevice->GetImmediateContext(&g_pd3dDeviceContext);
            g_pSwapChain = pSwapChain;

            DXGI_SWAP_CHAIN_DESC sd;
            pSwapChain->GetDesc(&sd);

            // Initialize ImGui
            ImGui::CreateContext();
            ImGuiIO& io = ImGui::GetIO();
            io.ConfigFlags |= ImGuiConfigFlags_NoMouseCursorChange;

            // Setup ImGui style
            ImGui::StyleColorsDark();

            // Initialize ImGui for D3D11
            ImGui_ImplWin32_Init(sd.OutputWindow);
            ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

            // Hook window procedure
            oWndProc = (WNDPROC)SetWindowLongPtr(sd.OutputWindow, GWLP_WNDPROC, (LONG_PTR)hkWndProc);

            g_bImGuiInitialized = true;
            init = true;

            LOG("D3D11 hook initialized successfully!");
        }
    }

    if (init && g_bImGuiInitialized) {
        // Create render target if needed
        if (!g_mainRenderTargetView) {
            ID3D11Texture2D* pBackBuffer;
            pSwapChain->GetBuffer(0, __uuidof(ID3D11Texture2D), (LPVOID*)&pBackBuffer);
            if (pBackBuffer) {
                g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
                pBackBuffer->Release();
            }
        }

        // Start ImGui frame
        ImGui_ImplDX11_NewFrame();
        ImGui_ImplWin32_NewFrame();
        ImGui::NewFrame();

        // Update features
        FeatureManager::Update();

        // Render ESP
        FeatureManager::Render();

        // Render GUI
        if (g_bShowMenu.load()) {
            ImGui::Begin("Skywere CS2 Cheat", nullptr, ImGuiWindowFlags_AlwaysAutoResize);
            ImGui::Text("Cheat is running!");
            ImGui::Text("Press INSERT to toggle menu");
            ImGui::Text("Press END to unload");

            if (ImGui::CollapsingHeader("Aimbot")) {
                ImGui::Text("Aimbot settings here...");
            }

            if (ImGui::CollapsingHeader("ESP")) {
                ImGui::Text("ESP settings here...");
            }

            if (ImGui::CollapsingHeader("Misc")) {
                ImGui::Text("Misc settings here...");
            }

            ImGui::End();
        }

        // Render ImGui
        ImGui::Render();

        // Set render target
        g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);

        // Render ImGui draw data
        ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());
    }

    return oPresent(pSwapChain, SyncInterval, Flags);
}

// Forward declare ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

LRESULT __stdcall hkWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    // Handle menu toggle
    if (uMsg == WM_KEYDOWN) {
        if (wParam == VK_INSERT) {
            bool currentState = g_bShowMenu.load();
            g_bShowMenu.store(!currentState);
            LOG(currentState ? "Menu hidden" : "Menu shown");
            return 0;
        }
        else if (wParam == VK_END) {
            LOG("Unload key pressed!");
            g_bRunning = false;
            return 0;
        }
    }

    // Let ImGui handle input when menu is visible
    if (g_bImGuiInitialized && g_bShowMenu.load()) {
        ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam);

        // Block input to game when menu is open
        if (ImGui::GetIO().WantCaptureMouse || ImGui::GetIO().WantCaptureKeyboard) {
            return 0;
        }
    }

    return CallWindowProc(oWndProc, hWnd, uMsg, wParam, lParam);
}

// Main thread function
DWORD WINAPI MainThread(LPVOID lpParam) {
    LOG("Skywere CS2 Cheat - Initializing...");
    
    // Wait for game to load
    while (!GetModuleHandle(CLIENT_DLL)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG("Game modules detected, initializing systems...");
    
    // Initialize memory system
    if (!Memory::Initialize()) {
        LOG("Failed to initialize memory system!");
        return 1;
    }
    
    // Initialize entity system
    if (!EntityManager::Initialize()) {
        LOG("Failed to initialize entity system!");
        return 1;
    }
    
    // Initialize features
    if (!FeatureManager::Initialize()) {
        LOG("Failed to initialize features!");
        return 1;
    }
    
    // Initialize config system
    if (!Config::Initialize()) {
        LOG("Failed to initialize config system!");
        return 1;
    }
    
    // Load default config
    Config::Load("default");
    
    // Initialize input system
    if (!Input::Initialize()) {
        LOG("Failed to initialize input system!");
        return 1;
    }
    
    LOG("Skywere initialized successfully!");

    // Hook D3D11 Present function
    if (!HookDirectX()) {
        LOG("Failed to hook DirectX!");
        return 1;
    }

    LOG("DirectX hooked successfully!");

    // Main loop
    while (g_bRunning) {
        // Update input
        Input::Update();
        
        // Update config auto-save
        Config::UpdateAutoSave();
        
        // Check for unload key (END key)
        if (GetAsyncKeyState(VK_END) & 0x8000) {
            break;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    LOG("Shutting down Skywere...");
    Cleanup();
    
    // Unload DLL
    FreeLibraryAndExitThread(g_hModule, 0);
    return 0;
}

bool HookDirectX() {
    LOG("Attempting to hook DirectX...");

    // Create a temporary D3D11 device to get the Present function address
    D3D_FEATURE_LEVEL featureLevel;
    DXGI_SWAP_CHAIN_DESC swapChainDesc = {};
    swapChainDesc.BufferCount = 1;
    swapChainDesc.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    swapChainDesc.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    swapChainDesc.OutputWindow = GetForegroundWindow();
    swapChainDesc.SampleDesc.Count = 1;
    swapChainDesc.Windowed = TRUE;
    swapChainDesc.BufferDesc.ScanlineOrdering = DXGI_MODE_SCANLINE_ORDER_UNSPECIFIED;
    swapChainDesc.BufferDesc.Scaling = DXGI_MODE_SCALING_UNSPECIFIED;
    swapChainDesc.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    ID3D11Device* tempDevice = nullptr;
    IDXGISwapChain* tempSwapChain = nullptr;
    ID3D11DeviceContext* tempContext = nullptr;

    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        0,
        nullptr,
        0,
        D3D11_SDK_VERSION,
        &swapChainDesc,
        &tempSwapChain,
        &tempDevice,
        &featureLevel,
        &tempContext
    );

    if (FAILED(hr)) {
        LOG("Failed to create temporary D3D11 device");
        return false;
    }

    // Get the Present function address from the vtable
    void** pVTable = *reinterpret_cast<void***>(tempSwapChain);
    void* pPresent = pVTable[8]; // Present is at index 8 in IDXGISwapChain vtable

    // Clean up temporary objects
    tempContext->Release();
    tempDevice->Release();
    tempSwapChain->Release();

    // Hook the Present function using simple memory patching
    // In a real implementation, you'd use a proper hooking library
    oPresent = reinterpret_cast<Present_t>(pPresent);

    // Simple inline hook (this is a basic example - use proper hooking in production)
    DWORD oldProtect;
    VirtualProtect(pPresent, 12, PAGE_EXECUTE_READWRITE, &oldProtect);

    // Create a simple jump to our hook
    BYTE hookBytes[12];
    hookBytes[0] = 0x48; // mov rax, address
    hookBytes[1] = 0xB8;
    *reinterpret_cast<void**>(&hookBytes[2]) = &hkPresent;
    hookBytes[10] = 0xFF; // jmp rax
    hookBytes[11] = 0xE0;

    memcpy(pPresent, hookBytes, 12);
    VirtualProtect(pPresent, 12, oldProtect, &oldProtect);

    LOG("DirectX Present function hooked!");
    return true;
}

void Cleanup() {
    LOG("Starting cleanup...");

    // Shutdown systems
    FeatureManager::Shutdown();
    Config::Shutdown();
    Memory::Shutdown();

    // Cleanup ImGui
    if (g_bImGuiInitialized) {
        ImGui_ImplDX11_Shutdown();
        ImGui_ImplWin32_Shutdown();
        g_bImGuiInitialized = false;
    }

    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }

    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = nullptr;
    }

    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = nullptr;
    }

    // Restore window procedure
    if (oWndProc) {
        HWND hwnd = FindWindow(L"Valve001", nullptr);
        if (hwnd) {
            SetWindowLongPtr(hwnd, GWLP_WNDPROC, (LONG_PTR)oWndProc);
        }
    }

    if (ImGui::GetCurrentContext()) {
        ImGui::DestroyContext();
    }

    LOG("Skywere shutdown complete.");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_bRunning = false;
        break;
    }
    return TRUE;
}
