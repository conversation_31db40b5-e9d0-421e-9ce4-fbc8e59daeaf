#include "pch.h"

// Simple overlay system
bool g_bOverlayInitialized = false;

// Simple console overlay for debugging
void ShowConsoleOverlay() {
    if (!g_bShowMenu.load()) return;

    // Allocate console if not already allocated
    static bool consoleAllocated = false;
    if (!consoleAllocated) {
        AllocConsole();
        freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
        freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
        freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
        SetConsoleTitleA("Skywere CS2 Cheat - Debug Console");
        consoleAllocated = true;

        std::cout << "=== SKYWERE CS2 CHEAT ===" << std::endl;
        std::cout << "Console overlay active!" << std::endl;
        std::cout << "Press INSERT to toggle menu" << std::endl;
        std::cout << "Press END to unload cheat" << std::endl;
        std::cout << "=========================" << std::endl;
    }

    // Show menu status
    static bool lastMenuState = false;
    bool currentMenuState = g_bShowMenu.load();
    if (currentMenuState != lastMenuState) {
        if (currentMenuState) {
            std::cout << "[INFO] Menu shown - Console overlay active" << std::endl;
        } else {
            std::cout << "[INFO] Menu hidden - Console overlay inactive" << std::endl;
        }
        lastMenuState = currentMenuState;
    }
}

// Main thread function
DWORD WINAPI MainThread(LPVOID lpParam) {
    LOG("Skywere CS2 Cheat - Initializing...");
    
    // Wait for game to load
    while (!GetModuleHandle(CLIENT_DLL)) {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    LOG("Game modules detected, initializing systems...");
    
    // Initialize memory system
    if (!Memory::Initialize()) {
        LOG("Failed to initialize memory system!");
        return 1;
    }
    
    // Initialize entity system
    if (!EntityManager::Initialize()) {
        LOG("Failed to initialize entity system!");
        return 1;
    }
    
    // Initialize features
    if (!FeatureManager::Initialize()) {
        LOG("Failed to initialize features!");
        return 1;
    }
    
    // Initialize config system
    if (!Config::Initialize()) {
        LOG("Failed to initialize config system!");
        return 1;
    }
    
    // Load default config
    Config::Load("default");
    
    // Initialize input system
    if (!Input::Initialize()) {
        LOG("Failed to initialize input system!");
        return 1;
    }
    
    LOG("Skywere initialized successfully!");

    // Initialize simple overlay system
    g_bOverlayInitialized = true;

    LOG("Overlay system initialized!");

    // Main loop
    while (g_bRunning) {
        // Update input
        Input::Update();

        // Check for menu toggle (INSERT key)
        static bool insertPressed = false;
        if (GetAsyncKeyState(VK_INSERT) & 0x8000) {
            if (!insertPressed) {
                bool currentState = g_bShowMenu.load();
                g_bShowMenu.store(!currentState);
                LOG(currentState ? "Menu hidden" : "Menu shown");
                insertPressed = true;
            }
        } else {
            insertPressed = false;
        }

        // Show console overlay when menu is active
        ShowConsoleOverlay();

        // Check for unload key (END key)
        if (GetAsyncKeyState(VK_END) & 0x8000) {
            LOG("Unload key pressed!");
            break;
        }

        // Update features
        FeatureManager::Update();

        // Update config auto-save
        Config::UpdateAutoSave();

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }
    
    LOG("Shutting down Skywere...");
    Cleanup();
    
    // Unload DLL
    FreeLibraryAndExitThread(g_hModule, 0);
    return 0;
}

void Cleanup() {
    LOG("Starting cleanup...");

    // Shutdown systems
    FeatureManager::Shutdown();
    Config::Shutdown();
    Memory::Shutdown();

    // Free console if allocated
    FreeConsole();

    // Cleanup ImGui
    if (ImGui::GetCurrentContext()) {
        ImGui::DestroyContext();
    }

    LOG("Skywere shutdown complete.");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_bRunning = false;
        break;
    }
    return TRUE;
}
