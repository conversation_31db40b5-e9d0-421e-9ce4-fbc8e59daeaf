#include "pch.h"

// Overlay Window Variables
HWND g_hOverlayWnd = nullptr;
HWND g_hGameWnd = nullptr;
WNDPROC g_OriginalWndProc = nullptr;

// D3D11 Objects for overlay
ID3D11Device* g_pd3dDevice = nullptr;
ID3D11DeviceContext* g_pd3dDeviceContext = nullptr;
IDXGISwapChain* g_pSwapChain = nullptr;
ID3D11RenderTargetView* g_mainRenderTargetView = nullptr;

// ImGui State
bool g_bImGuiInitialized = false;
bool g_bOverlayCreated = false;

// Overlay window class name
const char* OVERLAY_CLASS_NAME = "SkywerOverlay";

// Forward declaration for ImGui Win32 handler
extern IMGUI_IMPL_API LRESULT ImGui_ImplWin32_WndProcHandler(HWND hWnd, UINT msg, WPARAM wParam, LPARAM lParam);

// Overlay Window Procedure
LRESULT CALLBACK OverlayWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    if (g_bImGuiInitialized && ImGui_ImplWin32_WndProcHandler(hWnd, uMsg, wParam, lParam)) {
        return true;
    }

    switch (uMsg) {
    case WM_SIZE:
        if (g_pd3dDevice != nullptr && wParam != SIZE_MINIMIZED) {
            if (g_mainRenderTargetView) {
                g_mainRenderTargetView->Release();
                g_mainRenderTargetView = nullptr;
            }
            g_pSwapChain->ResizeBuffers(0, (UINT)LOWORD(lParam), (UINT)HIWORD(lParam), DXGI_FORMAT_UNKNOWN, 0);
            ID3D11Texture2D* pBackBuffer;
            g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
            g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
            pBackBuffer->Release();
        }
        return 0;
    case WM_SYSCOMMAND:
        if ((wParam & 0xfff0) == SC_KEYMENU) // Disable ALT application menu
            return 0;
        break;
    case WM_DESTROY:
        PostQuitMessage(0);
        return 0;
    }
    return DefWindowProc(hWnd, uMsg, wParam, lParam);
}

// Game Window Hook Procedure
LRESULT CALLBACK GameWndProc(HWND hWnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    // Handle our hotkeys
    if (uMsg == WM_KEYDOWN) {
        if (wParam == VK_INSERT) {
            bool currentState = g_bShowMenu.load();
            g_bShowMenu.store(!currentState);
            LOG(currentState ? "Menu hidden" : "Menu shown");

            // Show/hide overlay window
            if (g_hOverlayWnd) {
                ShowWindow(g_hOverlayWnd, currentState ? SW_HIDE : SW_SHOW);
                if (!currentState) {
                    SetForegroundWindow(g_hOverlayWnd);
                }
            }
            return 0;
        }
        else if (wParam == VK_END) {
            LOG("Unload key pressed!");
            g_bRunning = false;
            return 0;
        }
    }

    return CallWindowProc(g_OriginalWndProc, hWnd, uMsg, wParam, lParam);
}

// Create overlay window
bool CreateOverlayWindow() {
    LOG("Creating overlay window...");

    // Find the game window
    g_hGameWnd = FindWindowA("SDL_app", nullptr);
    if (!g_hGameWnd) {
        g_hGameWnd = FindWindowA(nullptr, "Counter-Strike 2");
    }
    if (!g_hGameWnd) {
        LOG("Failed to find game window");
        return false;
    }

    LOG("Found game window: 0x%p", g_hGameWnd);

    // Hook the game window procedure
    g_OriginalWndProc = (WNDPROC)SetWindowLongPtrA(g_hGameWnd, GWLP_WNDPROC, (LONG_PTR)GameWndProc);
    if (!g_OriginalWndProc) {
        LOG("Failed to hook game window procedure");
        return false;
    }

    // Get game window rect
    RECT gameRect;
    GetWindowRect(g_hGameWnd, &gameRect);

    // Register overlay window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = OverlayWndProc;
    wc.hInstance = g_hModule;
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)CreateSolidBrush(RGB(0, 0, 0));
    wc.lpszClassName = OVERLAY_CLASS_NAME;

    if (!RegisterClassEx(&wc)) {
        LOG("Failed to register overlay window class");
        return false;
    }

    // Create overlay window
    g_hOverlayWnd = CreateWindowEx(
        WS_EX_TOPMOST | WS_EX_TRANSPARENT | WS_EX_LAYERED,
        OVERLAY_CLASS_NAME,
        "Skywere Overlay",
        WS_POPUP,
        gameRect.left, gameRect.top,
        gameRect.right - gameRect.left, gameRect.bottom - gameRect.top,
        nullptr, nullptr, g_hModule, nullptr
    );

    if (!g_hOverlayWnd) {
        LOG("Failed to create overlay window");
        return false;
    }

    // Set window transparency
    SetLayeredWindowAttributes(g_hOverlayWnd, RGB(0, 0, 0), 255, LWA_COLORKEY);

    LOG("Overlay window created successfully");
    return true;
}

// Initialize DirectX for overlay
bool InitializeDirectX() {
    LOG("Initializing DirectX for overlay...");

    DXGI_SWAP_CHAIN_DESC sd;
    ZeroMemory(&sd, sizeof(sd));
    sd.BufferCount = 2;
    sd.BufferDesc.Width = 0;
    sd.BufferDesc.Height = 0;
    sd.BufferDesc.Format = DXGI_FORMAT_R8G8B8A8_UNORM;
    sd.BufferDesc.RefreshRate.Numerator = 60;
    sd.BufferDesc.RefreshRate.Denominator = 1;
    sd.Flags = DXGI_SWAP_CHAIN_FLAG_ALLOW_MODE_SWITCH;
    sd.BufferUsage = DXGI_USAGE_RENDER_TARGET_OUTPUT;
    sd.OutputWindow = g_hOverlayWnd;
    sd.SampleDesc.Count = 1;
    sd.SampleDesc.Quality = 0;
    sd.Windowed = TRUE;
    sd.SwapEffect = DXGI_SWAP_EFFECT_DISCARD;

    UINT createDeviceFlags = 0;
    D3D_FEATURE_LEVEL featureLevel;
    const D3D_FEATURE_LEVEL featureLevelArray[2] = { D3D_FEATURE_LEVEL_11_0, D3D_FEATURE_LEVEL_10_0, };

    HRESULT hr = D3D11CreateDeviceAndSwapChain(
        nullptr,
        D3D_DRIVER_TYPE_HARDWARE,
        nullptr,
        createDeviceFlags,
        featureLevelArray,
        2,
        D3D11_SDK_VERSION,
        &sd,
        &g_pSwapChain,
        &g_pd3dDevice,
        &featureLevel,
        &g_pd3dDeviceContext
    );

    if (FAILED(hr)) {
        LOG("Failed to create D3D11 device and swap chain");
        return false;
    }

    // Create render target
    ID3D11Texture2D* pBackBuffer;
    g_pSwapChain->GetBuffer(0, IID_PPV_ARGS(&pBackBuffer));
    g_pd3dDevice->CreateRenderTargetView(pBackBuffer, nullptr, &g_mainRenderTargetView);
    pBackBuffer->Release();

    LOG("DirectX initialized successfully");
    return true;
}

// Initialize ImGui
bool InitializeImGui() {
    LOG("Initializing ImGui...");

    // Setup Dear ImGui context
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImGuiIO& io = ImGui::GetIO(); (void)io;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableKeyboard;
    io.ConfigFlags |= ImGuiConfigFlags_NavEnableGamepad;

    // Setup Dear ImGui style
    ImGui::StyleColorsDark();

    // Setup Platform/Renderer backends
    ImGui_ImplWin32_Init(g_hOverlayWnd);
    ImGui_ImplDX11_Init(g_pd3dDevice, g_pd3dDeviceContext);

    g_bImGuiInitialized = true;
    LOG("ImGui initialized successfully");
    return true;
}

// Render overlay
void RenderOverlay() {
    if (!g_bImGuiInitialized) return;

    // Start the Dear ImGui frame
    ImGui_ImplDX11_NewFrame();
    ImGui_ImplWin32_NewFrame();
    ImGui::NewFrame();

    // Render our GUI
    if (g_bShowMenu.load()) {
        ImGui::SetNextWindowSize(ImVec2(600, 500), ImGuiCond_FirstUseEver);
        ImGui::SetNextWindowPos(ImVec2(50, 50), ImGuiCond_FirstUseEver);

        ImGui::Begin("Skywere CS2 Cheat", nullptr, ImGuiWindowFlags_NoCollapse);

        ImGui::Text("Skywere CS2 Internal Cheat v1.0");
        ImGui::Separator();

        if (ImGui::CollapsingHeader("Aimbot", ImGuiTreeNodeFlags_DefaultOpen)) {
            static bool aimbotEnabled = false;
            static float aimbotFov = 5.0f;
            static float aimbotSmooth = 1.0f;

            ImGui::Checkbox("Enable Aimbot", &aimbotEnabled);
            ImGui::SliderFloat("FOV", &aimbotFov, 1.0f, 20.0f);
            ImGui::SliderFloat("Smooth", &aimbotSmooth, 0.1f, 10.0f);
        }

        if (ImGui::CollapsingHeader("ESP", ImGuiTreeNodeFlags_DefaultOpen)) {
            static bool espEnabled = false;
            static bool espBox = false;
            static bool espName = false;
            static bool espHealth = false;

            ImGui::Checkbox("Enable ESP", &espEnabled);
            ImGui::Checkbox("Box ESP", &espBox);
            ImGui::Checkbox("Name ESP", &espName);
            ImGui::Checkbox("Health ESP", &espHealth);
        }

        if (ImGui::CollapsingHeader("Triggerbot", ImGuiTreeNodeFlags_DefaultOpen)) {
            static bool triggerbotEnabled = false;
            static float triggerbotDelay = 50.0f;

            ImGui::Checkbox("Enable Triggerbot", &triggerbotEnabled);
            ImGui::SliderFloat("Delay (ms)", &triggerbotDelay, 0.0f, 500.0f);
        }

        if (ImGui::CollapsingHeader("Misc", ImGuiTreeNodeFlags_DefaultOpen)) {
            static bool bhopEnabled = false;
            static bool noFlash = false;
            static bool noSmoke = false;

            ImGui::Checkbox("Bunny Hop", &bhopEnabled);
            ImGui::Checkbox("No Flash", &noFlash);
            ImGui::Checkbox("No Smoke", &noSmoke);
        }

        ImGui::Text("Press INSERT to toggle menu");
        ImGui::Text("Press END to exit cheat");

        ImGui::End();
    }

    // Rendering
    ImGui::Render();
    const float clear_color[4] = { 0.0f, 0.0f, 0.0f, 0.0f };
    g_pd3dDeviceContext->OMSetRenderTargets(1, &g_mainRenderTargetView, nullptr);
    g_pd3dDeviceContext->ClearRenderTargetView(g_mainRenderTargetView, clear_color);
    ImGui_ImplDX11_RenderDrawData(ImGui::GetDrawData());

    g_pSwapChain->Present(1, 0);
}

// Initialize overlay system
bool InitializeOverlay() {
    LOG("Initializing overlay system...");

    // Create overlay window
    if (!CreateOverlayWindow()) {
        LOG("Failed to create overlay window");
        return false;
    }

    // Initialize DirectX
    if (!InitializeDirectX()) {
        LOG("Failed to initialize DirectX");
        return false;
    }

    // Initialize ImGui
    if (!InitializeImGui()) {
        LOG("Failed to initialize ImGui");
        return false;
    }

    g_bOverlayCreated = true;
    LOG("Overlay system initialized successfully!");
    return true;
}

// Render thread function
DWORD WINAPI RenderThread(LPVOID lpParam) {
    LOG("Starting render thread...");

    // Wait for overlay to be created
    while (!g_bOverlayCreated && g_bRunning) {
        Sleep(100);
    }

    if (!g_bRunning) return 0;

    // Show overlay window
    ShowWindow(g_hOverlayWnd, SW_SHOW);
    UpdateWindow(g_hOverlayWnd);

    // Message loop for overlay
    MSG msg;
    while (g_bRunning) {
        while (PeekMessage(&msg, nullptr, 0U, 0U, PM_REMOVE)) {
            TranslateMessage(&msg);
            DispatchMessage(&msg);
            if (msg.message == WM_QUIT) {
                g_bRunning = false;
                break;
            }
        }

        if (!g_bRunning) break;

        // Render overlay
        RenderOverlay();

        Sleep(16); // ~60 FPS
    }

    LOG("Render thread exiting...");
    return 0;
}

// Main thread function
DWORD WINAPI MainThread(LPVOID lpParam) {
    LOG("Skywere CS2 Cheat - Initializing...");

    // Wait for game to load
    while (!GetModuleHandle(CLIENT_DLL)) {
        Sleep(100);
    }

    LOG("Game modules detected, initializing systems...");

    // Initialize memory system
    if (!Memory::Initialize()) {
        LOG("Failed to initialize memory system!");
        return 1;
    }

    // Initialize entity system
    if (!EntityManager::Initialize()) {
        LOG("Failed to initialize entity system!");
        return 1;
    }

    // Initialize features
    if (!FeatureManager::Initialize()) {
        LOG("Failed to initialize features!");
        return 1;
    }

    // Initialize config system
    if (!Config::Initialize()) {
        LOG("Failed to initialize config system!");
        return 1;
    }

    // Load default config
    Config::Load("default");

    // Initialize input system
    if (!Input::Initialize()) {
        LOG("Failed to initialize input system!");
        return 1;
    }

    LOG("Skywere initialized successfully!");

    // Initialize overlay system
    if (!InitializeOverlay()) {
        LOG("Failed to initialize overlay!");
        return 1;
    }

    LOG("Overlay initialized successfully!");

    // Start render thread
    HANDLE hRenderThread = CreateThread(nullptr, 0, RenderThread, nullptr, 0, nullptr);
    if (!hRenderThread) {
        LOG("Failed to create render thread!");
        return 1;
    }

    // Main loop
    while (g_bRunning) {
        // Update input
        Input::Update();

        // Update features (ESP, Aimbot, etc.)
        FeatureManager::Update();

        // Update config auto-save
        Config::UpdateAutoSave();

        // Small sleep to prevent high CPU usage
        Sleep(1);
    }

    // Wait for render thread to finish
    WaitForSingleObject(hRenderThread, 2000);
    CloseHandle(hRenderThread);
    
    LOG("Shutting down Skywere...");
    Cleanup();
    
    // Unload DLL
    FreeLibraryAndExitThread(g_hModule, 0);
    return 0;
}

void Cleanup() {
    LOG("Starting cleanup...");

    // Shutdown systems
    FeatureManager::Shutdown();
    Config::Shutdown();
    Memory::Shutdown();

    // Cleanup ImGui
    if (g_bImGuiInitialized) {
        ImGui_ImplDX11_Shutdown();
        ImGui_ImplWin32_Shutdown();
        ImGui::DestroyContext();
        g_bImGuiInitialized = false;
    }

    // Cleanup D3D11 objects
    if (g_mainRenderTargetView) {
        g_mainRenderTargetView->Release();
        g_mainRenderTargetView = nullptr;
    }

    if (g_pSwapChain) {
        g_pSwapChain->Release();
        g_pSwapChain = nullptr;
    }

    if (g_pd3dDeviceContext) {
        g_pd3dDeviceContext->Release();
        g_pd3dDeviceContext = nullptr;
    }

    if (g_pd3dDevice) {
        g_pd3dDevice->Release();
        g_pd3dDevice = nullptr;
    }

    // Destroy overlay window
    if (g_hOverlayWnd) {
        DestroyWindow(g_hOverlayWnd);
        g_hOverlayWnd = nullptr;
    }

    // Restore game window procedure
    if (g_OriginalWndProc && g_hGameWnd) {
        SetWindowLongPtr(g_hGameWnd, GWLP_WNDPROC, (LONG_PTR)g_OriginalWndProc);
    }

    // Unregister overlay window class
    UnregisterClass(OVERLAY_CLASS_NAME, g_hModule);

    LOG("Skywere shutdown complete.");
}

// DLL Entry Point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        g_hModule = hModule;
        DisableThreadLibraryCalls(hModule);
        CreateThread(nullptr, 0, MainThread, nullptr, 0, nullptr);
        break;
    case DLL_PROCESS_DETACH:
        g_bRunning = false;
        break;
    }
    return TRUE;
}
