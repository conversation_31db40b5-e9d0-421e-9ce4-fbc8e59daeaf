@echo off
setlocal enabledelayedexpansion

title Skywere CS2 Injector

echo.
echo ===============================================
echo         Skywere CS2 Advanced Injector
echo ===============================================
echo.

:: Check if running as administrator
net session >nul 2>&1
if errorlevel 1 (
    echo ERROR: This script requires administrator privileges!
    echo Please run as administrator.
    echo.
    pause
    exit /b 1
)

:: Set paths
set "INJECTOR_PATH=%~dp0x64\Release\Injector.exe"
set "DLL_PATH=%~dp0..\Skywere\x64\Release\Skywere.dll"
set "CONFIG_PATH=%~dp0config.json"

:: Check if injector exists
if not exist "%INJECTOR_PATH%" (
    echo ERROR: Injector not found at: %INJECTOR_PATH%
    echo Please build the project first using build.bat
    echo.
    pause
    exit /b 1
)

:: Check if DLL exists
if not exist "%DLL_PATH%" (
    echo ERROR: Skywere.dll not found at: %DLL_PATH%
    echo Please build the Skywere project first.
    echo.
    echo Trying alternative path...
    set "DLL_PATH=%~dp0Skywere.dll"
    if not exist "!DLL_PATH!" (
        echo ERROR: Skywere.dll not found in current directory either.
        echo Please ensure Skywere.dll is available.
        echo.
        pause
        exit /b 1
    )
)

echo Found injector: %INJECTOR_PATH%
echo Found DLL: %DLL_PATH%
echo.

:: Check if CS2 is running
echo Checking for CS2 process...
tasklist /FI "IMAGENAME eq cs2.exe" 2>NUL | find /I /N "cs2.exe" >NUL
if errorlevel 1 (
    echo CS2 is not running. Would you like to:
    echo [1] Wait for CS2 to start
    echo [2] Exit
    echo.
    set /p choice="Enter your choice (1-2): "
    
    if "!choice!"=="1" (
        echo Waiting for CS2 to start...
        echo Press Ctrl+C to cancel.
        echo.
        goto :wait_for_cs2
    ) else (
        echo Exiting...
        pause
        exit /b 0
    )
) else (
    echo CS2 is running!
    echo.
)

goto :inject

:wait_for_cs2
timeout /t 2 /nobreak >nul
tasklist /FI "IMAGENAME eq cs2.exe" 2>NUL | find /I /N "cs2.exe" >NUL
if errorlevel 1 (
    goto :wait_for_cs2
)
echo CS2 detected! Proceeding with injection...
echo.

:inject
:: Display injection options
echo Select injection method:
echo [1] Manual Map (Recommended - Most Stealthy)
echo [2] LoadLibrary (Standard)
echo [3] Thread Hijacking (Advanced)
echo [4] Process Hollowing (Experimental)
echo.
set /p method="Enter your choice (1-4): "

set "injection_method=manualmap"
if "!method!"=="1" set "injection_method=manualmap"
if "!method!"=="2" set "injection_method=loadlibrary"
if "!method!"=="3" set "injection_method=hijack"
if "!method!"=="4" set "injection_method=hollow"

echo.
echo Selected method: !injection_method!
echo.

:: Ask for stealth options
set /p stealth="Enable maximum stealth? (Y/N): "
set "stealth_flag="
if /i "!stealth!"=="Y" set "stealth_flag=-s"

:: Ask for delay
set /p delay="Add injection delay in seconds (0 for no delay): "
set "delay_flag="
if not "!delay!"=="0" (
    set /a delay_ms=!delay!*1000
    set "delay_flag=-d !delay_ms!"
)

:: Perform injection
echo.
echo ===============================================
echo              STARTING INJECTION
echo ===============================================
echo.
echo Method: !injection_method!
echo Target: cs2.exe
echo DLL: %DLL_PATH%
echo Stealth: !stealth!
echo Delay: !delay! seconds
echo.
echo Injecting...

"%INJECTOR_PATH%" -m !injection_method! !stealth_flag! !delay_flag! -v cs2.exe "%DLL_PATH%"

set "exit_code=!errorlevel!"

echo.
echo ===============================================
if !exit_code! equ 0 (
    echo            INJECTION SUCCESSFUL!
    echo.
    echo Skywere has been successfully injected into CS2.
    echo You can now use the cheat features.
    echo.
    echo IMPORTANT REMINDERS:
    echo - Use the cheat responsibly
    echo - Be aware of VAC detection risks
    echo - Close the cheat before exiting CS2
) else (
    echo            INJECTION FAILED!
    echo.
    echo Error code: !exit_code!
    echo.
    echo Common solutions:
    echo - Ensure CS2 is running
    echo - Run as administrator
    echo - Disable antivirus temporarily
    echo - Check if CS2 is already injected
    echo - Try a different injection method
)
echo ===============================================
echo.

:: Ask if user wants to inject again
if !exit_code! neq 0 (
    set /p retry="Would you like to try again? (Y/N): "
    if /i "!retry!"=="Y" (
        echo.
        goto :inject
    )
)

echo Press any key to exit...
pause >nul
