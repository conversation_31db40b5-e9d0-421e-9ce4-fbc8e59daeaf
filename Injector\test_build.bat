@echo off
echo Testing Injector compilation...
echo.

REM Set up Visual Studio environment
call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo Error: Could not set up Visual Studio environment
    pause
    exit /b 1
)

echo Building Release configuration...
msbuild Injector.vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if errorlevel 1 (
    echo.
    echo Build failed! Check the errors above.
    pause
    exit /b 1
) else (
    echo.
    echo Build successful!
    if exist "x64\Release\Injector.exe" (
        echo Executable created: x64\Release\Injector.exe
        echo File size: 
        dir "x64\Release\Injector.exe" | find ".exe"
    ) else (
        echo Warning: Executable not found in expected location
    )
)

echo.
pause
