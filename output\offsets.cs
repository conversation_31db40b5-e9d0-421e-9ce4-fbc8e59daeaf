// Generated using https://github.com/a2x/cs2-dumper
// 2025-06-30 21:10:27.187280500 UTC

namespace CS2Dumper.Offsets {
    // Module: client.dll
    public static class ClientDll {
        public const nint dwCSGOInput = 0x1A75250;
        public const nint dwEntityList = 0x1A020A8;
        public const nint dwGameEntitySystem = 0x1B25BD8;
        public const nint dwGameEntitySystem_highestEntityIndex = 0x20F0;
        public const nint dwGameRules = 0x1A66B38;
        public const nint dwGlobalVars = 0x1849EB0;
        public const nint dwGlowManager = 0x1A66270;
        public const nint dwLocalPlayerController = 0x1A50AD0;
        public const nint dwLocalPlayerPawn = 0x18560D0;
        public const nint dwPlantedC4 = 0x1A702F8;
        public const nint dwPrediction = 0x1855F50;
        public const nint dwSensitivity = 0x1A67858;
        public const nint dwSensitivity_sensitivity = 0x40;
        public const nint dwViewAngles = 0x1A75620;
        public const nint dwViewMatrix = 0x1A6B230;
        public const nint dwViewRender = 0x1A6BB80;
        public const nint dwWeaponC4 = 0x1A04590;
    }
    // Module: engine2.dll
    public static class Engine2Dll {
        public const nint dwBuildNumber = 0x540BE4;
        public const nint dwNetworkGameClient = 0x53FCE0;
        public const nint dwNetworkGameClient_clientTickCount = 0x368;
        public const nint dwNetworkGameClient_deltaTick = 0x27C;
        public const nint dwNetworkGameClient_isBackgroundMap = 0x281447;
        public const nint dwNetworkGameClient_localPlayer = 0xF0;
        public const nint dwNetworkGameClient_maxClients = 0x238;
        public const nint dwNetworkGameClient_serverTickCount = 0x36C;
        public const nint dwNetworkGameClient_signOnState = 0x228;
        public const nint dwWindowHeight = 0x623564;
        public const nint dwWindowWidth = 0x623560;
    }
    // Module: inputsystem.dll
    public static class InputsystemDll {
        public const nint dwInputSystem = 0x387E0;
    }
    // Module: matchmaking.dll
    public static class MatchmakingDll {
        public const nint dwGameTypes = 0x1A52E0;
        public const nint dwGameTypes_mapName = 0x120;
    }
    // Module: soundsystem.dll
    public static class SoundsystemDll {
        public const nint dwSoundSystem = 0x3A15F0;
        public const nint dwSoundSystem_engineViewData = 0x7C;
    }
}
