﻿  pch.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/pch.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
  main.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/main.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
  injector.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/injector.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
  manual_map.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/manual_map.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\manual_map.cpp(173,133): warning C4244: 'argumento': conversão de 'ULONGLONG' para 'DWORD', possível perda de dados
  process.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/process.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
  utils.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\include\pch.h(30,9): warning C4005: 'ERROR': redefinição de macro
  (compilando o arquivo fonte 'src/utils.cpp')
      C:\Program Files (x86)\Windows Kits\10\Include\10.0.26100.0\um\wingdi.h(118,9):
      ver a definição anterior de "ERROR"
  
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\utils.cpp(495,42): warning C4319: '~': estendendo com zeros 'DWORD' para 'ULONG_PTR' de tamanho maior
C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\src\utils.cpp(501,41): warning C4319: '~': estendendo com zeros 'DWORD' para 'SIZE_T' de tamanho maior
  Gerando código
  Previous IPDB not found, fall back to full compilation.
  All 897 functions were compiled because no usable IPDB/IOBJ from previous compilation was found.
  Finalizada a geração de código
  Injector.vcxproj -> C:\Users\<USER>\Desktop\teste hack cs2 real\Injector\x64\Release\Injector.exe
