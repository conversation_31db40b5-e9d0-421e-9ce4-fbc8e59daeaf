using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using Guna.UI2.WinForms;

namespace InjectorGUI
{
    public partial class MainForm : Form
    {
        private Guna2Panel mainPanel;
        private Guna2Panel headerPanel;
        private Guna2Panel contentPanel;
        private Guna2TextBox processNameTextBox;
        private Guna2TextBox dllPathTextBox;
        private Guna2Button selectDllButton;
        private Guna2Button injectButton;
        private Guna2Button refreshButton;
        private Guna2ComboBox processComboBox;
        private Guna2TextBox logTextBox;
        private Label titleLabel;
        private Label processLabel;
        private Label dllLabel;
        private Label logLabel;
        private Guna2ControlBox closeButton;
        private Guna2ControlBox minimizeButton;
        private Guna2DragControl dragControl;

        public MainForm()
        {
            InitializeComponent();
            LoadProcesses();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = "Skywere Injector";
            this.Size = new Size(600, 500);
            this.FormBorderStyle = FormBorderStyle.None;
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(25, 25, 25);

            // Drag control
            dragControl = new Guna2DragControl();
            dragControl.TargetControl = this;

            // Main panel
            mainPanel = new Guna2Panel();
            mainPanel.Dock = DockStyle.Fill;
            mainPanel.FillColor = Color.FromArgb(25, 25, 25);
            mainPanel.BorderRadius = 10;
            this.Controls.Add(mainPanel);

            // Header panel
            headerPanel = new Guna2Panel();
            headerPanel.Size = new Size(600, 50);
            headerPanel.Location = new Point(0, 0);
            headerPanel.FillColor = Color.FromArgb(35, 35, 35);
            headerPanel.Dock = DockStyle.Top;
            mainPanel.Controls.Add(headerPanel);

            // Title label
            titleLabel = new Label();
            titleLabel.Text = "SKYWERE INJECTOR";
            titleLabel.Font = new Font("Segoe UI", 14, FontStyle.Bold);
            titleLabel.ForeColor = Color.FromArgb(94, 148, 255);
            titleLabel.Location = new Point(20, 15);
            titleLabel.AutoSize = true;
            headerPanel.Controls.Add(titleLabel);

            // Close button
            closeButton = new Guna2ControlBox();
            closeButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            closeButton.FillColor = Color.Transparent;
            closeButton.IconColor = Color.White;
            closeButton.Location = new Point(550, 10);
            closeButton.Size = new Size(30, 30);
            headerPanel.Controls.Add(closeButton);

            // Minimize button
            minimizeButton = new Guna2ControlBox();
            minimizeButton.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            minimizeButton.ControlBoxType = Guna.UI2.WinForms.Enums.ControlBoxType.MinimizeBox;
            minimizeButton.FillColor = Color.Transparent;
            minimizeButton.IconColor = Color.White;
            minimizeButton.Location = new Point(520, 10);
            minimizeButton.Size = new Size(30, 30);
            headerPanel.Controls.Add(minimizeButton);

            // Content panel
            contentPanel = new Guna2Panel();
            contentPanel.Dock = DockStyle.Fill;
            contentPanel.FillColor = Color.FromArgb(25, 25, 25);
            contentPanel.Padding = new Padding(20);
            mainPanel.Controls.Add(contentPanel);

            // Process label
            processLabel = new Label();
            processLabel.Text = "Target Process:";
            processLabel.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            processLabel.ForeColor = Color.White;
            processLabel.Location = new Point(20, 20);
            processLabel.AutoSize = true;
            contentPanel.Controls.Add(processLabel);

            // Process combo box
            processComboBox = new Guna2ComboBox();
            processComboBox.Size = new Size(400, 36);
            processComboBox.Location = new Point(20, 45);
            processComboBox.FillColor = Color.FromArgb(40, 40, 40);
            processComboBox.ForeColor = Color.White;
            processComboBox.BorderColor = Color.FromArgb(94, 148, 255);
            processComboBox.Font = new Font("Segoe UI", 9);
            processComboBox.DropDownStyle = ComboBoxStyle.DropDownList;
            contentPanel.Controls.Add(processComboBox);

            // Refresh button
            refreshButton = new Guna2Button();
            refreshButton.Text = "Refresh";
            refreshButton.Size = new Size(100, 36);
            refreshButton.Location = new Point(430, 45);
            refreshButton.FillColor = Color.FromArgb(94, 148, 255);
            refreshButton.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            refreshButton.BorderRadius = 5;
            refreshButton.Click += RefreshButton_Click;
            contentPanel.Controls.Add(refreshButton);

            // DLL label
            dllLabel = new Label();
            dllLabel.Text = "DLL Path:";
            dllLabel.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            dllLabel.ForeColor = Color.White;
            dllLabel.Location = new Point(20, 100);
            dllLabel.AutoSize = true;
            contentPanel.Controls.Add(dllLabel);

            // DLL path textbox
            dllPathTextBox = new Guna2TextBox();
            dllPathTextBox.Size = new Size(400, 36);
            dllPathTextBox.Location = new Point(20, 125);
            dllPathTextBox.FillColor = Color.FromArgb(40, 40, 40);
            dllPathTextBox.ForeColor = Color.White;
            dllPathTextBox.BorderColor = Color.FromArgb(94, 148, 255);
            dllPathTextBox.Font = new Font("Segoe UI", 9);
            dllPathTextBox.PlaceholderText = "Select DLL file...";
            dllPathTextBox.Text = "Skywere.dll";
            contentPanel.Controls.Add(dllPathTextBox);

            // Select DLL button
            selectDllButton = new Guna2Button();
            selectDllButton.Text = "Browse";
            selectDllButton.Size = new Size(100, 36);
            selectDllButton.Location = new Point(430, 125);
            selectDllButton.FillColor = Color.FromArgb(94, 148, 255);
            selectDllButton.Font = new Font("Segoe UI", 9, FontStyle.Bold);
            selectDllButton.BorderRadius = 5;
            selectDllButton.Click += SelectDllButton_Click;
            contentPanel.Controls.Add(selectDllButton);

            // Inject button
            injectButton = new Guna2Button();
            injectButton.Text = "INJECT DLL";
            injectButton.Size = new Size(510, 45);
            injectButton.Location = new Point(20, 180);
            injectButton.FillColor = Color.FromArgb(255, 77, 77);
            injectButton.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            injectButton.BorderRadius = 8;
            injectButton.Click += InjectButton_Click;
            contentPanel.Controls.Add(injectButton);

            // Log label
            logLabel = new Label();
            logLabel.Text = "Log:";
            logLabel.Font = new Font("Segoe UI", 10, FontStyle.Bold);
            logLabel.ForeColor = Color.White;
            logLabel.Location = new Point(20, 240);
            logLabel.AutoSize = true;
            contentPanel.Controls.Add(logLabel);

            // Log textbox
            logTextBox = new Guna2TextBox();
            logTextBox.Size = new Size(510, 150);
            logTextBox.Location = new Point(20, 265);
            logTextBox.FillColor = Color.FromArgb(40, 40, 40);
            logTextBox.ForeColor = Color.White;
            logTextBox.BorderColor = Color.FromArgb(94, 148, 255);
            logTextBox.Font = new Font("Consolas", 8);
            logTextBox.Multiline = true;
            logTextBox.ScrollBars = ScrollBars.Vertical;
            logTextBox.ReadOnly = true;
            contentPanel.Controls.Add(logTextBox);

            this.ResumeLayout(false);
        }

        private void LoadProcesses()
        {
            try
            {
                processComboBox.Items.Clear();
                processComboBox.Items.Add("cs2.exe");
                processComboBox.Items.Add("notepad.exe");
                processComboBox.Items.Add("calc.exe");
                
                var processes = System.Diagnostics.Process.GetProcesses()
                    .Where(p => !string.IsNullOrEmpty(p.ProcessName))
                    .OrderBy(p => p.ProcessName)
                    .Select(p => p.ProcessName + ".exe")
                    .Distinct()
                    .Take(50);

                foreach (var process in processes)
                {
                    if (!processComboBox.Items.Contains(process))
                    {
                        processComboBox.Items.Add(process);
                    }
                }

                if (processComboBox.Items.Contains("cs2.exe"))
                {
                    processComboBox.SelectedItem = "cs2.exe";
                }

                LogMessage("Processes loaded successfully.");
            }
            catch (Exception ex)
            {
                LogMessage($"Error loading processes: {ex.Message}");
            }
        }

        private void RefreshButton_Click(object sender, EventArgs e)
        {
            LoadProcesses();
        }

        private void SelectDllButton_Click(object sender, EventArgs e)
        {
            using (OpenFileDialog openFileDialog = new OpenFileDialog())
            {
                openFileDialog.Filter = "DLL files (*.dll)|*.dll|All files (*.*)|*.*";
                openFileDialog.Title = "Select DLL file";

                if (openFileDialog.ShowDialog() == DialogResult.OK)
                {
                    dllPathTextBox.Text = openFileDialog.FileName;
                    LogMessage($"Selected DLL: {openFileDialog.FileName}");
                }
            }
        }

        private async void InjectButton_Click(object sender, EventArgs e)
        {
            try
            {
                string processName = processComboBox.SelectedItem?.ToString();
                string dllPath = dllPathTextBox.Text;

                if (string.IsNullOrEmpty(processName))
                {
                    LogMessage("ERROR: Please select a target process.");
                    return;
                }

                if (string.IsNullOrEmpty(dllPath))
                {
                    LogMessage("ERROR: Please select a DLL file.");
                    return;
                }

                if (!File.Exists(dllPath))
                {
                    LogMessage($"ERROR: DLL file not found: {dllPath}");
                    return;
                }

                LogMessage($"Starting injection...");
                LogMessage($"Target: {processName}");
                LogMessage($"DLL: {dllPath}");

                injectButton.Enabled = false;
                injectButton.Text = "INJECTING...";

                await Task.Run(() =>
                {
                    var processes = Injector.GetProcessesByName(processName);
                    if (processes.Length == 0)
                    {
                        throw new Exception($"Process not found: {processName}");
                    }

                    var targetProcess = processes[0];
                    LogMessage($"Found process ID: {targetProcess.Id}");

                    bool success = Injector.InjectDLL(targetProcess.Id, dllPath);
                    if (success)
                    {
                        LogMessage("SUCCESS: DLL injected successfully!");
                    }
                    else
                    {
                        LogMessage("FAILED: Injection failed!");
                    }
                });
            }
            catch (Exception ex)
            {
                LogMessage($"ERROR: {ex.Message}");
            }
            finally
            {
                injectButton.Enabled = true;
                injectButton.Text = "INJECT DLL";
            }
        }

        private void LogMessage(string message)
        {
            if (InvokeRequired)
            {
                Invoke(new Action<string>(LogMessage), message);
                return;
            }

            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            logTextBox.Text += $"[{timestamp}] {message}\r\n";
            logTextBox.SelectionStart = logTextBox.Text.Length;
            logTextBox.ScrollToCaret();
        }
    }
}
