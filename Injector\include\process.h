#pragma once
#include <windows.h>
#include <string>
#include <vector>
#include <map>
#include <psapi.h>
#include <tlhelp32.h>

// Forward declarations
enum class ProcessArchitecture {
    X86,
    X64,
    UNKNOWN
};

struct ProcessInfo {
    DWORD pid = 0;
    std::wstring name;
    std::wstring path;
    ProcessArchitecture arch = ProcessArchitecture::UNKNOWN;
    bool isElevated = false;
    bool isWow64 = false;
};

class ProcessManager {
public:
    // Process enumeration
    static std::vector<ProcessInfo> GetProcessList();
    static std::vector<DWORD> FindProcessByName(const std::wstring& processName);
    static ProcessInfo GetProcessInfo(DWORD processId);
    static ProcessInfo GetProcessInfo(const std::wstring& processName);
    
    // Process operations
    static HANDLE OpenProcess(DWORD processId, DWORD desiredAccess = PROCESS_ALL_ACCESS);
    static bool IsProcessRunning(DWORD processId);
    static bool IsProcessRunning(const std::wstring& processName);
    static bool WaitForProcess(const std::wstring& processName, DWORD timeout = INFINITE);
    static bool TerminateProcess(DWORD processId);
    
    // Process information
    static ProcessArchitecture GetProcessArchitecture(HANDLE hProcess);
    static ProcessArchitecture GetProcessArchitecture(DWORD processId);
    static bool IsProcess64Bit(HANDLE hProcess);
    static bool IsProcessElevated(HANDLE hProcess);
    static std::wstring GetProcessPath(HANDLE hProcess);
    static std::wstring GetProcessPath(DWORD processId);
    
    // Module operations
    static std::vector<HMODULE> GetProcessModules(HANDLE hProcess);
    static HMODULE GetModuleHandle(HANDLE hProcess, const std::wstring& moduleName);
    static LPVOID GetModuleBaseAddress(HANDLE hProcess, const std::wstring& moduleName);
    static SIZE_T GetModuleSize(HANDLE hProcess, HMODULE hModule);
    static std::wstring GetModulePath(HANDLE hProcess, HMODULE hModule);
    
    // Thread operations
    static std::vector<DWORD> GetProcessThreads(DWORD processId);
    static HANDLE OpenThread(DWORD threadId, DWORD desiredAccess = THREAD_ALL_ACCESS);
    static bool SuspendThread(DWORD threadId);
    static bool ResumeThread(DWORD threadId);
    static CONTEXT GetThreadContext(HANDLE hThread);
    static bool SetThreadContext(HANDLE hThread, const CONTEXT& context);
    
    // Memory operations
    static bool ReadMemory(HANDLE hProcess, LPVOID address, LPVOID buffer, SIZE_T size);
    static bool WriteMemory(HANDLE hProcess, LPVOID address, LPCVOID data, SIZE_T size);
    static LPVOID AllocateMemory(HANDLE hProcess, SIZE_T size, DWORD allocationType = MEM_COMMIT | MEM_RESERVE, DWORD protection = PAGE_EXECUTE_READWRITE);
    static bool FreeMemory(HANDLE hProcess, LPVOID address, SIZE_T size = 0);
    static bool ProtectMemory(HANDLE hProcess, LPVOID address, SIZE_T size, DWORD newProtection, DWORD* oldProtection = nullptr);
    
    // Advanced process operations
    static bool InjectShellcode(HANDLE hProcess, const std::vector<BYTE>& shellcode, LPVOID& allocatedAddress);
    static bool CreateRemoteThread(HANDLE hProcess, LPTHREAD_START_ROUTINE startAddress, LPVOID parameter, HANDLE* hThread = nullptr);
    static bool QueueUserAPC(HANDLE hThread, PAPCFUNC apcFunction, ULONG_PTR parameter);
    
    // Process environment
    static std::vector<std::wstring> GetProcessCommandLine(DWORD processId);
    static std::map<std::wstring, std::wstring> GetProcessEnvironment(DWORD processId);
    static bool IsProcessInJob(HANDLE hProcess);
    static bool IsProcessCritical(HANDLE hProcess);
    
    // Security and privileges
    static bool EnablePrivilege(const std::wstring& privilegeName);
    static bool DisablePrivilege(const std::wstring& privilegeName);
    static bool HasPrivilege(const std::wstring& privilegeName);
    static bool IsRunningAsAdmin();
    static bool ElevateProcess();
    
    // Anti-detection
    static bool IsProcessBeingDebugged(HANDLE hProcess);
    static bool IsRemoteDebuggerPresent(HANDLE hProcess);
    static bool CheckForHooks(HANDLE hProcess);
    static bool IsProcessInSandbox(HANDLE hProcess);
    static bool IsProcessVirtualized(HANDLE hProcess);
    
private:
    // Internal helper functions
    static bool EnableDebugPrivilege();
    static DWORD GetProcessIdFromHandle(HANDLE hProcess);
    static std::wstring GetProcessNameFromId(DWORD processId);
    static bool IsWow64Process(HANDLE hProcess);
    
    // NTDLL functions
    static bool InitializeNtFunctions();
    static HMODULE ntdllModule;
    
    // Function pointers for NTDLL
    typedef NTSTATUS(NTAPI* pNtQueryInformationProcess)(HANDLE, PROCESSINFOCLASS, PVOID, ULONG, PULONG);
    typedef NTSTATUS(NTAPI* pNtReadVirtualMemory)(HANDLE, PVOID, PVOID, SIZE_T, PSIZE_T);
    typedef NTSTATUS(NTAPI* pNtWriteVirtualMemory)(HANDLE, PVOID, PVOID, SIZE_T, PSIZE_T);
    typedef NTSTATUS(NTAPI* pNtAllocateVirtualMemory)(HANDLE, PVOID*, ULONG_PTR, PSIZE_T, ULONG, ULONG);
    typedef NTSTATUS(NTAPI* pNtFreeVirtualMemory)(HANDLE, PVOID*, PSIZE_T, ULONG);
    typedef NTSTATUS(NTAPI* pNtProtectVirtualMemory)(HANDLE, PVOID*, PSIZE_T, ULONG, PULONG);
    
    static pNtQueryInformationProcess NtQueryInformationProcess;
    static pNtReadVirtualMemory NtReadVirtualMemory;
    static pNtWriteVirtualMemory NtWriteVirtualMemory;
    static pNtAllocateVirtualMemory NtAllocateVirtualMemory;
    static pNtFreeVirtualMemory NtFreeVirtualMemory;
    static pNtProtectVirtualMemory NtProtectVirtualMemory;
};

// Process scanner for finding injection targets
class ProcessScanner {
public:
    static std::vector<ProcessInfo> ScanForTargets(const std::vector<std::wstring>& targetNames);
    static ProcessInfo FindBestTarget(const std::vector<std::wstring>& targetNames);
    static bool IsValidTarget(const ProcessInfo& processInfo);
    static bool IsProcessProtected(HANDLE hProcess);
    
private:
    static bool CheckProcessIntegrity(HANDLE hProcess);
    static bool CheckAntiCheatPresence(HANDLE hProcess);
    static int CalculateTargetScore(const ProcessInfo& processInfo);
};
