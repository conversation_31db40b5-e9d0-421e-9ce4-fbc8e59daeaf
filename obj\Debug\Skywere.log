﻿  pch.cpp
  dllmain.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(75,31): error C2065: 'ImGuiConfigFlags_NoMouseCursorChange': identificador não declarado
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(118,56): error C2065: 'ImGuiWindowFlags_NoCollapse': identificador não declarado
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(123,51): error C2065: 'ImGuiTreeNodeFlags_DefaultOpen': identificador não declarado
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(125,62): error C2664: 'bool ImGui::Checkbox(const char *,bool *)': não é possível converter um argumento 2 de 'std::atomic<bool> *' em 'bool *'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(125,50): message : Tipos apontados não são relacionados; conversão requer reinterpret_cast, conversão C-style ou conversão function-style
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\external\imgui\imgui.h(545,20): message : consulte a declaração de 'ImGui::Checkbox'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(128,48): error C2065: 'ImGuiTreeNodeFlags_DefaultOpen': identificador não declarado
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(130,59): error C2664: 'bool ImGui::Checkbox(const char *,bool *)': não é possível converter um argumento 2 de 'std::atomic<bool> *' em 'bool *'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\dllmain.cpp(130,47): message : Tipos apontados não são relacionados; conversão requer reinterpret_cast, conversão C-style ou conversão function-style
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\external\imgui\imgui.h(545,20): message : consulte a declaração de 'ImGui::Checkbox'
  memory.cpp
  entity.cpp
  features.cpp
  gui.cpp
  config.cpp
  imgui_impl_win32.cpp
  imgui_impl_dx11.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\imgui_impl_dx11.cpp(81,19): error C2039: ' GetTexDataAsRGBA32': não é um membro de 'ImFontAtlas'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\external\imgui\imgui.h(16): message : consulte a declaração de 'ImFontAtlas'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\src\imgui_impl_dx11.cpp(114,19): error C2039: ' SetTexID': não é um membro de 'ImFontAtlas'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\external\imgui\imgui.h(16): message : consulte a declaração de 'ImFontAtlas'
  Gerando Código...
