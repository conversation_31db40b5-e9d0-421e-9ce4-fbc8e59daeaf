﻿  pch.cpp
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35,20): error C2144: erro de sintaxe: 'bool' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(36,20): error C2144: erro de sintaxe: 'void' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(36,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(36,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(37,20): error C2144: erro de sintaxe: 'void' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(37,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(37,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(38,28): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(38,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(38,20): error C2146: erro de sintaxe: ';' ausente antes do identificador 'LRESULT'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(41,20): error C2144: erro de sintaxe: 'bool' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(41,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(41,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(42,20): error C2144: erro de sintaxe: 'void' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(42,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(42,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(43,20): error C2144: erro de sintaxe: 'void' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(43,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(43,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,20): error C2144: erro de sintaxe: 'void' deve ser precedido por ';'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,25): error C4430: faltando especificador de tipo - int assumido. Observação: C++ não suporta default-int
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,5): error C2086: 'int IMGUI_IMPL_API': redefinição
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(35): message : consulte a declaração de 'IMGUI_IMPL_API'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,25): error C2182: 'ImGui_ImplDX11_RenderDrawData': uso inválido de tipo 'void'
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,55): error C2065: 'ImDrawData': identificador não declarado
C:\Users\<USER>\Desktop\teste hack cs2 real\Skywere\include\pch.h(44,67): error C2065: 'draw_data': identificador não declarado
