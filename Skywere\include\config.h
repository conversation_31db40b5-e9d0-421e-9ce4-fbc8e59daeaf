#pragma once

// Configuration manager
class Config {
public:
    static bool Initialize();
    static void Shutdown();
    
    // Config operations
    static bool Load(const std::string& name = "default");
    static bool Save(const std::string& name = "default");
    static bool Delete(const std::string& name);
    static bool Exists(const std::string& name);
    static std::vector<std::string> GetConfigList();
    
    // Auto-save
    static void EnableAutoSave(bool enable, float interval = 30.0f);
    static void UpdateAutoSave();
    
    // Config values
    template<typename T>
    static void Set(const std::string& key, const T& value);
    
    template<typename T>
    static T Get(const std::string& key, const T& defaultValue = T{});
    
    // Specialized setters/getters
    static void SetBool(const std::string& key, bool value);
    static void SetInt(const std::string& key, int value);
    static void SetFloat(const std::string& key, float value);
    static void SetString(const std::string& key, const std::string& value);
    static void SetColor(const std::string& key, const ImVec4& value);
    static void SetVector3(const std::string& key, const Vector3& value);
    static void SetAngles(const std::string& key, const QAngle& value);
    
    static bool GetBool(const std::string& key, bool defaultValue = false);
    static int GetInt(const std::string& key, int defaultValue = 0);
    static float GetFloat(const std::string& key, float defaultValue = 0.0f);
    static std::string GetString(const std::string& key, const std::string& defaultValue = "");
    static ImVec4 GetColor(const std::string& key, const ImVec4& defaultValue = ImVec4(1, 1, 1, 1));
    static Vector3 GetVector3(const std::string& key, const Vector3& defaultValue = Vector3());
    static QAngle GetAngles(const std::string& key, const QAngle& defaultValue = QAngle());
    
    // Feature configs
    static void SaveFeatureConfigs();
    static void LoadFeatureConfigs();
    
private:
    static bool initialized;
    static std::string configDirectory;
    static std::string currentConfig;
    static std::unordered_map<std::string, std::string> configData;
    static std::mutex configMutex;
    
    // Auto-save
    static bool autoSaveEnabled;
    static float autoSaveInterval;
    static std::chrono::steady_clock::time_point lastAutoSave;
    
    // File operations
    static std::string GetConfigPath(const std::string& name);
    static bool CreateConfigDirectory();
    static bool LoadFromFile(const std::string& filepath);
    static bool SaveToFile(const std::string& filepath);
    
    // JSON parsing
    static bool ParseJSON(const std::string& json);
    static std::string GenerateJSON();
    
    // Value conversion
    static std::string ToString(const ImVec4& color);
    static std::string ToStringVector3(const Vector3& vector);
    static std::string ToString(const QAngle& angles);
    static ImVec4 ToColor(const std::string& str);
    static Vector3 ToVector3(const std::string& str);
    static QAngle ToAngles(const std::string& str);
};

// Config presets
namespace ConfigPresets {
    void LoadLegitPreset();
    void LoadRagePreset();
    void LoadESPOnlyPreset();
    void LoadDefaultPreset();
    void LoadSilentPreset();
}

// Config validation
namespace ConfigValidator {
    bool ValidateAimbotConfig();
    bool ValidateESPConfig();
    bool ValidateTriggerbotConfig();
    bool ValidateMiscConfig();
    bool ValidateGlowConfig();
    bool ValidateAllConfigs();
    
    void FixInvalidValues();
}

// Config migration (for updating old configs)
namespace ConfigMigration {
    bool NeedsMigration(const std::string& configName);
    bool MigrateConfig(const std::string& configName);
    int GetConfigVersion(const std::string& configName);
    void SetConfigVersion(const std::string& configName, int version);
}

// Config backup system
namespace ConfigBackup {
    bool CreateBackup(const std::string& configName);
    bool RestoreBackup(const std::string& configName, const std::string& backupName);
    std::vector<std::string> GetBackupList(const std::string& configName);
    bool DeleteBackup(const std::string& configName, const std::string& backupName);
    void CleanupOldBackups(int maxBackups = 10);
}

// Config sharing
namespace ConfigSharing {
    std::string ExportConfig(const std::string& configName);
    bool ImportConfig(const std::string& configData, const std::string& newName);
    bool ExportToFile(const std::string& configName, const std::string& filepath);
    bool ImportFromFile(const std::string& filepath, const std::string& newName);
    
    // Base64 encoding/decoding for sharing
    std::string EncodeBase64(const std::string& data);
    std::string DecodeBase64(const std::string& encoded);
}

// Config templates
namespace ConfigTemplates {
    struct Template {
        std::string name;
        std::string description;
        std::string author;
        std::string data;
        std::vector<std::string> tags;
    };
    
    std::vector<Template> GetAvailableTemplates();
    bool ApplyTemplate(const Template& tmpl, const std::string& configName);
    bool CreateTemplate(const std::string& configName, const Template& tmpl);
    bool DeleteTemplate(const std::string& templateName);
}

// Macro definitions for easier config access
#define CONFIG_BOOL(key, default) Config::GetBool(key, default)
#define CONFIG_INT(key, default) Config::GetInt(key, default)
#define CONFIG_FLOAT(key, default) Config::GetFloat(key, default)
#define CONFIG_STRING(key, default) Config::GetString(key, default)
#define CONFIG_COLOR(key, default) Config::GetColor(key, default)
#define CONFIG_VECTOR3(key, default) Config::GetVector3(key, default)
#define CONFIG_ANGLES(key, default) Config::GetAngles(key, default)

#define SET_CONFIG_BOOL(key, value) Config::SetBool(key, value)
#define SET_CONFIG_INT(key, value) Config::SetInt(key, value)
#define SET_CONFIG_FLOAT(key, value) Config::SetFloat(key, value)
#define SET_CONFIG_STRING(key, value) Config::SetString(key, value)
#define SET_CONFIG_COLOR(key, value) Config::SetColor(key, value)
#define SET_CONFIG_VECTOR3(key, value) Config::SetVector3(key, value)
#define SET_CONFIG_ANGLES(key, value) Config::SetAngles(key, value)

// Feature-specific config keys
namespace ConfigKeys {
    // Aimbot
    namespace Aimbot {
        constexpr const char* ENABLED = "aimbot.enabled";
        constexpr const char* FOV = "aimbot.fov";
        constexpr const char* SMOOTHNESS = "aimbot.smoothness";
        constexpr const char* HITBOX = "aimbot.hitbox";
        constexpr const char* AUTO_SHOOT = "aimbot.auto_shoot";
        constexpr const char* RCS = "aimbot.rcs";
        constexpr const char* PREDICTION = "aimbot.prediction";
        constexpr const char* VISIBILITY_CHECK = "aimbot.visibility_check";
        constexpr const char* TEAM_CHECK = "aimbot.team_check";
        constexpr const char* KEY = "aimbot.key";
        constexpr const char* KEY_MODE = "aimbot.key_mode";
    }
    
    // ESP
    namespace ESP {
        constexpr const char* ENABLED = "esp.enabled";
        constexpr const char* PLAYERS = "esp.players";
        constexpr const char* WEAPONS = "esp.weapons";
        constexpr const char* BOMBS = "esp.bombs";
        constexpr const char* BOXES = "esp.boxes";
        constexpr const char* NAMES = "esp.names";
        constexpr const char* HEALTH = "esp.health";
        constexpr const char* ARMOR = "esp.armor";
        constexpr const char* DISTANCE = "esp.distance";
        constexpr const char* SKELETON = "esp.skeleton";
        constexpr const char* SNAPLINES = "esp.snaplines";
        constexpr const char* VISIBILITY_CHECK = "esp.visibility_check";
        constexpr const char* TEAM_CHECK = "esp.team_check";
        constexpr const char* ENEMY_COLOR = "esp.enemy_color";
        constexpr const char* TEAM_COLOR = "esp.team_color";
        constexpr const char* WEAPON_COLOR = "esp.weapon_color";
        constexpr const char* BOMB_COLOR = "esp.bomb_color";
    }
    
    // Triggerbot
    namespace Triggerbot {
        constexpr const char* ENABLED = "triggerbot.enabled";
        constexpr const char* DELAY = "triggerbot.delay";
        constexpr const char* VISIBILITY_CHECK = "triggerbot.visibility_check";
        constexpr const char* TEAM_CHECK = "triggerbot.team_check";
        constexpr const char* KEY = "triggerbot.key";
        constexpr const char* KEY_MODE = "triggerbot.key_mode";
        constexpr const char* AUTO_WALL = "triggerbot.auto_wall";
        constexpr const char* MIN_DAMAGE = "triggerbot.min_damage";
    }
    
    // Misc
    namespace Misc {
        constexpr const char* ENABLED = "misc.enabled";
        constexpr const char* BUNNY_HOP = "misc.bunny_hop";
        constexpr const char* AUTO_STRAFE = "misc.auto_strafe";
        constexpr const char* NO_FLASH = "misc.no_flash";
        constexpr const char* NO_SMOKE = "misc.no_smoke";
        constexpr const char* RADAR_HACK = "misc.radar_hack";
        constexpr const char* SPECTATOR_LIST = "misc.spectator_list";
        constexpr const char* WATERMARK = "misc.watermark";
        constexpr const char* FPS_COUNTER = "misc.fps_counter";
        constexpr const char* BHOP_KEY = "misc.bhop_key";
        constexpr const char* STRAFE_SMOOTH = "misc.strafe_smooth";
        constexpr const char* FLASH_ALPHA = "misc.flash_alpha";
    }
    
    // Glow
    namespace Glow {
        constexpr const char* ENABLED = "glow.enabled";
        constexpr const char* PLAYERS = "glow.players";
        constexpr const char* WEAPONS = "glow.weapons";
        constexpr const char* BOMBS = "glow.bombs";
        constexpr const char* TEAM_CHECK = "glow.team_check";
        constexpr const char* ENEMY_COLOR = "glow.enemy_color";
        constexpr const char* TEAM_COLOR = "glow.team_color";
        constexpr const char* WEAPON_COLOR = "glow.weapon_color";
        constexpr const char* BOMB_COLOR = "glow.bomb_color";
        constexpr const char* BRIGHTNESS = "glow.brightness";
        constexpr const char* STYLE = "glow.style";
    }
}
