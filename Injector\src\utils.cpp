#include "pch.h"
#include "utils.h"

// Static member definitions
std::random_device Utils::randomDevice;
std::mt19937 Utils::randomGenerator(Utils::randomDevice());
bool Utils::randomInitialized = false;
std::vector<std::wstring> Utils::blacklistedProcesses;
std::vector<std::wstring> Utils::vmProcesses;
std::vector<std::wstring> Utils::sandboxProcesses;

// String utilities
std::wstring Utils::StringToWString(const std::string& str) {
    if (str.empty()) return std::wstring();
    
    int size = MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, nullptr, 0);
    std::wstring wstr(size, 0);
    MultiByteToWideChar(CP_UTF8, 0, str.c_str(), -1, &wstr[0], size);
    return wstr;
}

std::string Utils::WStringToString(const std::wstring& wstr) {
    if (wstr.empty()) return std::string();
    
    int size = WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, nullptr, 0, nullptr, nullptr);
    std::string str(size, 0);
    WideCharToMultiByte(CP_UTF8, 0, wstr.c_str(), -1, &str[0], size, nullptr, nullptr);
    return str;
}

std::string Utils::ToLower(const std::string& str) {
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::wstring Utils::ToLower(const std::wstring& wstr) {
    std::wstring result = wstr;
    std::transform(result.begin(), result.end(), result.begin(), ::towlower);
    return result;
}

bool Utils::EndsWith(const std::wstring& str, const std::wstring& suffix) {
    if (suffix.length() > str.length()) return false;
    return str.compare(str.length() - suffix.length(), suffix.length(), suffix) == 0;
}

bool Utils::StartsWith(const std::wstring& str, const std::wstring& prefix) {
    if (prefix.length() > str.length()) return false;
    return str.compare(0, prefix.length(), prefix) == 0;
}

// File utilities
bool Utils::FileExists(const std::wstring& filePath) {
    DWORD attributes = GetFileAttributesW(filePath.c_str());
    return attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY);
}

bool Utils::DirectoryExists(const std::wstring& dirPath) {
    DWORD attributes = GetFileAttributesW(dirPath.c_str());
    return attributes != INVALID_FILE_ATTRIBUTES && (attributes & FILE_ATTRIBUTE_DIRECTORY);
}

std::wstring Utils::GetCurrentDirectory() {
    wchar_t buffer[MAX_PATH];
    DWORD length = ::GetCurrentDirectoryW(MAX_PATH, buffer);
    if (length > 0 && length < MAX_PATH) {
        return std::wstring(buffer);
    }
    return L"";
}

std::wstring Utils::GetExecutableDirectory() {
    wchar_t buffer[MAX_PATH];
    DWORD length = GetModuleFileNameW(nullptr, buffer, MAX_PATH);
    if (length > 0) {
        std::wstring path(buffer);
        size_t lastSlash = path.find_last_of(L"\\");
        if (lastSlash != std::wstring::npos) {
            return path.substr(0, lastSlash);
        }
    }
    return L"";
}

std::wstring Utils::GetTempDirectory() {
    wchar_t buffer[MAX_PATH];
    DWORD length = GetTempPathW(MAX_PATH, buffer);
    if (length > 0 && length < MAX_PATH) {
        return std::wstring(buffer);
    }
    return L"";
}

SIZE_T Utils::GetFileSize(const std::wstring& filePath) {
    HANDLE hFile = CreateFileW(filePath.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (hFile == INVALID_HANDLE_VALUE) {
        return 0;
    }
    
    LARGE_INTEGER fileSize;
    if (GetFileSizeEx(hFile, &fileSize)) {
        CloseHandle(hFile);
        return static_cast<SIZE_T>(fileSize.QuadPart);
    }
    
    CloseHandle(hFile);
    return 0;
}

std::vector<BYTE> Utils::ReadFile(const std::wstring& filePath) {
    std::vector<BYTE> data;
    
    HANDLE hFile = CreateFileW(filePath.c_str(), GENERIC_READ, FILE_SHARE_READ, nullptr, OPEN_EXISTING, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (hFile == INVALID_HANDLE_VALUE) {
        return data;
    }
    
    LARGE_INTEGER fileSize;
    if (!GetFileSizeEx(hFile, &fileSize)) {
        CloseHandle(hFile);
        return data;
    }
    
    data.resize(static_cast<size_t>(fileSize.QuadPart));
    
    DWORD bytesRead;
    if (!::ReadFile(hFile, data.data(), static_cast<DWORD>(data.size()), &bytesRead, nullptr)) {
        data.clear();
    }
    
    CloseHandle(hFile);
    return data;
}

bool Utils::WriteFile(const std::wstring& filePath, const std::vector<BYTE>& data) {
    HANDLE hFile = CreateFileW(filePath.c_str(), GENERIC_WRITE, 0, nullptr, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, nullptr);
    if (hFile == INVALID_HANDLE_VALUE) {
        return false;
    }
    
    DWORD bytesWritten;
    bool success = ::WriteFile(hFile, data.data(), static_cast<DWORD>(data.size()), &bytesWritten, nullptr) &&
                   bytesWritten == data.size();
    
    CloseHandle(hFile);
    return success;
}

bool Utils::DeleteFile(const std::wstring& filePath) {
    return DeleteFileW(filePath.c_str()) != 0;
}

// System utilities
std::wstring Utils::GetSystemDirectory() {
    wchar_t buffer[MAX_PATH];
    UINT length = ::GetSystemDirectoryW(buffer, MAX_PATH);
    if (length > 0 && length < MAX_PATH) {
        return std::wstring(buffer);
    }
    return L"";
}

std::wstring Utils::GetWindowsDirectory() {
    wchar_t buffer[MAX_PATH];
    UINT length = ::GetWindowsDirectoryW(buffer, MAX_PATH);
    if (length > 0 && length < MAX_PATH) {
        return std::wstring(buffer);
    }
    return L"";
}

bool Utils::IsWindows64Bit() {
    SYSTEM_INFO si;
    GetNativeSystemInfo(&si);
    return si.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_AMD64 ||
           si.wProcessorArchitecture == PROCESSOR_ARCHITECTURE_IA64;
}

DWORD Utils::GetWindowsVersion() {
    typedef NTSTATUS(WINAPI* RtlGetVersionPtr)(PRTL_OSVERSIONINFOW);
    HMODULE hMod = ::GetModuleHandleW(L"ntdll.dll");
    if (hMod) {
        RtlGetVersionPtr fxPtr = (RtlGetVersionPtr)::GetProcAddress(hMod, "RtlGetVersion");
        if (fxPtr != nullptr) {
            RTL_OSVERSIONINFOW rovi = { 0 };
            rovi.dwOSVersionInfoSize = sizeof(rovi);
            if (fxPtr(&rovi) == 0) {
                return MAKELONG(rovi.dwMinorVersion, rovi.dwMajorVersion);
            }
        }
    }
    return 0;
}

std::wstring Utils::GetComputerName() {
    wchar_t buffer[MAX_COMPUTERNAME_LENGTH + 1];
    DWORD size = MAX_COMPUTERNAME_LENGTH + 1;
    
    if (::GetComputerNameW(buffer, &size)) {
        return std::wstring(buffer);
    }
    
    return L"";
}

std::wstring Utils::GetUserName() {
    wchar_t buffer[UNLEN + 1];
    DWORD size = UNLEN + 1;
    
    if (::GetUserNameW(buffer, &size)) {
        return std::wstring(buffer);
    }
    
    return L"";
}

// Cryptography utilities
std::vector<BYTE> Utils::GenerateRandomBytes(SIZE_T size) {
    InitializeRandom();
    
    std::vector<BYTE> bytes(size);
    std::uniform_int_distribution<int> dist(0, 255);
    
    for (SIZE_T i = 0; i < size; ++i) {
        bytes[i] = static_cast<BYTE>(dist(randomGenerator));
    }
    
    return bytes;
}

DWORD Utils::GenerateRandomDWORD() {
    InitializeRandom();
    
    std::uniform_int_distribution<DWORD> dist;
    return dist(randomGenerator);
}

std::string Utils::GenerateRandomString(SIZE_T length) {
    InitializeRandom();
    
    const std::string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    std::uniform_int_distribution<int> dist(0, static_cast<int>(chars.length() - 1));
    
    std::string result;
    result.reserve(length);
    
    for (SIZE_T i = 0; i < length; ++i) {
        result += chars[dist(randomGenerator)];
    }
    
    return result;
}

std::vector<BYTE> Utils::XOREncrypt(const std::vector<BYTE>& data, const std::vector<BYTE>& key) {
    std::vector<BYTE> result(data.size());
    
    for (size_t i = 0; i < data.size(); ++i) {
        result[i] = data[i] ^ key[i % key.size()];
    }
    
    return result;
}

std::vector<BYTE> Utils::XORDecrypt(const std::vector<BYTE>& data, const std::vector<BYTE>& key) {
    return XOREncrypt(data, key); // XOR is symmetric
}

DWORD Utils::CalculateCRC32(const std::vector<BYTE>& data) {
    // Simplified CRC32 implementation
    DWORD crc = 0xFFFFFFFF;
    
    for (BYTE byte : data) {
        crc ^= byte;
        for (int i = 0; i < 8; ++i) {
            if (crc & 1) {
                crc = (crc >> 1) ^ 0xEDB88320;
            } else {
                crc >>= 1;
            }
        }
    }
    
    return crc ^ 0xFFFFFFFF;
}

std::string Utils::CalculateMD5(const std::vector<BYTE>& data) {
    // Simplified MD5 placeholder - in real implementation use proper crypto library
    DWORD hash = CalculateCRC32(data);
    char buffer[33];
    sprintf_s(buffer, "%08x%08x%08x%08x", hash, hash ^ 0x12345678, hash ^ 0x87654321, hash ^ 0xABCDEF00);
    return std::string(buffer);
}

// Time utilities
DWORD Utils::GetCurrentTimestamp() {
    return static_cast<DWORD>(time(nullptr));
}

std::string Utils::GetCurrentTimeString() {
    time_t now = time(nullptr);
    char buffer[26];
    ctime_s(buffer, sizeof(buffer), &now);
    
    // Remove newline
    std::string result(buffer);
    if (!result.empty() && result.back() == '\n') {
        result.pop_back();
    }
    
    return result;
}

void Utils::Sleep(DWORD milliseconds) {
    ::Sleep(milliseconds);
}

void Utils::RandomSleep(DWORD minMs, DWORD maxMs) {
    if (minMs >= maxMs) return;

    DWORD delay = minMs + (GenerateRandomDWORD() % (maxMs - minMs));
    Sleep(delay);
}

// Anti-detection utilities
bool Utils::IsRunningInVM() {
    // Check for VM indicators
    std::vector<std::wstring> vmIndicators = {
        L"vmware", L"virtualbox", L"vbox", L"qemu", L"xen", L"hyper-v", L"parallels"
    };

    // Check running processes
    auto processes = GetRunningProcesses();
    for (const auto& process : processes) {
        std::wstring lowerProcess = ToLower(process);
        for (const auto& indicator : vmIndicators) {
            if (lowerProcess.find(indicator) != std::wstring::npos) {
                return true;
            }
        }
    }

    // Check registry keys
    std::wstring value;
    if (ReadRegistryString(HKEY_LOCAL_MACHINE, L"HARDWARE\\DESCRIPTION\\System", L"SystemBiosVersion", value)) {
        std::wstring lowerValue = ToLower(value);
        for (const auto& indicator : vmIndicators) {
            if (lowerValue.find(indicator) != std::wstring::npos) {
                return true;
            }
        }
    }

    return false;
}

bool Utils::IsRunningInSandbox() {
    // Check for sandbox indicators
    std::vector<std::wstring> sandboxIndicators = {
        L"sandboxie", L"cuckoo", L"anubis", L"joebox", L"threatanalyzer"
    };

    auto processes = GetRunningProcesses();
    for (const auto& process : processes) {
        std::wstring lowerProcess = ToLower(process);
        for (const auto& indicator : sandboxIndicators) {
            if (lowerProcess.find(indicator) != std::wstring::npos) {
                return true;
            }
        }
    }

    // Check for low system resources (common in sandboxes)
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus)) {
        // Less than 2GB RAM might indicate sandbox
        if (memStatus.ullTotalPhys < 2ULL * 1024 * 1024 * 1024) {
            return true;
        }
    }

    return false;
}

bool Utils::IsBeingDebugged() {
    // Check for debugger presence
    if (IsDebuggerPresent()) {
        return true;
    }

    // Check for remote debugger
    BOOL isDebugged = FALSE;
    CheckRemoteDebuggerPresent(GetCurrentProcess(), &isDebugged);
    if (isDebugged) {
        return true;
    }

    // Check for debug heap
    HANDLE hHeap = GetProcessHeap();
    ULONG heapFlags = *(ULONG*)((BYTE*)hHeap + 0x70);
    if (heapFlags & 0x00000002) { // HEAP_VALIDATE_PARAMETERS_ENABLED
        return true;
    }

    return false;
}

bool Utils::IsAnalysisToolPresent() {
    std::vector<std::wstring> analysisTools = {
        L"ollydbg.exe", L"x64dbg.exe", L"windbg.exe", L"ida.exe", L"ida64.exe",
        L"ghidra.exe", L"processhacker.exe", L"procmon.exe", L"wireshark.exe"
    };

    auto processes = GetRunningProcesses();
    for (const auto& process : processes) {
        std::wstring lowerProcess = ToLower(process);
        for (const auto& tool : analysisTools) {
            if (lowerProcess.find(tool) != std::wstring::npos) {
                return true;
            }
        }
    }

    return false;
}

std::vector<std::wstring> Utils::GetRunningProcesses() {
    std::vector<std::wstring> processes;

    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return processes;
    }

    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);

    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            processes.push_back(pe32.szExeFile);
        } while (Process32NextW(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return processes;
}

bool Utils::IsBlacklistedProcessRunning() {
    if (blacklistedProcesses.empty()) {
        // Initialize blacklist
        blacklistedProcesses = {
            L"ollydbg.exe", L"x64dbg.exe", L"windbg.exe", L"ida.exe", L"ida64.exe",
            L"ghidra.exe", L"processhacker.exe", L"procmon.exe", L"wireshark.exe",
            L"fiddler.exe", L"httpdebugger.exe", L"regmon.exe", L"filemon.exe"
        };
    }

    auto processes = GetRunningProcesses();
    for (const auto& process : processes) {
        std::wstring lowerProcess = ToLower(process);
        for (const auto& blacklisted : blacklistedProcesses) {
            if (lowerProcess == blacklisted) {
                return true;
            }
        }
    }

    return false;
}

// Memory utilities
void Utils::SecureZeroMemory(void* ptr, SIZE_T size) {
    volatile char* p = static_cast<volatile char*>(ptr);
    while (size--) {
        *p++ = 0;
    }
}

bool Utils::IsValidPointer(const void* ptr) {
    if (!ptr) return false;

    __try {
        volatile char test = *static_cast<const volatile char*>(ptr);
        return true;
    }
    __except (EXCEPTION_EXECUTE_HANDLER) {
        return false;
    }
}

LPVOID Utils::AlignToPage(LPVOID address) {
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    return (LPVOID)((ULONG_PTR)address & ~(si.dwPageSize - 1));
}

SIZE_T Utils::AlignToPage(SIZE_T size) {
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    return (size + si.dwPageSize - 1) & ~(si.dwPageSize - 1);
}

DWORD Utils::GetPageSize() {
    SYSTEM_INFO si;
    GetSystemInfo(&si);
    return si.dwPageSize;
}

// Error handling
std::string Utils::GetLastErrorString() {
    return GetErrorString(GetLastError());
}

std::string Utils::GetErrorString(DWORD errorCode) {
    char* messageBuffer = nullptr;

    DWORD size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr, errorCode, MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        (LPSTR)&messageBuffer, 0, nullptr);

    std::string message;
    if (size > 0 && messageBuffer) {
        message = std::string(messageBuffer, size);
        // Remove trailing newlines
        while (!message.empty() && (message.back() == '\n' || message.back() == '\r')) {
            message.pop_back();
        }
    } else {
        message = "Unknown error (" + std::to_string(errorCode) + ")";
    }

    if (messageBuffer) {
        LocalFree(messageBuffer);
    }

    return message;
}

void Utils::LogError(const std::string& function, DWORD errorCode) {
    std::string errorMsg = function + " failed with error: " + std::to_string(errorCode) +
                          " (" + GetErrorString(errorCode) + ")";
    ERROR(errorMsg);
}

// Console utilities
void Utils::SetConsoleTitle(const std::wstring& title) {
    ::SetConsoleTitleW(title.c_str());
}

void Utils::SetConsoleColor(WORD color) {
    HANDLE hConsole = GetStdHandle(STD_OUTPUT_HANDLE);
    SetConsoleTextAttribute(hConsole, color);
}

void Utils::ClearConsole() {
    system("cls");
}

bool Utils::AllocateConsole() {
    return ::AllocConsole() != 0;
}

void Utils::FreeConsole() {
    ::FreeConsole();
}

void Utils::InitializeRandom() {
    if (!randomInitialized) {
        randomGenerator.seed(randomDevice());
        randomInitialized = true;
    }
}

// Registry utilities
bool Utils::ReadRegistryString(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, std::wstring& value) {
    HKEY hSubKey;
    if (RegOpenKeyExW(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return false;
    }

    DWORD dataSize = 0;
    DWORD dataType;

    // Get required buffer size
    if (RegQueryValueExW(hSubKey, valueName.c_str(), nullptr, &dataType, nullptr, &dataSize) != ERROR_SUCCESS ||
        dataType != REG_SZ) {
        RegCloseKey(hSubKey);
        return false;
    }

    // Read the value
    std::vector<wchar_t> buffer(dataSize / sizeof(wchar_t));
    if (RegQueryValueExW(hSubKey, valueName.c_str(), nullptr, nullptr,
                        reinterpret_cast<LPBYTE>(buffer.data()), &dataSize) == ERROR_SUCCESS) {
        value = buffer.data();
        RegCloseKey(hSubKey);
        return true;
    }

    RegCloseKey(hSubKey);
    return false;
}

bool Utils::WriteRegistryString(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, const std::wstring& value) {
    HKEY hSubKey;
    if (RegCreateKeyExW(hKey, subKey.c_str(), 0, nullptr, REG_OPTION_NON_VOLATILE,
                       KEY_WRITE, nullptr, &hSubKey, nullptr) != ERROR_SUCCESS) {
        return false;
    }

    DWORD dataSize = static_cast<DWORD>((value.length() + 1) * sizeof(wchar_t));
    bool success = RegSetValueExW(hSubKey, valueName.c_str(), 0, REG_SZ,
                                 reinterpret_cast<const BYTE*>(value.c_str()), dataSize) == ERROR_SUCCESS;

    RegCloseKey(hSubKey);
    return success;
}

bool Utils::ReadRegistryDWORD(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, DWORD& value) {
    HKEY hSubKey;
    if (RegOpenKeyExW(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey) != ERROR_SUCCESS) {
        return false;
    }

    DWORD dataSize = sizeof(DWORD);
    DWORD dataType;

    bool success = RegQueryValueExW(hSubKey, valueName.c_str(), nullptr, &dataType,
                                   reinterpret_cast<LPBYTE>(&value), &dataSize) == ERROR_SUCCESS &&
                   dataType == REG_DWORD;

    RegCloseKey(hSubKey);
    return success;
}

bool Utils::WriteRegistryDWORD(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, DWORD value) {
    HKEY hSubKey;
    if (RegCreateKeyExW(hKey, subKey.c_str(), 0, nullptr, REG_OPTION_NON_VOLATILE,
                       KEY_WRITE, nullptr, &hSubKey, nullptr) != ERROR_SUCCESS) {
        return false;
    }

    bool success = RegSetValueExW(hSubKey, valueName.c_str(), 0, REG_DWORD,
                                 reinterpret_cast<const BYTE*>(&value), sizeof(DWORD)) == ERROR_SUCCESS;

    RegCloseKey(hSubKey);
    return success;
}

bool Utils::DeleteRegistryKey(HKEY hKey, const std::wstring& subKey) {
    return RegDeleteKeyW(hKey, subKey.c_str()) == ERROR_SUCCESS;
}

// Network utilities (simplified implementations)
bool Utils::IsInternetConnected() {
    return InternetCheckConnectionW(L"http://www.google.com", FLAG_ICC_FORCE_CONNECTION, 0) != 0;
}

std::string Utils::DownloadString(const std::string& url) {
    // Simplified implementation - in real scenario use proper HTTP library
    return "";
}

std::vector<BYTE> Utils::DownloadFile(const std::string& url) {
    // Simplified implementation - in real scenario use proper HTTP library
    return std::vector<BYTE>();
}

std::string Utils::GetPublicIP() {
    // Simplified implementation - in real scenario use proper HTTP library
    return "";
}

// Hardware utilities (simplified implementations)
std::string Utils::GetCPUInfo() {
    int cpuInfo[4];
    __cpuid(cpuInfo, 0);

    char vendor[13];
    memcpy(vendor, &cpuInfo[1], 4);
    memcpy(vendor + 4, &cpuInfo[3], 4);
    memcpy(vendor + 8, &cpuInfo[2], 4);
    vendor[12] = '\0';

    return std::string(vendor);
}

std::string Utils::GetGPUInfo() {
    // Simplified implementation - would need DirectX or OpenGL
    return "Unknown GPU";
}

DWORD Utils::GetRAMSize() {
    MEMORYSTATUSEX memStatus;
    memStatus.dwLength = sizeof(memStatus);
    if (GlobalMemoryStatusEx(&memStatus)) {
        return static_cast<DWORD>(memStatus.ullTotalPhys / (1024 * 1024)); // MB
    }
    return 0;
}

std::string Utils::GetHardDriveSerial() {
    // Simplified implementation - would need WMI
    return "Unknown";
}

std::string Utils::GetMACAddress() {
    // Simplified implementation - would need to enumerate network adapters
    return "Unknown";
}

std::string Utils::GetHWID() {
    // Generate a simple HWID based on available info
    std::string cpu = GetCPUInfo();
    DWORD ram = GetRAMSize();
    std::string computer = WStringToString(GetComputerName());

    std::string combined = cpu + std::to_string(ram) + computer;
    DWORD hash = CalculateCRC32(std::vector<BYTE>(combined.begin(), combined.end()));

    char buffer[16];
    sprintf_s(buffer, "%08X", hash);
    return std::string(buffer);
}

bool Utils::IsRunningAsAdmin() {
    HANDLE hToken;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken)) {
        return false;
    }

    TOKEN_ELEVATION elevation;
    DWORD size;
    bool isElevated = false;

    if (GetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &size)) {
        isElevated = elevation.TokenIsElevated != 0;
    }

    CloseHandle(hToken);
    return isElevated;
}
