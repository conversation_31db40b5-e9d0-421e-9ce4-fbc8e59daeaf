@echo off
echo Building Skywere.dll with Visual Studio 2019...
echo.

REM Set up Visual Studio 2019 environment
call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo Error: Could not set up Visual Studio 2019 environment
    echo Make sure Visual Studio 2019 is installed with C++ workload
    pause
    exit /b 1
)

echo Building Release configuration...
msbuild Skywere.vcxproj /p:Configuration=Release /p:Platform=x64 /verbosity:minimal

if errorlevel 1 (
    echo.
    echo Build failed! Check the errors above.
    echo.
    echo Common solutions:
    echo 1. Make sure Visual Studio 2019 is installed with C++ workload
    echo 2. Install Windows 10 SDK
    echo 3. Install MSVC v142 compiler toolset
    pause
    exit /b 1
) else (
    echo.
    echo Build successful!
    if exist "x64\Release\Skywere.dll" (
        echo DLL created: x64\Release\Skywere.dll
        echo File size: 
        dir "x64\Release\Skywere.dll" | find ".dll"
        echo.
        echo Ready to use with injector!
    ) else (
        echo Warning: DLL not found in expected location
    )
)

echo.
pause
