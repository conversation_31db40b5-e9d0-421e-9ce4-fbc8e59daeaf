#pragma once

// GUI Manager
class GUI {
public:
    static bool Initialize(HWND hwnd);
    static void Shutdown();
    static void Render();
    static void HandleInput(UINT msg, WPARAM wParam, LPARAM lParam);
    
    static bool IsVisible();
    static void SetVisible(bool state) { visible = state; }
    static void Toggle();
    
private:
    static bool initialized;
    static bool visible;
    static HWND gameWindow;
    static ID3D11Device* device;
    static ID3D11DeviceContext* context;
    static IDXGISwapChain* swapChain;
    static ID3D11RenderTargetView* renderTargetView;
    
    // GUI rendering
    static void RenderMainMenu();
    static void RenderAimbotTab();
    static void RenderESPTab();
    static void RenderTriggerbotTab();
    static void RenderMiscTab();
    static void RenderGlowTab();
    static void RenderConfigTab();
    
    // Utility functions
    static void SetupStyle();
    static void HelpMarker(const char* desc);
    static bool ColorEdit4(const char* label, ImVec4& color);
    static bool KeyBind(const char* label, int& key);
    
    // D3D11 setup
    static bool SetupD3D11();
    static void CleanupD3D11();
    static bool CreateRenderTarget();
    static void CleanupRenderTarget();
};

// Overlay for ESP rendering
class Overlay {
public:
    static bool Initialize();
    static void Shutdown();
    static void BeginFrame();
    static void EndFrame();
    
    // Drawing functions
    static void DrawLine(const Vector2& from, const Vector2& to, const ImVec4& color, float thickness = 1.0f);
    static void DrawBox(const Vector2& min, const Vector2& max, const ImVec4& color, float thickness = 1.0f);
    static void DrawFilledBox(const Vector2& min, const Vector2& max, const ImVec4& color);
    static void DrawCircle(const Vector2& center, float radius, const ImVec4& color, float thickness = 1.0f);
    static void DrawFilledCircle(const Vector2& center, float radius, const ImVec4& color);
    static void DrawText(const Vector2& position, const std::string& text, const ImVec4& color, float fontSize = 12.0f);
    static void DrawTextCentered(const Vector2& position, const std::string& text, const ImVec4& color, float fontSize = 12.0f);
    
    // Utility
    static Vector2 GetTextSize(const std::string& text, float fontSize = 12.0f);
    static ImDrawList* GetDrawList() { return drawList; }
    
private:
    static bool initialized;
    static ImDrawList* drawList;
    static ImFont* defaultFont;
    static ImFont* boldFont;
    
    static void SetupFonts();
};

// Config GUI
class ConfigGUI {
public:
    static void Render();
    
private:
    static void RenderConfigList();
    static void RenderConfigActions();
    static void RenderConfigEditor();
    
    static std::vector<std::string> configFiles;
    static std::string selectedConfig;
    static std::string newConfigName;
    static bool showCreateDialog;
    static bool showDeleteDialog;
    
    static void RefreshConfigList();
    static void LoadConfig(const std::string& name);
    static void SaveConfig(const std::string& name);
    static void DeleteConfig(const std::string& name);
    static void CreateConfig(const std::string& name);
};

// Color schemes
namespace ColorSchemes {
    extern ImVec4 Primary;
    extern ImVec4 Secondary;
    extern ImVec4 Accent;
    extern ImVec4 Background;
    extern ImVec4 Text;
    extern ImVec4 TextDisabled;
    extern ImVec4 Border;
    extern ImVec4 Success;
    extern ImVec4 Warning;
    extern ImVec4 Error;
    
    void ApplyDarkTheme();
    void ApplyLightTheme();
    void ApplyCustomTheme();
}

// GUI utilities
namespace GUIUtils {
    // Input helpers
    bool InputFloat(const char* label, float* value, float min = 0.0f, float max = 100.0f, const char* format = "%.1f");
    bool InputInt(const char* label, int* value, int min = 0, int max = 100);
    bool SliderFloat(const char* label, float* value, float min, float max, const char* format = "%.1f");
    bool SliderInt(const char* label, int* value, int min, int max);
    bool Checkbox(const char* label, bool* value);
    bool Button(const char* label, const ImVec2& size = ImVec2(0, 0));
    
    // Layout helpers
    void SameLine(float offset = 0.0f);
    void Separator();
    void Spacing();
    void NewLine();
    void Indent(float width = 0.0f);
    void Unindent(float width = 0.0f);
    
    // Text helpers
    void Text(const char* text);
    void TextColored(const ImVec4& color, const char* text);
    void TextCentered(const char* text);
    void TextRight(const char* text);
    
    // Window helpers
    bool BeginChild(const char* id, const ImVec2& size = ImVec2(0, 0), bool border = false);
    void EndChild();
    bool BeginTabBar(const char* id);
    void EndTabBar();
    bool BeginTabItem(const char* label);
    void EndTabItem();
    
    // Popup helpers
    bool BeginPopup(const char* id);
    void EndPopup();
    void OpenPopup(const char* id);
    bool BeginPopupModal(const char* name, bool* open = nullptr);
    void EndPopupModal();
    
    // Style helpers
    inline void PushStyleColor(ImGuiCol idx, const ImVec4& color) { ImGui::PushStyleColor(idx, color); }
    inline void PopStyleColor(int count = 1) { ImGui::PopStyleColor(count); }
    inline void PushStyleVar(ImGuiStyleVar idx, float val) { ImGui::PushStyleVar(idx, val); }
    inline void PushStyleVar(ImGuiStyleVar idx, const ImVec2& val) { ImGui::PushStyleVar(idx, val); }
    inline void PopStyleVar(int count = 1) { ImGui::PopStyleVar(count); }
}

// Key names for display
namespace KeyNames {
    std::string GetKeyName(int key);
    int GetKeyFromName(const std::string& name);
    std::vector<std::pair<std::string, int>> GetAllKeys();
}

// Animation system
class AnimationSystem {
public:
    static void Update();
    
    // Animation types
    static float EaseInOut(float t);
    static float EaseIn(float t);
    static float EaseOut(float t);
    static float Bounce(float t);
    static float Elastic(float t);
    
    // Animated values
    class AnimatedFloat {
    public:
        AnimatedFloat(float initial = 0.0f, float speed = 5.0f);
        void SetTarget(float target);
        void SetSpeed(float speed);
        void Update(float deltaTime);
        float GetValue() const { return current; }
        bool IsAnimating() const { return std::abs(current - target) > 0.001f; }
        
    private:
        float current;
        float target;
        float speed;
    };
    
    class AnimatedColor {
    public:
        AnimatedColor(const ImVec4& initial = ImVec4(0, 0, 0, 0), float speed = 5.0f);
        void SetTarget(const ImVec4& target);
        void SetSpeed(float speed);
        void Update(float deltaTime);
        ImVec4 GetValue() const { return current; }
        bool IsAnimating() const;
        
    private:
        ImVec4 current;
        ImVec4 target;
        float speed;
    };
    
private:
    static std::chrono::steady_clock::time_point lastUpdate;
    static float deltaTime;
};
