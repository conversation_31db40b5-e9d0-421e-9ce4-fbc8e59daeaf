#pragma once
#include <windows.h>
#include <string>
#include <vector>

class ManualMapper {
public:
    static bool MapDLL(HANDLE hProcess, const std::wstring& dllPath);
    static bool MapDLL(HANDLE hProcess, const std::vector<BYTE>& dllData);
    
private:
    struct ManualMapData {
        LPVOID imageBase;
        HMODULE(WINAPI* fnLoadLibraryA)(LPCSTR);
        FARPROC(WINAPI* fnGetProcAddress)(HMODULE, LPCSTR);
        BOOL(WINAPI* fnDllMain)(HMODULE, DWORD, LPVOID);
        LPVOID shellcode;
        SIZE_T shellcodeSize;
    };
    
    // Core mapping functions
    static LPVOID MapImage(HANDLE hProcess, const std::vector<BYTE>& dllData);
    static bool ResolveImports(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool RelocateImage(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool ExecuteEntryPoint(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    
    // PE parsing functions
    static PIMAGE_NT_HEADERS GetNtHeaders(const std::vector<BYTE>& dllData);
    static PIMAGE_SECTION_HEADER GetSectionHeader(const std::vector<BYTE>& dllData, const char* sectionName);
    static PIMAGE_IMPORT_DESCRIPTOR GetImportDescriptor(const std::vector<BYTE>& dllData);
    static PIMAGE_BASE_RELOCATION GetRelocationTable(const std::vector<BYTE>& dllData);
    
    // Utility functions
    static std::vector<BYTE> ReadFileToMemory(const std::wstring& filePath);
    static bool ValidatePE(const std::vector<BYTE>& dllData);
    static DWORD RVAToOffset(const std::vector<BYTE>& dllData, DWORD rva);
    static LPVOID GetProcAddressEx(HANDLE hProcess, HMODULE hModule, const char* procName);
    
    // Shellcode for manual mapping
    static DWORD WINAPI MappingShellcode(ManualMapData* data);
    static void ShellcodeEnd();
    
    // Advanced features
    static bool FixRelocations(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData, LPVOID preferredBase);
    static bool ProcessImportTable(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool CallTlsCallbacks(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool SetupExceptionHandlers(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    
    // Evasion techniques
    static bool RandomizeImageBase(HANDLE hProcess, LPVOID& imageBase, SIZE_T imageSize);
    static bool EncryptSections(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool ObfuscateHeaders(HANDLE hProcess, LPVOID imageBase);
    static bool HideFromModuleList(HANDLE hProcess, LPVOID imageBase);
    
    // Error handling
    static void LogMappingError(const std::string& function, DWORD errorCode);
    static bool ValidateMapping(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
};

// Reflective DLL injection
class ReflectiveMapper {
public:
    static bool InjectReflectiveDLL(HANDLE hProcess, const std::vector<BYTE>& dllData);
    
private:
    static DWORD FindReflectiveLoader(const std::vector<BYTE>& dllData);
    static bool ExecuteReflectiveLoader(HANDLE hProcess, LPVOID dllBase, DWORD loaderOffset);
};

// Advanced manual mapping with anti-detection
class StealthMapper : public ManualMapper {
public:
    static bool StealthMapDLL(HANDLE hProcess, const std::wstring& dllPath);
    
private:
    static bool ApplyStealthTechniques(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData);
    static bool ModifyPEB(HANDLE hProcess, LPVOID imageBase);
    static bool HookNtQueryVirtualMemory(HANDLE hProcess, LPVOID imageBase);
    static bool CreateFakeModule(HANDLE hProcess, LPVOID imageBase);
};
