#pragma once

// Forward declarations
class C_BaseEntity;
class C_BasePlayerPawn;
class C_CSPlayerPawn;
class C_CSPlayerController;
class C_CSWeaponBase;

// Entity classes
class C_BaseEntity {
public:
    uintptr_t GetAddress() const { return reinterpret_cast<uintptr_t>(this); }
    
    // Basic entity info
    int GetHealth() const;
    int GetMaxHealth() const;
    int GetTeamNum() const;
    Vector3 GetOrigin() const;
    QAngle GetAngles() const;
    Vector3 GetVelocity() const;
    bool IsAlive() const;
    bool IsDormant() const;
    
    // Entity relationships
    EntityHandle GetOwnerHandle() const;
    C_BaseEntity* GetOwner() const;
    
    // Utility
    float DistanceTo(const Vector3& point) const;
    float DistanceTo(const C_BaseEntity* other) const;
    
protected:
    template<typename T>
    T GetValue(std::ptrdiff_t offset) const {
        return Memory::Read<T>(GetAddress() + offset);
    }
    
    template<typename T>
    void SetValue(std::ptrdiff_t offset, const T& value) const {
        Memory::Write<T>(GetAddress() + offset, value);
    }
};

class C_BasePlayerPawn : public C_BaseEntity {
public:
    // Player-specific info
    EntityHandle GetControllerHandle() const;
    C_CSPlayerController* GetController() const;
    
    // Weapon info
    EntityHandle GetActiveWeaponHandle() const;
    C_CSWeaponBase* GetActiveWeapon() const;
    
    // Movement
    Vector3 GetEyePosition() const;
    QAngle GetViewAngles() const;
    void SetViewAngles(const QAngle& angles);
    
    // Flags
    int GetFlags() const;
    bool IsOnGround() const;
    bool IsInAir() const;
    bool IsDucking() const;
};

class C_CSPlayerPawn : public C_BasePlayerPawn {
public:
    // CS-specific info
    bool IsScoped() const;
    bool IsDefusing() const;
    bool IsGrabbingHostage() const;
    int GetShotsFired() const;
    int GetArmorValue() const;
    bool HasDefuser() const;
    bool HasHelmet() const;
    bool IsInBombZone() const;
    bool IsInBuyZone() const;
    
    // Flash info
    float GetFlashDuration() const;
    float GetFlashMaxAlpha() const;
    bool IsFlashed() const;
    
    // Utility
    bool IsEnemy(const C_CSPlayerPawn* other) const;
    bool IsVisible() const;
    Vector3 GetBonePosition(int boneId) const;
    Vector3 GetHeadPosition() const;
};

class C_CSPlayerController : public C_BaseEntity {
public:
    // Player info
    std::string GetPlayerName() const;
    EntityHandle GetPawnHandle() const;
    C_CSPlayerPawn* GetPawn() const;
    
    // Pawn state (when pawn is not accessible)
    bool IsPawnAlive() const;
    int GetPawnHealth() const;
    int GetPawnArmor() const;
    bool PawnHasDefuser() const;
    bool PawnHasHelmet() const;
    
    // Utility
    bool IsValid() const;
    bool IsBot() const;
};

class C_CSWeaponBase : public C_BaseEntity {
public:
    // Weapon info
    int GetClip1() const;
    int GetClip2() const;
    int GetReserveAmmo() const;
    
    // Attack timing
    int GetNextPrimaryAttackTick() const;
    float GetNextPrimaryAttackTickRatio() const;
    int GetNextSecondaryAttackTick() const;
    float GetNextSecondaryAttackTickRatio() const;
    
    // Utility
    bool CanShoot() const;
    bool IsReloading() const;
    std::string GetWeaponName() const;
};

// Entity management
class EntityManager {
public:
    static bool Initialize();
    static void Update();
    
    // Entity access
    static C_CSPlayerPawn* GetLocalPlayer();
    static C_CSPlayerController* GetLocalPlayerController();
    static std::vector<C_CSPlayerPawn*> GetPlayers();
    static std::vector<C_CSPlayerPawn*> GetEnemies();
    static std::vector<C_CSPlayerPawn*> GetTeammates();
    
    // Entity list access
    static C_BaseEntity* GetEntityByIndex(int index);
    static C_BaseEntity* GetEntityByHandle(EntityHandle handle);
    static int GetHighestEntityIndex();
    
    // Utility
    static bool IsValidEntity(C_BaseEntity* entity);
    static bool IsPlayer(C_BaseEntity* entity);
    
private:
    static uintptr_t entityListBase;
    static C_CSPlayerPawn* localPlayer;
    static C_CSPlayerController* localPlayerController;
    static std::vector<C_CSPlayerPawn*> players;
    static std::mutex playersMutex;
    
    static void UpdateLocalPlayer();
    static void UpdatePlayers();
    static C_BaseEntity* GetEntityFromListEntry(uintptr_t listEntry);
};

// Bone IDs for CS2
enum BoneId : int {
    BONE_HEAD = 6,
    BONE_NECK = 5,
    BONE_SPINE = 4,
    BONE_PELVIS = 0,
    BONE_LEFT_SHOULDER = 8,
    BONE_RIGHT_SHOULDER = 13,
    BONE_LEFT_ELBOW = 9,
    BONE_RIGHT_ELBOW = 14,
    BONE_LEFT_HAND = 11,
    BONE_RIGHT_HAND = 16,
    BONE_LEFT_KNEE = 23,
    BONE_RIGHT_KNEE = 26,
    BONE_LEFT_FOOT = 24,
    BONE_RIGHT_FOOT = 27
};

// Team IDs
enum TeamId : int {
    TEAM_NONE = 0,
    TEAM_SPECTATOR = 1,
    TEAM_TERRORIST = 2,
    TEAM_COUNTER_TERRORIST = 3
};

// Player flags
enum PlayerFlags : int {
    FL_ONGROUND = (1 << 0),
    FL_DUCKING = (1 << 1),
    FL_WATERJUMP = (1 << 2),
    FL_ONTRAIN = (1 << 3),
    FL_INRAIN = (1 << 4),
    FL_FROZEN = (1 << 5),
    FL_ATCONTROLS = (1 << 6),
    FL_CLIENT = (1 << 7),
    FL_FAKECLIENT = (1 << 8),
    FL_INWATER = (1 << 9)
};
