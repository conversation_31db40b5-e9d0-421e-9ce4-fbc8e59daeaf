// Dear ImGui: standalone example application for DirectX 11
// Minimal ImGui header for compilation

#pragma once

#include <windows.h>
#include <d3d11.h>

// Forward declarations
struct ImVec2 { float x, y; ImVec2() { x = y = 0.0f; } ImVec2(float _x, float _y) { x = _x; y = _y; } };
struct ImVec4 { float x, y, z, w; ImVec4() { x = y = z = w = 0.0f; } ImVec4(float _x, float _y, float _z, float _w) { x = _x; y = _y; z = _z; w = _w; } };

// Additional ImGui types
struct ImDrawList {};
struct ImFont {};
struct ImFontAtlas {};

// Forward declarations
struct ImDrawData;
typedef void* ImTextureID;
typedef unsigned int ImU32;
typedef unsigned short ImWchar;
typedef int ImGuiKey;
typedef int ImGuiMouseButton;
typedef int ImGuiWindowFlags;
typedef int ImGuiTreeNodeFlags;
typedef int ImGuiConfigFlags;
typedef int ImGuiBackendFlags;

// Key mapping
enum ImGuiKey_ {
    ImGuiKey_Tab,
    ImGuiKey_LeftArrow,
    ImGuiKey_RightArrow,
    ImGuiKey_UpArrow,
    ImGuiKey_DownArrow,
    ImGuiKey_PageUp,
    ImGuiKey_PageDown,
    ImGuiKey_Home,
    ImGuiKey_End,
    ImGuiKey_Insert,
    ImGuiKey_Delete,
    ImGuiKey_Backspace,
    ImGuiKey_Space,
    ImGuiKey_Enter,
    ImGuiKey_Escape,
    ImGuiKey_KeyPadEnter,
    ImGuiKey_A,
    ImGuiKey_C,
    ImGuiKey_V,
    ImGuiKey_X,
    ImGuiKey_Y,
    ImGuiKey_Z,
    ImGuiKey_COUNT
};

// Backend flags
enum ImGuiBackendFlags_ {
    ImGuiBackendFlags_None                  = 0,
    ImGuiBackendFlags_HasGamepad            = 1 << 0,
    ImGuiBackendFlags_HasMouseCursors       = 1 << 1,
    ImGuiBackendFlags_HasSetMousePos        = 1 << 2,
    ImGuiBackendFlags_RendererHasVtxOffset  = 1 << 3,
};

// ImGuiIO structure
struct ImGuiIO {
    ImGuiConfigFlags ConfigFlags;
    ImGuiBackendFlags BackendFlags;
    ImVec2 DisplaySize;
    float DeltaTime;
    float IniSavingRate;
    const char* IniFilename;
    const char* LogFilename;
    float MouseDoubleClickTime;
    float MouseDoubleClickMaxDist;
    float MouseDragThreshold;
    int KeyMap[ImGuiKey_COUNT];
    float KeyRepeatDelay;
    float KeyRepeatRate;
    void* UserData;

    ImFontAtlas* Fonts;
    float FontGlobalScale;
    bool FontAllowUserScaling;
    ImFont* FontDefault;
    ImVec2 FontDisplayOffset;

    bool MouseDrawCursor;
    bool ConfigMacOSXBehaviors;
    bool ConfigInputTextCursorBlink;
    bool ConfigWindowsResizeFromEdges;
    bool ConfigWindowsMoveFromTitleBarOnly;
    float ConfigMemoryCompactTimer;

    const char* BackendPlatformName;
    const char* BackendRendererName;
    void* BackendPlatformUserData;
    void* BackendRendererUserData;
    void* BackendLanguageUserData;

    ImVec2 MousePos;
    bool MouseDown[5];
    float MouseWheel;
    float MouseWheelH;
    bool KeyCtrl;
    bool KeyShift;
    bool KeyAlt;
    bool KeySuper;
    bool KeysDown[512];
    float NavInputs[16];

    bool WantCaptureMouse;
    bool WantCaptureKeyboard;
    bool WantTextInput;
    bool WantSetMousePos;
    bool WantSaveIniSettings;
    float Framerate;
    int MetricsRenderVertices;
    int MetricsRenderIndices;
    int MetricsRenderWindows;
    int MetricsActiveWindows;
    int MetricsActiveAllocations;
    ImVec2 MouseDelta;

    // Functions
    void AddInputCharacterUTF16(ImWchar c) { (void)c; }
    void SetTexID(ImTextureID id) { (void)id; }
    void GetTexDataAsRGBA32(unsigned char** pixels, int* width, int* height) {
        static unsigned char data[4] = {255,255,255,255};
        *pixels = data; *width = 1; *height = 1;
    }

    ImGuiIO() { memset(this, 0, sizeof(*this)); }
};

// Draw data
struct ImDrawData {
    bool Valid;
    ImDrawList** CmdLists;
    int CmdListsCount;
    int TotalIdxCount;
    int TotalVtxCount;
    ImVec2 DisplayPos;
    ImVec2 DisplaySize;
    ImVec2 FramebufferScale;

    ImDrawData() { Clear(); }
    void Clear() { memset(this, 0, sizeof(*this)); }
};

// ImGui enums
typedef int ImGuiCol;
typedef int ImGuiStyleVar;
typedef int ImGuiCond;

enum ImGuiCol_ {
    ImGuiCol_Text,
    ImGuiCol_Button,
    ImGuiCol_ButtonHovered,
    ImGuiCol_ButtonActive,
    ImGuiCol_WindowBg,
    ImGuiCol_ChildBg,
    ImGuiCol_PopupBg,
    ImGuiCol_Border,
    ImGuiCol_BorderShadow,
    ImGuiCol_FrameBg,
    ImGuiCol_FrameBgHovered,
    ImGuiCol_FrameBgActive
};

enum ImGuiStyleVar_ {
    ImGuiStyleVar_Alpha,
    ImGuiStyleVar_WindowPadding,
    ImGuiStyleVar_WindowRounding,
    ImGuiStyleVar_WindowBorderSize,
    ImGuiStyleVar_WindowMinSize,
    ImGuiStyleVar_WindowTitleAlign,
    ImGuiStyleVar_ChildRounding,
    ImGuiStyleVar_ChildBorderSize,
    ImGuiStyleVar_PopupRounding,
    ImGuiStyleVar_PopupBorderSize,
    ImGuiStyleVar_FramePadding,
    ImGuiStyleVar_FrameRounding,
    ImGuiStyleVar_FrameBorderSize,
    ImGuiStyleVar_ItemSpacing,
    ImGuiStyleVar_ItemInnerSpacing,
    ImGuiStyleVar_IndentSpacing,
    ImGuiStyleVar_ScrollbarSize,
    ImGuiStyleVar_ScrollbarRounding,
    ImGuiStyleVar_GrabMinSize,
    ImGuiStyleVar_GrabRounding,
    ImGuiStyleVar_TabRounding,
    ImGuiStyleVar_ButtonTextAlign,
    ImGuiStyleVar_SelectableTextAlign
};

enum ImGuiCond_ {
    ImGuiCond_Always,
    ImGuiCond_Once,
    ImGuiCond_FirstUseEver,
    ImGuiCond_Appearing
};

// ImGui context
struct ImGuiContext;
extern ImGuiContext* GImGui;

// Main API
namespace ImGui
{
    // Context creation and access
    ImGuiContext* CreateContext();
    void          DestroyContext(ImGuiContext* ctx = nullptr);
    ImGuiContext* GetCurrentContext();
    void          SetCurrentContext(ImGuiContext* ctx);

    // Main
    ImGuiIO&      GetIO();
    void          NewFrame();
    void          EndFrame();
    void          Render();
    ImDrawData*   GetDrawData();
    void          ShowDemoWindow(bool* p_open = nullptr);

    // Style
    void          StyleColorsDark();

    // Windows
    bool          Begin(const char* name, bool* p_open = nullptr, int flags = 0);
    void          End();

    // Child Windows
    bool          BeginChild(const char* str_id, const ImVec2& size = ImVec2(0, 0), bool border = false, int flags = 0);
    void          EndChild();

    // Windows Utilities
    bool          IsWindowAppearing();
    bool          IsWindowCollapsed();
    bool          IsWindowFocused(int flags = 0);
    bool          IsWindowHovered(int flags = 0);
    ImVec2        GetWindowPos();
    ImVec2        GetWindowSize();
    float         GetWindowWidth();
    float         GetWindowHeight();

    // Widgets: Text
    void          Text(const char* fmt, ...);
    void          TextColored(const ImVec4& col, const char* fmt, ...);
    void          TextDisabled(const char* fmt, ...);
    void          TextWrapped(const char* fmt, ...);
    void          LabelText(const char* label, const char* fmt, ...);
    void          BulletText(const char* fmt, ...);

    // Widgets: Main
    bool          Button(const char* label, const ImVec2& size = ImVec2(0, 0));
    bool          SmallButton(const char* label);
    bool          InvisibleButton(const char* str_id, const ImVec2& size, int flags = 0);
    bool          ArrowButton(const char* str_id, int dir);
    void          Image(void* user_texture_id, const ImVec2& size, const ImVec2& uv0 = ImVec2(0, 0), const ImVec2& uv1 = ImVec2(1, 1), const ImVec4& tint_col = ImVec4(1, 1, 1, 1), const ImVec4& border_col = ImVec4(0, 0, 0, 0));
    bool          ImageButton(void* user_texture_id, const ImVec2& size, const ImVec2& uv0 = ImVec2(0, 0), const ImVec2& uv1 = ImVec2(1, 1), int frame_padding = -1, const ImVec4& bg_col = ImVec4(0, 0, 0, 0), const ImVec4& tint_col = ImVec4(1, 1, 1, 1));
    bool          Checkbox(const char* label, bool* v);
    bool          CheckboxFlags(const char* label, int* flags, int flags_value);
    bool          CheckboxFlags(const char* label, unsigned int* flags, unsigned int flags_value);
    bool          RadioButton(const char* label, bool active);
    bool          RadioButton(const char* label, int* v, int v_button);
    void          ProgressBar(float fraction, const ImVec2& size_arg = ImVec2(-1, 0), const char* overlay = nullptr);
    void          Bullet();

    // Widgets: Combo Box
    bool          BeginCombo(const char* label, const char* preview_value, int flags = 0);
    void          EndCombo();
    bool          Combo(const char* label, int* current_item, const char* const items[], int items_count, int popup_max_height_in_items = -1);
    bool          Combo(const char* label, int* current_item, const char* items_separated_by_zeros, int popup_max_height_in_items = -1);

    // Widgets: Drag Sliders
    bool          DragFloat(const char* label, float* v, float v_speed = 1.0f, float v_min = 0.0f, float v_max = 0.0f, const char* format = "%.3f", int flags = 0);
    bool          DragFloat2(const char* label, float v[2], float v_speed = 1.0f, float v_min = 0.0f, float v_max = 0.0f, const char* format = "%.3f", int flags = 0);
    bool          DragFloat3(const char* label, float v[3], float v_speed = 1.0f, float v_min = 0.0f, float v_max = 0.0f, const char* format = "%.3f", int flags = 0);
    bool          DragFloat4(const char* label, float v[4], float v_speed = 1.0f, float v_min = 0.0f, float v_max = 0.0f, const char* format = "%.3f", int flags = 0);
    bool          DragInt(const char* label, int* v, float v_speed = 1.0f, int v_min = 0, int v_max = 0, const char* format = "%d", int flags = 0);
    bool          DragInt2(const char* label, int v[2], float v_speed = 1.0f, int v_min = 0, int v_max = 0, const char* format = "%d", int flags = 0);
    bool          DragInt3(const char* label, int v[3], float v_speed = 1.0f, int v_min = 0, int v_max = 0, const char* format = "%d", int flags = 0);
    bool          DragInt4(const char* label, int v[4], float v_speed = 1.0f, int v_min = 0, int v_max = 0, const char* format = "%d", int flags = 0);

    // Widgets: Regular Sliders
    bool          SliderFloat(const char* label, float* v, float v_min, float v_max, const char* format = "%.3f", int flags = 0);
    bool          SliderFloat2(const char* label, float v[2], float v_min, float v_max, const char* format = "%.3f", int flags = 0);
    bool          SliderFloat3(const char* label, float v[3], float v_min, float v_max, const char* format = "%.3f", int flags = 0);
    bool          SliderFloat4(const char* label, float v[4], float v_min, float v_max, const char* format = "%.3f", int flags = 0);
    bool          SliderAngle(const char* label, float* v_rad, float v_degrees_min = -360.0f, float v_degrees_max = +360.0f, const char* format = "%.0f deg", int flags = 0);
    bool          SliderInt(const char* label, int* v, int v_min, int v_max, const char* format = "%d", int flags = 0);
    bool          SliderInt2(const char* label, int v[2], int v_min, int v_max, const char* format = "%d", int flags = 0);
    bool          SliderInt3(const char* label, int v[3], int v_min, int v_max, const char* format = "%d", int flags = 0);
    bool          SliderInt4(const char* label, int v[4], int v_min, int v_max, const char* format = "%d", int flags = 0);

    // Widgets: Input with Keyboard
    bool          InputText(const char* label, char* buf, size_t buf_size, int flags = 0, void* callback = nullptr, void* user_data = nullptr);
    bool          InputTextMultiline(const char* label, char* buf, size_t buf_size, const ImVec2& size = ImVec2(0, 0), int flags = 0, void* callback = nullptr, void* user_data = nullptr);
    bool          InputTextWithHint(const char* label, const char* hint, char* buf, size_t buf_size, int flags = 0, void* callback = nullptr, void* user_data = nullptr);
    bool          InputFloat(const char* label, float* v, float step = 0.0f, float step_fast = 0.0f, const char* format = "%.3f", int flags = 0);
    bool          InputFloat2(const char* label, float v[2], const char* format = "%.3f", int flags = 0);
    bool          InputFloat3(const char* label, float v[3], const char* format = "%.3f", int flags = 0);
    bool          InputFloat4(const char* label, float v[4], const char* format = "%.3f", int flags = 0);
    bool          InputInt(const char* label, int* v, int step = 1, int step_fast = 100, int flags = 0);
    bool          InputInt2(const char* label, int v[2], int flags = 0);
    bool          InputInt3(const char* label, int v[3], int flags = 0);
    bool          InputInt4(const char* label, int v[4], int flags = 0);
    bool          InputDouble(const char* label, double* v, double step = 0.0, double step_fast = 0.0, const char* format = "%.6f", int flags = 0);

    // Widgets: Color Editor/Picker
    bool          ColorEdit3(const char* label, float col[3], int flags = 0);
    bool          ColorEdit4(const char* label, float col[4], int flags = 0);
    bool          ColorPicker3(const char* label, float col[3], int flags = 0);
    bool          ColorPicker4(const char* label, float col[4], int flags = 0, const float* ref_col = nullptr);
    bool          ColorButton(const char* desc_id, const ImVec4& col, int flags = 0, ImVec2 size = ImVec2(0, 0));
    void          SetColorEditOptions(int flags);

    // Widgets: Trees
    bool          TreeNode(const char* label);
    bool          TreeNode(const char* str_id, const char* fmt, ...);
    bool          TreeNode(const void* ptr_id, const char* fmt, ...);
    bool          TreeNodeEx(const char* label, int flags = 0);
    bool          TreeNodeEx(const char* str_id, int flags, const char* fmt, ...);
    bool          TreeNodeEx(const void* ptr_id, int flags, const char* fmt, ...);
    void          TreePush(const char* str_id);
    void          TreePush(const void* ptr_id = nullptr);
    void          TreePop();
    float         GetTreeNodeToLabelSpacing();
    bool          CollapsingHeader(const char* label, int flags = 0);
    bool          CollapsingHeader(const char* label, bool* p_visible, int flags = 0);
    void          SetNextItemOpen(bool is_open, int cond = 0);

    // Widgets: Selectables
    bool          Selectable(const char* label, bool selected = false, int flags = 0, const ImVec2& size = ImVec2(0, 0));
    bool          Selectable(const char* label, bool* p_selected, int flags = 0, const ImVec2& size = ImVec2(0, 0));

    // Widgets: List Boxes
    bool          BeginListBox(const char* label, const ImVec2& size = ImVec2(0, 0));
    void          EndListBox();
    bool          ListBox(const char* label, int* current_item, const char* const items[], int items_count, int height_in_items = -1);

    // Widgets: Menus
    bool          BeginMenuBar();
    void          EndMenuBar();
    bool          BeginMainMenuBar();
    void          EndMainMenuBar();
    bool          BeginMenu(const char* label, bool enabled = true);
    void          EndMenu();
    bool          MenuItem(const char* label, const char* shortcut = nullptr, bool selected = false, bool enabled = true);
    bool          MenuItem(const char* label, const char* shortcut, bool* p_selected, bool enabled = true);

    // Tooltips
    void          BeginTooltip();
    void          EndTooltip();
    void          SetTooltip(const char* fmt, ...);

    // Popups, Modals
    bool          BeginPopup(const char* str_id, int flags = 0);
    bool          BeginPopupModal(const char* name, bool* p_open = nullptr, int flags = 0);
    void          EndPopup();
    void          OpenPopup(const char* str_id, int popup_flags = 0);
    void          OpenPopup(unsigned int id, int popup_flags = 0);
    void          OpenPopupOnItemClick(const char* str_id = nullptr, int popup_flags = 1);
    void          CloseCurrentPopup();

    // Columns
    void          Columns(int count = 1, const char* id = nullptr, bool border = true);
    void          NextColumn();
    int           GetColumnIndex();
    float         GetColumnWidth(int column_index = -1);
    void          SetColumnWidth(int column_index, float width);
    float         GetColumnOffset(int column_index = -1);
    void          SetColumnOffset(int column_index, float offset_x);
    int           GetColumnsCount();

    // Tab Bars, Tabs
    bool          BeginTabBar(const char* str_id, int flags = 0);
    void          EndTabBar();
    bool          BeginTabItem(const char* label, bool* p_open = nullptr, int flags = 0);
    void          EndTabItem();
    bool          TabItemButton(const char* label, int flags = 0);
    void          SetTabItemClosed(const char* tab_or_docked_window_label);

    // Logging/Capture
    void          LogToTTY(int auto_open_depth = -1);
    void          LogToFile(int auto_open_depth = -1, const char* filename = nullptr);
    void          LogToClipboard(int auto_open_depth = -1);
    void          LogFinish();
    void          LogButtons();
    void          LogText(const char* fmt, ...);

    // Drag and Drop
    bool          BeginDragDropSource(int flags = 0);
    bool          SetDragDropPayload(const char* type, const void* data, size_t sz, int cond = 0);
    void          EndDragDropSource();
    bool          BeginDragDropTarget();
    const void*   AcceptDragDropPayload(const char* type, int flags = 0);
    void          EndDragDropTarget();
    const void*   GetDragDropPayload();

    // Clipping
    void          PushClipRect(const ImVec2& clip_rect_min, const ImVec2& clip_rect_max, bool intersect_with_current_clip_rect);
    void          PopClipRect();

    // Focus, Activation
    void          SetItemDefaultFocus();
    void          SetKeyboardFocusHere(int offset = 0);

    // Item/Widgets Utilities
    bool          IsItemHovered(int flags = 0);
    bool          IsItemActive();
    bool          IsItemFocused();
    bool          IsItemClicked(int mouse_button = 0);
    bool          IsItemVisible();
    bool          IsItemEdited();
    bool          IsItemActivated();
    bool          IsItemDeactivated();
    bool          IsItemDeactivatedAfterEdit();
    bool          IsItemToggledOpen();
    bool          IsAnyItemHovered();
    bool          IsAnyItemActive();
    bool          IsAnyItemFocused();
    ImVec2        GetItemRectMin();
    ImVec2        GetItemRectMax();
    ImVec2        GetItemRectSize();
    void          SetItemAllowOverlap();

    // Miscellaneous Utilities
    bool          IsRectVisible(const ImVec2& size);
    bool          IsRectVisible(const ImVec2& rect_min, const ImVec2& rect_max);
    double        GetTime();
    int           GetFrameCount();
    const char*   GetStyleColorName(int idx);
    void          SetStateStorage(void* storage);
    void*         GetStateStorage();

    // Text Utilities
    ImVec2        CalcTextSize(const char* text, const char* text_end = nullptr, bool hide_text_after_double_hash = false, float wrap_width = -1.0f);

    // Color Utilities
    ImVec4        ColorConvertU32ToFloat4(unsigned int in);
    unsigned int  ColorConvertFloat4ToU32(const ImVec4& in);
    void          ColorConvertRGBtoHSV(float r, float g, float b, float& out_h, float& out_s, float& out_v);
    void          ColorConvertHSVtoRGB(float h, float s, float v, float& out_r, float& out_g, float& out_b);

    // Inputs Utilities: Keyboard
    int           GetKeyIndex(int imgui_key);
    bool          IsKeyDown(int user_key_index);
    bool          IsKeyPressed(int user_key_index, bool repeat = true);
    bool          IsKeyReleased(int user_key_index);
    int           GetKeyPressedAmount(int key_index, float repeat_delay, float rate);
    void          CaptureKeyboardFromApp(bool want_capture_keyboard_value = true);

    // Inputs Utilities: Mouse
    bool          IsMouseDown(int button);
    bool          IsMouseClicked(int button, bool repeat = false);
    bool          IsMouseReleased(int button);
    bool          IsMouseDoubleClicked(int button);
    bool          IsMouseHoveringRect(const ImVec2& r_min, const ImVec2& r_max, bool clip = true);
    bool          IsMousePosValid(const ImVec2* mouse_pos = nullptr);
    bool          IsAnyMouseDown();
    ImVec2        GetMousePos();
    ImVec2        GetMousePosOnOpeningCurrentPopup();
    bool          IsMouseDragging(int button, float lock_threshold = -1.0f);
    ImVec2        GetMouseDragDelta(int button = 0, float lock_threshold = -1.0f);
    void          ResetMouseDragDelta(int button = 0);
    int           GetMouseCursor();
    void          SetMouseCursor(int cursor_type);
    void          CaptureMouseFromApp(bool want_capture_mouse_value = true);

    // Clipboard Utilities
    const char*   GetClipboardText();
    void          SetClipboardText(const char* text);

    // Settings/.Ini Utilities
    void          LoadIniSettingsFromDisk(const char* ini_filename);
    void          LoadIniSettingsFromMemory(const char* ini_data, size_t ini_size = 0);
    void          SaveIniSettingsToDisk(const char* ini_filename);
    const char*   SaveIniSettingsToMemory(size_t* out_ini_size = nullptr);

    // Debug Utilities
    bool          DebugCheckVersionAndDataLayout(const char* version_str, size_t sz_io, size_t sz_style, size_t sz_vec2, size_t sz_vec4, size_t sz_drawvert, size_t sz_drawidx);
    void          DebugTextEncoding(const char* text);

    // Memory Allocators
    void          SetAllocatorFunctions(void* (*alloc_func)(size_t sz, void* user_data), void (*free_func)(void* ptr, void* user_data), void* user_data = nullptr);
    void          GetAllocatorFunctions(void* (**p_alloc_func)(size_t sz, void* user_data), void (**p_free_func)(void* ptr, void* user_data), void** p_user_data);
    void*         MemAlloc(size_t size);
    void          MemFree(void* ptr);

    // Style
    void          PushStyleColor(int idx, const ImVec4& col);
    void          PushStyleVar(int idx, float val);
    void          PushStyleVar(int idx, const ImVec2& val);
    void          PopStyleColor(int count = 1);
    void          PopStyleVar(int count = 1);

    // Layout
    void          Separator();
    void          SameLine(float offset_from_start_x = 0.0f, float spacing = -1.0f);
    void          Spacing();
    void          Dummy(const ImVec2& size);
    void          Indent(float indent_w = 0.0f);
    void          Unindent(float indent_w = 0.0f);

    // Window manipulation
    void          SetNextWindowPos(const ImVec2& pos, int cond = 0, const ImVec2& pivot = ImVec2(0, 0));
    void          SetNextWindowSize(const ImVec2& size, int cond = 0);

    // Drawing
    ImDrawList*   GetWindowDrawList();
    ImDrawList*   GetBackgroundDrawList();
    ImDrawList*   GetForegroundDrawList();

    // Input Utilities
    bool          IsAnyMouseDown();
}

// Dummy context structure
struct ImGuiContext {
    bool initialized = false;
};

// Global ImGui context and IO
static ImGuiContext g_Context;
static ImGuiIO g_IO;

// Dummy implementation stubs
inline ImGuiContext* ImGui::CreateContext() { return &g_Context; }
inline void ImGui::DestroyContext(ImGuiContext*) {}
inline ImGuiContext* ImGui::GetCurrentContext() { return &g_Context; }
inline void ImGui::SetCurrentContext(ImGuiContext*) {}
inline ImGuiIO& ImGui::GetIO() { return g_IO; }
inline void ImGui::NewFrame() {}
inline void ImGui::EndFrame() {}
inline void ImGui::Render() {}
inline ImDrawData* ImGui::GetDrawData() { static ImDrawData data; return &data; }
inline void ImGui::ShowDemoWindow(bool*) {}
inline void ImGui::StyleColorsDark() {}
inline bool ImGui::Begin(const char*, bool*, int) { return true; }
inline void ImGui::End() {}
inline void ImGui::Text(const char*, ...) {}
inline bool ImGui::Button(const char*, const ImVec2&) { return false; }
inline bool ImGui::Checkbox(const char*, bool*) { return false; }
inline bool ImGui::SliderFloat(const char*, float*, float, float, const char*, int) { return false; }
inline bool ImGui::SliderInt(const char*, int*, int, int, const char*, int) { return false; }
inline bool ImGui::ColorEdit3(const char*, float[3], int) { return false; }
inline bool ImGui::ColorEdit4(const char*, float[4], int) { return false; }
inline bool ImGui::BeginTabBar(const char*, int) { return true; }
inline void ImGui::EndTabBar() {}
inline bool ImGui::BeginTabItem(const char*, bool*, int) { return true; }
inline void ImGui::EndTabItem() {}
inline bool ImGui::TreeNode(const char*) { return false; }
inline void ImGui::TreePop() {}
inline bool ImGui::CollapsingHeader(const char*, int) { return false; }
inline void ImGui::PushStyleColor(int, const ImVec4&) {}
inline void ImGui::PushStyleVar(int, float) {}
inline void ImGui::PushStyleVar(int, const ImVec2&) {}
inline void ImGui::PopStyleColor(int) {}
inline void ImGui::PopStyleVar(int) {}
inline void ImGui::Separator() {}
inline void ImGui::SameLine(float, float) {}
inline void ImGui::Spacing() {}
inline void ImGui::Dummy(const ImVec2&) {}
inline void ImGui::Indent(float) {}
inline void ImGui::Unindent(float) {}
inline void ImGui::SetNextWindowPos(const ImVec2&, int, const ImVec2&) {}
inline void ImGui::SetNextWindowSize(const ImVec2&, int) {}
inline ImDrawList* ImGui::GetWindowDrawList() { static ImDrawList dl; return &dl; }
inline ImDrawList* ImGui::GetBackgroundDrawList() { static ImDrawList dl; return &dl; }
inline ImDrawList* ImGui::GetForegroundDrawList() { static ImDrawList dl; return &dl; }
inline bool ImGui::IsAnyMouseDown() {
    ImGuiIO& io = GetIO();
    for (int i = 0; i < 5; i++)
        if (io.MouseDown[i]) return true;
    return false;
}
