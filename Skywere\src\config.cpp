#include "pch.h"

// Static member definitions
bool Config::initialized = false;
std::string Config::configDirectory;
std::string Config::currentConfig;
std::unordered_map<std::string, std::string> Config::configData;
std::mutex Config::configMutex;
bool Config::autoSaveEnabled = false;
float Config::autoSaveInterval = 30.0f;
std::chrono::steady_clock::time_point Config::lastAutoSave;

bool Config::Initialize() {
    if (initialized) return true;
    
    LOG("Initializing config system...");
    
    // Get config directory
    char path[MAX_PATH];
    if (GetModuleFileNameA(nullptr, path, MAX_PATH) == 0) {
        LOG("Failed to get module path!");
        return false;
    }
    
    std::string modulePath = path;
    size_t lastSlash = modulePath.find_last_of("\\/");
    if (lastSlash != std::string::npos) {
        configDirectory = modulePath.substr(0, lastSlash) + "\\Skywere\\configs";
    } else {
        configDirectory = ".\\Skywere\\configs";
    }
    
    if (!CreateConfigDirectory()) {
        LOG("Failed to create config directory!");
        return false;
    }
    
    LOGF("Config directory: %s", configDirectory.c_str());
    
    lastAutoSave = std::chrono::steady_clock::now();
    initialized = true;
    
    LOG("Config system initialized successfully!");
    return true;
}

void Config::Shutdown() {
    if (!initialized) return;
    
    // Save current config before shutdown
    if (!currentConfig.empty()) {
        Save(currentConfig);
    }
    
    std::lock_guard<std::mutex> lock(configMutex);
    configData.clear();
    initialized = false;
    
    LOG("Config system shutdown complete.");
}

bool Config::Load(const std::string& name) {
    if (!initialized) return false;
    
    std::lock_guard<std::mutex> lock(configMutex);
    
    std::string filepath = GetConfigPath(name);
    if (!LoadFromFile(filepath)) {
        LOG("Failed to load config: " + name);
        return false;
    }
    
    currentConfig = name;
    LoadFeatureConfigs();
    
    LOGF("Config loaded: %s", name.c_str());
    return true;
}

bool Config::Save(const std::string& name) {
    if (!initialized) return false;
    
    std::lock_guard<std::mutex> lock(configMutex);
    
    SaveFeatureConfigs();
    
    std::string filepath = GetConfigPath(name);
    if (!SaveToFile(filepath)) {
        LOG("Failed to save config: " + name);
        return false;
    }
    
    currentConfig = name;
    LOGF("Config saved: %s", name.c_str());
    return true;
}

bool Config::Delete(const std::string& name) {
    if (!initialized || name == "default") return false;
    
    std::string filepath = GetConfigPath(name);
    if (DeleteFileA(filepath.c_str())) {
        LOGF("Config deleted: %s", name.c_str());
        return true;
    }
    
    return false;
}

bool Config::Exists(const std::string& name) {
    if (!initialized) return false;
    
    std::string filepath = GetConfigPath(name);
    DWORD attributes = GetFileAttributesA(filepath.c_str());
    return (attributes != INVALID_FILE_ATTRIBUTES && !(attributes & FILE_ATTRIBUTE_DIRECTORY));
}

std::vector<std::string> Config::GetConfigList() {
    std::vector<std::string> configs;
    if (!initialized) return configs;
    
    std::string searchPath = configDirectory + "\\*.json";
    WIN32_FIND_DATAA findData;
    HANDLE hFind = FindFirstFileA(searchPath.c_str(), &findData);
    
    if (hFind != INVALID_HANDLE_VALUE) {
        do {
            std::string filename = findData.cFileName;
            size_t dotPos = filename.find_last_of('.');
            if (dotPos != std::string::npos) {
                configs.push_back(filename.substr(0, dotPos));
            }
        } while (FindNextFileA(hFind, &findData));
        
        FindClose(hFind);
    }
    
    return configs;
}

void Config::EnableAutoSave(bool enable, float interval) {
    autoSaveEnabled = enable;
    autoSaveInterval = interval;
    lastAutoSave = std::chrono::steady_clock::now();
}

void Config::UpdateAutoSave() {
    if (!autoSaveEnabled || currentConfig.empty()) return;
    
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - lastAutoSave).count();
    
    if (elapsed >= autoSaveInterval) {
        Save(currentConfig);
        lastAutoSave = now;
    }
}

// Specialized setters
void Config::SetBool(const std::string& key, bool value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = value ? "true" : "false";
}

void Config::SetInt(const std::string& key, int value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = std::to_string(value);
}

void Config::SetFloat(const std::string& key, float value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = std::to_string(value);
}

void Config::SetString(const std::string& key, const std::string& value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = value;
}

void Config::SetColor(const std::string& key, const ImVec4& value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = ToString(value);
}

void Config::SetVector3(const std::string& key, const Vector3& value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = ToString(value);
}

void Config::SetAngles(const std::string& key, const QAngle& value) {
    std::lock_guard<std::mutex> lock(configMutex);
    configData[key] = ToString(value);
}

// Specialized getters
bool Config::GetBool(const std::string& key, bool defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    return it->second == "true";
}

int Config::GetInt(const std::string& key, int defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    try {
        return std::stoi(it->second);
    } catch (...) {
        return defaultValue;
    }
}

float Config::GetFloat(const std::string& key, float defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    try {
        return std::stof(it->second);
    } catch (...) {
        return defaultValue;
    }
}

std::string Config::GetString(const std::string& key, const std::string& defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    return it->second;
}

ImVec4 Config::GetColor(const std::string& key, const ImVec4& defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    return ToColor(it->second);
}

Vector3 Config::GetVector3(const std::string& key, const Vector3& defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    return ToVector3(it->second);
}

QAngle Config::GetAngles(const std::string& key, const QAngle& defaultValue) {
    std::lock_guard<std::mutex> lock(configMutex);
    auto it = configData.find(key);
    if (it == configData.end()) return defaultValue;
    return ToAngles(it->second);
}

void Config::SaveFeatureConfigs() {
    if (!FeatureManager::GetAimbot()) return;
    
    // Save aimbot config
    auto* aimbot = FeatureManager::GetAimbot();
    SetBool(ConfigKeys::Aimbot::ENABLED, aimbot->config.enabled);
    SetFloat(ConfigKeys::Aimbot::FOV, aimbot->config.fov);
    SetFloat(ConfigKeys::Aimbot::SMOOTHNESS, aimbot->config.smoothness);
    SetInt(ConfigKeys::Aimbot::HITBOX, aimbot->config.hitbox);
    SetBool(ConfigKeys::Aimbot::AUTO_SHOOT, aimbot->config.autoShoot);
    SetBool(ConfigKeys::Aimbot::RCS, aimbot->config.rcs);
    SetBool(ConfigKeys::Aimbot::PREDICTION, aimbot->config.prediction);
    SetBool(ConfigKeys::Aimbot::VISIBILITY_CHECK, aimbot->config.visibilityCheck);
    SetBool(ConfigKeys::Aimbot::TEAM_CHECK, aimbot->config.teamCheck);
    SetInt(ConfigKeys::Aimbot::KEY, aimbot->config.key);
    SetBool(ConfigKeys::Aimbot::KEY_MODE, aimbot->config.keyMode);
    
    // Save ESP config
    auto* esp = FeatureManager::GetESP();
    SetBool(ConfigKeys::ESP::ENABLED, esp->config.enabled);
    SetBool(ConfigKeys::ESP::PLAYERS, esp->config.players);
    SetBool(ConfigKeys::ESP::WEAPONS, esp->config.weapons);
    SetBool(ConfigKeys::ESP::BOMBS, esp->config.bombs);
    SetBool(ConfigKeys::ESP::BOXES, esp->config.boxes);
    SetBool(ConfigKeys::ESP::NAMES, esp->config.names);
    SetBool(ConfigKeys::ESP::HEALTH, esp->config.health);
    SetBool(ConfigKeys::ESP::ARMOR, esp->config.armor);
    SetBool(ConfigKeys::ESP::DISTANCE, esp->config.distance);
    SetBool(ConfigKeys::ESP::SKELETON, esp->config.skeleton);
    SetBool(ConfigKeys::ESP::SNAPLINES, esp->config.snaplines);
    SetBool(ConfigKeys::ESP::VISIBILITY_CHECK, esp->config.visibilityCheck);
    SetBool(ConfigKeys::ESP::TEAM_CHECK, esp->config.teamCheck);
    SetColor(ConfigKeys::ESP::ENEMY_COLOR, esp->config.enemyColor);
    SetColor(ConfigKeys::ESP::TEAM_COLOR, esp->config.teamColor);
    SetColor(ConfigKeys::ESP::WEAPON_COLOR, esp->config.weaponColor);
    SetColor(ConfigKeys::ESP::BOMB_COLOR, esp->config.bombColor);
    
    // Save other features...
}

void Config::LoadFeatureConfigs() {
    if (!FeatureManager::GetAimbot()) return;

    // Load aimbot config
    auto* aimbot = FeatureManager::GetAimbot();
    aimbot->config.enabled = GetBool(ConfigKeys::Aimbot::ENABLED, false);
    aimbot->config.fov = GetFloat(ConfigKeys::Aimbot::FOV, 5.0f);
    aimbot->config.smoothness = GetFloat(ConfigKeys::Aimbot::SMOOTHNESS, 1.0f);
    aimbot->config.hitbox = GetInt(ConfigKeys::Aimbot::HITBOX, BONE_HEAD);
    aimbot->config.autoShoot = GetBool(ConfigKeys::Aimbot::AUTO_SHOOT, false);
    aimbot->config.rcs = GetBool(ConfigKeys::Aimbot::RCS, true);
    aimbot->config.prediction = GetBool(ConfigKeys::Aimbot::PREDICTION, true);
    aimbot->config.visibilityCheck = GetBool(ConfigKeys::Aimbot::VISIBILITY_CHECK, true);
    aimbot->config.teamCheck = GetBool(ConfigKeys::Aimbot::TEAM_CHECK, true);
    aimbot->config.key = GetInt(ConfigKeys::Aimbot::KEY, VK_LBUTTON);
    aimbot->config.keyMode = GetBool(ConfigKeys::Aimbot::KEY_MODE, false);

    // Load ESP config
    auto* esp = FeatureManager::GetESP();
    esp->config.enabled = GetBool(ConfigKeys::ESP::ENABLED, false);
    esp->config.players = GetBool(ConfigKeys::ESP::PLAYERS, true);
    esp->config.weapons = GetBool(ConfigKeys::ESP::WEAPONS, false);
    esp->config.bombs = GetBool(ConfigKeys::ESP::BOMBS, true);
    esp->config.boxes = GetBool(ConfigKeys::ESP::BOXES, true);
    esp->config.names = GetBool(ConfigKeys::ESP::NAMES, true);
    esp->config.health = GetBool(ConfigKeys::ESP::HEALTH, true);
    esp->config.armor = GetBool(ConfigKeys::ESP::ARMOR, true);
    esp->config.distance = GetBool(ConfigKeys::ESP::DISTANCE, true);
    esp->config.skeleton = GetBool(ConfigKeys::ESP::SKELETON, false);
    esp->config.snaplines = GetBool(ConfigKeys::ESP::SNAPLINES, false);
    esp->config.visibilityCheck = GetBool(ConfigKeys::ESP::VISIBILITY_CHECK, false);
    esp->config.teamCheck = GetBool(ConfigKeys::ESP::TEAM_CHECK, true);
    esp->config.enemyColor = GetColor(ConfigKeys::ESP::ENEMY_COLOR, ImVec4(1.0f, 0.0f, 0.0f, 1.0f));
    esp->config.teamColor = GetColor(ConfigKeys::ESP::TEAM_COLOR, ImVec4(0.0f, 1.0f, 0.0f, 1.0f));
    esp->config.weaponColor = GetColor(ConfigKeys::ESP::WEAPON_COLOR, ImVec4(1.0f, 1.0f, 0.0f, 1.0f));
    esp->config.bombColor = GetColor(ConfigKeys::ESP::BOMB_COLOR, ImVec4(1.0f, 0.5f, 0.0f, 1.0f));

    // Load other features...
}

std::string Config::GetConfigPath(const std::string& name) {
    return configDirectory + "\\" + name + ".json";
}

bool Config::CreateConfigDirectory() {
    return CreateDirectoryA(configDirectory.c_str(), nullptr) || GetLastError() == ERROR_ALREADY_EXISTS;
}

bool Config::LoadFromFile(const std::string& filepath) {
    std::ifstream file(filepath);
    if (!file.is_open()) {
        // Create default config if file doesn't exist
        configData.clear();
        return true;
    }

    std::string content((std::istreambuf_iterator<char>(file)), std::istreambuf_iterator<char>());
    file.close();

    return ParseJSON(content);
}

bool Config::SaveToFile(const std::string& filepath) {
    std::string json = GenerateJSON();

    std::ofstream file(filepath);
    if (!file.is_open()) return false;

    file << json;
    file.close();

    return true;
}

bool Config::ParseJSON(const std::string& json) {
    // Simple JSON parsing - in a real implementation you'd use a proper JSON library
    configData.clear();

    size_t pos = 0;
    while (pos < json.length()) {
        // Find key
        size_t keyStart = json.find('"', pos);
        if (keyStart == std::string::npos) break;
        keyStart++;

        size_t keyEnd = json.find('"', keyStart);
        if (keyEnd == std::string::npos) break;

        std::string key = json.substr(keyStart, keyEnd - keyStart);

        // Find value
        size_t valueStart = json.find(':', keyEnd);
        if (valueStart == std::string::npos) break;
        valueStart++;

        // Skip whitespace
        while (valueStart < json.length() && std::isspace(json[valueStart])) {
            valueStart++;
        }

        size_t valueEnd;
        std::string value;

        if (json[valueStart] == '"') {
            // String value
            valueStart++;
            valueEnd = json.find('"', valueStart);
            if (valueEnd == std::string::npos) break;
            value = json.substr(valueStart, valueEnd - valueStart);
            valueEnd++;
        } else {
            // Number or boolean value
            valueEnd = json.find_first_of(",}", valueStart);
            if (valueEnd == std::string::npos) valueEnd = json.length();
            value = json.substr(valueStart, valueEnd - valueStart);

            // Trim whitespace
            value.erase(0, value.find_first_not_of(" \t\n\r"));
            value.erase(value.find_last_not_of(" \t\n\r") + 1);
        }

        configData[key] = value;
        pos = valueEnd;
    }

    return true;
}

std::string Config::GenerateJSON() {
    std::string json = "{\n";

    bool first = true;
    for (const auto& pair : configData) {
        if (!first) json += ",\n";
        json += "  \"" + pair.first + "\": ";

        // Check if value is a number or boolean
        if (pair.second == "true" || pair.second == "false" ||
            (pair.second.find_first_not_of("0123456789.-") == std::string::npos && !pair.second.empty())) {
            json += pair.second;
        } else {
            json += "\"" + pair.second + "\"";
        }

        first = false;
    }

    json += "\n}";
    return json;
}

std::string Config::ToString(const ImVec4& color) {
    return std::to_string(color.x) + "," + std::to_string(color.y) + "," +
           std::to_string(color.z) + "," + std::to_string(color.w);
}

std::string Config::ToStringVector3(const Vector3& vector) {
    return std::to_string(vector.x) + "," + std::to_string(vector.y) + "," + std::to_string(vector.z);
}

std::string Config::ToString(const QAngle& angles) {
    return std::to_string(angles.x) + "," + std::to_string(angles.y) + "," + std::to_string(angles.z);
}

ImVec4 Config::ToColor(const std::string& str) {
    ImVec4 color(1, 1, 1, 1);
    std::istringstream ss(str);
    std::string token;

    if (std::getline(ss, token, ',')) color.x = std::stof(token);
    if (std::getline(ss, token, ',')) color.y = std::stof(token);
    if (std::getline(ss, token, ',')) color.z = std::stof(token);
    if (std::getline(ss, token, ',')) color.w = std::stof(token);

    return color;
}

Vector3 Config::ToVector3(const std::string& str) {
    Vector3 vector;
    std::istringstream ss(str);
    std::string token;

    if (std::getline(ss, token, ',')) vector.x = std::stof(token);
    if (std::getline(ss, token, ',')) vector.y = std::stof(token);
    if (std::getline(ss, token, ',')) vector.z = std::stof(token);

    return vector;
}

QAngle Config::ToAngles(const std::string& str) {
    QAngle angles;
    std::istringstream ss(str);
    std::string token;

    if (std::getline(ss, token, ',')) angles.x = std::stof(token);
    if (std::getline(ss, token, ',')) angles.y = std::stof(token);
    if (std::getline(ss, token, ',')) angles.z = std::stof(token);

    return angles;
}
