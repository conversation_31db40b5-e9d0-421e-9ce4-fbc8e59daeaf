#include "pch.h"

// Static member definitions
std::unique_ptr<Aimbot> FeatureManager::aimbot;
std::unique_ptr<ESP> FeatureManager::esp;
std::unique_ptr<Triggerbot> FeatureManager::triggerbot;
std::unique_ptr<Misc> FeatureManager::misc;
std::unique_ptr<Glow> FeatureManager::glow;
bool FeatureManager::panicMode = false;
int FeatureManager::panicKey = VK_DELETE;

// Input system
std::array<bool, 256> Input::keyStates;
std::array<bool, 256> Input::prevKeyStates;
std::array<bool, 256> Input::toggleStates;

// FeatureManager implementation
bool FeatureManager::Initialize() {
    LOG("Initializing feature manager...");
    
    // Create feature instances
    aimbot = std::make_unique<Aimbot>();
    esp = std::make_unique<ESP>();
    triggerbot = std::make_unique<Triggerbot>();
    misc = std::make_unique<Misc>();
    glow = std::make_unique<Glow>();
    
    LOG("Feature manager initialized successfully!");
    return true;
}

void FeatureManager::Shutdown() {
    LOG("Shutting down feature manager...");
    
    aimbot.reset();
    esp.reset();
    triggerbot.reset();
    misc.reset();
    glow.reset();
    
    LOG("Feature manager shutdown complete.");
}

void FeatureManager::Update() {
    CheckPanicMode();
    
    if (panicMode) return;
    
    // Update entity manager first
    EntityManager::Update();
    
    // Update features
    if (aimbot && aimbot->IsEnabled()) {
        aimbot->Update();
    }
    
    if (triggerbot && triggerbot->IsEnabled()) {
        triggerbot->Update();
    }
    
    if (misc && misc->IsEnabled()) {
        misc->Update();
    }
    
    if (glow && glow->IsEnabled()) {
        glow->Update();
    }
    
    if (esp && esp->IsEnabled()) {
        esp->Update();
    }
}

void FeatureManager::Render() {
    if (panicMode) return;
    
    // Render ESP
    if (esp && esp->IsEnabled()) {
        esp->Render();
    }
    
    // Render misc overlays
    if (misc && misc->IsEnabled()) {
        misc->Render();
    }
}

void FeatureManager::Reset() {
    if (aimbot) aimbot->Reset();
    if (esp) esp->Reset();
    if (triggerbot) triggerbot->Reset();
    if (misc) misc->Reset();
    if (glow) glow->Reset();
}

void FeatureManager::CheckPanicMode() {
    if (Input::IsKeyPressed(panicKey)) {
        panicMode = !panicMode;
        if (panicMode) {
            LOG("Panic mode activated!");
            Reset();
        } else {
            LOG("Panic mode deactivated!");
        }
    }
}

// Input implementation
bool Input::Initialize() {
    keyStates.fill(false);
    prevKeyStates.fill(false);
    toggleStates.fill(false);
    return true;
}

void Input::Update() {
    UpdateKeyStates();
}

bool Input::IsKeyDown(int key) {
    if (key < 0 || key >= 256) return false;
    return keyStates[key];
}

bool Input::IsKeyPressed(int key) {
    if (key < 0 || key >= 256) return false;
    return keyStates[key] && !prevKeyStates[key];
}

bool Input::IsKeyReleased(int key) {
    if (key < 0 || key >= 256) return false;
    return !keyStates[key] && prevKeyStates[key];
}

bool Input::IsKeyToggled(int key) {
    if (key < 0 || key >= 256) return false;
    
    if (IsKeyPressed(key)) {
        toggleStates[key] = !toggleStates[key];
    }
    
    return toggleStates[key];
}

void Input::UpdateKeyStates() {
    prevKeyStates = keyStates;
    
    for (int i = 0; i < 256; ++i) {
        keyStates[i] = (GetAsyncKeyState(i) & 0x8000) != 0;
    }
}

// Aimbot implementation
Aimbot::Aimbot() : Feature("Aimbot"), currentTarget(nullptr), isAiming(false) {
    lastAngles = QAngle();
    lastShotTime = std::chrono::steady_clock::now();
}

void Aimbot::Update() {
    if (!ShouldAim()) {
        currentTarget = nullptr;
        isAiming = false;
        return;
    }
    
    C_CSPlayerPawn* target = GetBestTarget();
    if (!target) {
        currentTarget = nullptr;
        isAiming = false;
        return;
    }
    
    currentTarget = target;
    Vector3 aimPos = GetAimPosition(target);
    
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return;
    
    Vector3 eyePos = localPlayer->GetEyePosition();
    QAngle targetAngles = MathUtils::CalcAngle(eyePos, aimPos);
    
    // Apply recoil compensation
    if (config.rcs) {
        QAngle recoil = GetRecoilCompensation();
        targetAngles = targetAngles - recoil;
    }
    
    // Clamp angles
    MathUtils::ClampAngles(targetAngles);
    
    DoAim(targetAngles);
    isAiming = true;
    
    // Auto shoot
    if (config.autoShoot && Aimbot::CanShoot()) {
        // Simulate mouse click
        mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
        lastShotTime = std::chrono::steady_clock::now();
    }
}

void Aimbot::Render() {
    if (!currentTarget || !isAiming) return;
    
    // Draw FOV circle
    if (config.fov > 0.0f) {
        Vector2 screenCenter = WorldToScreen::GetScreenSize() * 0.5f;
        float radius = config.fov * 10.0f; // Scale FOV for display
        
        Overlay::DrawCircle(screenCenter, radius, ImVec4(1, 1, 1, 0.3f), 1.0f);
    }
    
    // Draw target info
    Vector3 targetPos = currentTarget->GetOrigin();
    Vector2 screenPos;
    if (WorldToScreen::Project(targetPos, screenPos)) {
        std::string targetInfo = "TARGET: " + std::to_string(currentTarget->GetHealth()) + "HP";
        Overlay::DrawText(screenPos + Vector2(0, -20), targetInfo, ImVec4(1, 0, 0, 1), 12.0f);
    }
}

C_CSPlayerPawn* Aimbot::GetBestTarget() {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return nullptr;
    
    Vector3 eyePos = localPlayer->GetEyePosition();
    QAngle viewAngles = localPlayer->GetViewAngles();
    
    C_CSPlayerPawn* bestTarget = nullptr;
    float bestFOV = config.fov;
    
    auto enemies = EntityManager::GetEnemies();
    for (auto* enemy : enemies) {
        if (!enemy || !enemy->IsAlive()) continue;
        
        // Team check
        if (config.teamCheck && !enemy->IsEnemy(localPlayer)) continue;
        
        Vector3 targetPos = GetAimPosition(enemy);
        
        // Visibility check
        if (config.visibilityCheck && !AimUtils::IsVisible(eyePos, targetPos)) continue;
        
        // FOV check
        float fov = AimUtils::GetFOV(viewAngles, eyePos, targetPos);
        if (fov < bestFOV) {
            bestFOV = fov;
            bestTarget = enemy;
        }
    }
    
    return bestTarget;
}

Vector3 Aimbot::GetAimPosition(C_CSPlayerPawn* target) {
    if (!target) return Vector3();
    
    Vector3 targetPos = target->GetBonePosition(config.hitbox);
    
    // Apply prediction
    if (config.prediction) {
        Vector3 velocity = target->GetVelocity();
        float distance = EntityManager::GetLocalPlayer()->DistanceTo(target);
        float time = distance / 3000.0f; // Approximate bullet travel time
        targetPos = AimUtils::PredictPosition(targetPos, velocity, time);
    }
    
    return targetPos;
}

QAngle Aimbot::GetRecoilCompensation() {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return QAngle();
    
    // Get shots fired for recoil calculation
    int shotsFired = localPlayer->GetShotsFired();
    if (shotsFired <= 1) return QAngle();
    
    // Simple recoil pattern (would need actual CS2 recoil data)
    float recoilX = shotsFired * 0.5f;
    float recoilY = (shotsFired % 2 == 0) ? shotsFired * 0.3f : -shotsFired * 0.3f;
    
    return QAngle(recoilX, recoilY, 0.0f);
}

bool Aimbot::ShouldAim() {
    if (config.keyMode) {
        return Input::IsKeyToggled(config.key);
    } else {
        return Input::IsKeyDown(config.key);
    }
}

void Aimbot::DoAim(const QAngle& targetAngles) {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return;

    QAngle currentAngles = localPlayer->GetViewAngles();
    QAngle smoothedAngles = AimUtils::SmoothAim(currentAngles, targetAngles, config.smoothness);

    localPlayer->SetViewAngles(smoothedAngles);
    lastAngles = smoothedAngles;
}

bool Aimbot::CanShoot() {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return false;

    C_CSWeaponBase* weapon = localPlayer->GetActiveWeapon();
    if (!weapon) return false;

    return weapon->CanShoot();
}

// ESP implementation
ESP::ESP() : Feature("ESP") {}

void ESP::Update() {
    // ESP update logic if needed
}

void ESP::Render() {
    if (!config.players) return;

    auto players = EntityManager::GetPlayers();
    for (auto* player : players) {
        if (!ShouldRenderPlayer(player)) continue;
        RenderPlayer(player);
    }

    if (config.weapons) {
        RenderWeapons();
    }

    if (config.bombs) {
        RenderBombs();
    }
}

void ESP::RenderPlayer(C_CSPlayerPawn* player) {
    if (!player) return;

    Vector3 origin = player->GetOrigin();
    Vector3 head = player->GetHeadPosition();

    Vector2 screenOrigin, screenHead;
    if (!WorldToScreen::Project(origin, screenOrigin)) return;
    if (!WorldToScreen::Project(head, screenHead)) return;

    ImVec4 color = GetPlayerColor(player);

    // Draw box
    if (config.boxes) {
        RenderPlayerBox(player, screenHead, screenOrigin);
    }

    // Draw skeleton
    if (config.skeleton) {
        RenderPlayerSkeleton(player);
    }

    // Draw snaplines
    if (config.snaplines) {
        RenderSnapline(screenOrigin);
    }

    // Draw player info
    RenderPlayerInfo(player, screenHead, screenOrigin);
}

void ESP::RenderPlayerBox(C_CSPlayerPawn* player, const Vector2& head, const Vector2& feet) {
    float height = feet.y - head.y;
    float width = height * 0.4f;

    Vector2 topLeft = Vector2(head.x - width / 2, head.y);
    Vector2 bottomRight = Vector2(feet.x + width / 2, feet.y);

    ImVec4 color = GetPlayerColor(player);
    Overlay::DrawBox(topLeft, bottomRight, color, config.boxThickness);
}

void ESP::RenderPlayerInfo(C_CSPlayerPawn* player, const Vector2& head, const Vector2& feet) {
    float height = feet.y - head.y;
    float width = height * 0.4f;
    Vector2 infoPos = Vector2(head.x + width / 2 + 5, head.y);

    ImVec4 textColor = ImVec4(1, 1, 1, 1);

    // Name
    if (config.names) {
        C_CSPlayerController* controller = player->GetController();
        if (controller) {
            std::string name = controller->GetPlayerName();
            Overlay::DrawText(infoPos, name, textColor, config.fontSize);
            infoPos.y += config.fontSize + 2;
        }
    }

    // Health
    if (config.health) {
        int health = player->GetHealth();
        std::string healthText = "HP: " + std::to_string(health);
        ImVec4 healthColor = ImVec4(1.0f - health / 100.0f, health / 100.0f, 0, 1);
        Overlay::DrawText(infoPos, healthText, healthColor, config.fontSize);
        infoPos.y += config.fontSize + 2;
    }

    // Armor
    if (config.armor) {
        int armor = player->GetArmorValue();
        if (armor > 0) {
            std::string armorText = "ARMOR: " + std::to_string(armor);
            Overlay::DrawText(infoPos, armorText, ImVec4(0, 0, 1, 1), config.fontSize);
            infoPos.y += config.fontSize + 2;
        }
    }

    // Distance
    if (config.distance) {
        C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
        if (localPlayer) {
            float dist = localPlayer->DistanceTo(player) / 52.49f; // Convert to meters
            std::string distText = std::to_string(static_cast<int>(dist)) + "m";
            Overlay::DrawText(infoPos, distText, textColor, config.fontSize);
        }
    }
}

void ESP::RenderPlayerSkeleton(C_CSPlayerPawn* player) {
    // Simplified skeleton rendering
    std::vector<std::pair<int, int>> bones = {
        {BONE_HEAD, BONE_NECK},
        {BONE_NECK, BONE_SPINE},
        {BONE_SPINE, BONE_PELVIS}
    };

    ImVec4 color = GetPlayerColor(player);

    for (auto& bonePair : bones) {
        Vector3 bone1 = player->GetBonePosition(bonePair.first);
        Vector3 bone2 = player->GetBonePosition(bonePair.second);

        Vector2 screen1, screen2;
        if (WorldToScreen::Project(bone1, screen1) &&
            WorldToScreen::Project(bone2, screen2)) {
            Overlay::DrawLine(screen1, screen2, color, config.skeletonThickness);
        }
    }
}

void ESP::RenderSnapline(const Vector2& screen) {
    Vector2 screenCenter = WorldToScreen::GetScreenSize() * 0.5f;
    screenCenter.y = WorldToScreen::GetScreenSize().y; // Bottom of screen

    Overlay::DrawLine(screenCenter, screen, ImVec4(1, 1, 1, 0.5f), config.snaplineThickness);
}

void ESP::RenderWeapons() {
    // Weapon rendering would go here
    // This requires iterating through weapon entities
}

void ESP::RenderBombs() {
    // Bomb rendering would go here
    // This requires finding bomb entities
}

ImVec4 ESP::GetPlayerColor(C_CSPlayerPawn* player) {
    if (!player) return config.enemyColor;

    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return config.enemyColor;

    if (config.teamCheck && !player->IsEnemy(localPlayer)) {
        return config.teamColor;
    }

    return config.enemyColor;
}

bool ESP::ShouldRenderPlayer(C_CSPlayerPawn* player) {
    if (!player || !player->IsAlive()) return false;

    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer || player == localPlayer) return false;

    if (config.visibilityCheck && !player->IsVisible()) return false;

    return true;
}

// Triggerbot implementation
Triggerbot::Triggerbot() : Feature("Triggerbot"), isTriggering(false) {
    lastShotTime = std::chrono::steady_clock::now();
}

void Triggerbot::Update() {
    if (!ShouldShoot()) {
        isTriggering = false;
        return;
    }

    C_CSPlayerPawn* target = GetCrosshairTarget();
    if (!target) {
        isTriggering = false;
        return;
    }

    // Check delay
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastShotTime).count();

    if (elapsed >= config.delay) {
        Shoot();
        lastShotTime = now;
        isTriggering = true;
    }
}

bool Triggerbot::ShouldShoot() {
    if (config.keyMode) {
        return Input::IsKeyToggled(config.key);
    } else {
        return Input::IsKeyDown(config.key);
    }
}

C_CSPlayerPawn* Triggerbot::GetCrosshairTarget() {
    // This would require ray tracing from crosshair
    // Simplified implementation for now
    return nullptr;
}

void Triggerbot::Shoot() {
    mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
    mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);
}

// Misc implementation
Misc::Misc() : Feature("Misc"), wasOnGround(false), currentFPS(0.0f), frameCount(0) {
    lastFPSUpdate = std::chrono::steady_clock::now();
}

void Misc::Update() {
    if (config.bunnyHop) {
        UpdateBunnyHop();
    }

    if (config.autoStrafe) {
        UpdateAutoStrafe();
    }

    if (config.noFlash) {
        UpdateNoFlash();
    }

    if (config.noSmoke) {
        UpdateNoSmoke();
    }

    if (config.radarHack) {
        UpdateRadarHack();
    }

    // Update FPS counter
    frameCount++;
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastFPSUpdate).count();

    if (elapsed >= 1000) {
        currentFPS = frameCount * 1000.0f / elapsed;
        frameCount = 0;
        lastFPSUpdate = now;
    }
}

void Misc::Render() {
    if (config.spectatorList) {
        RenderSpectatorList();
    }

    if (config.watermark) {
        RenderWatermark();
    }

    if (config.fpsCounter) {
        RenderFPSCounter();
    }
}

void Misc::UpdateBunnyHop() {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return;

    bool onGround = localPlayer->IsOnGround();

    if (Input::IsKeyDown(config.bhopKey)) {
        if (onGround && !wasOnGround) {
            // Jump
            keybd_event(VK_SPACE, 0, 0, 0);
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
            keybd_event(VK_SPACE, 0, KEYEVENTF_KEYUP, 0);
        }
    }

    wasOnGround = onGround;
}

void Misc::UpdateAutoStrafe() {
    // Auto strafe implementation would go here
}

void Misc::UpdateNoFlash() {
    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return;

    if (localPlayer->IsFlashed()) {
        // Set flash alpha to configured value
        // This would require memory patching
    }
}

void Misc::UpdateNoSmoke() {
    // No smoke implementation would go here
}

void Misc::UpdateRadarHack() {
    // Radar hack implementation would go here
}

void Misc::RenderSpectatorList() {
    Vector2 screenSize = WorldToScreen::GetScreenSize();
    Vector2 pos = Vector2(screenSize.x - 200, 50);

    Overlay::DrawText(pos, "Spectators:", ImVec4(1, 1, 1, 1), 14.0f);
    pos.y += 20;

    for (const auto& spectator : spectators) {
        Overlay::DrawText(pos, spectator, ImVec4(0.8f, 0.8f, 0.8f, 1), 12.0f);
        pos.y += 15;
    }
}

void Misc::RenderWatermark() {
    Vector2 screenSize = WorldToScreen::GetScreenSize();
    Vector2 pos = Vector2(10, 10);

    std::string watermark = "Skywere CS2 Cheat v1.0";
    Overlay::DrawText(pos, watermark, ImVec4(0, 1, 0, 1), 14.0f);
}

void Misc::RenderFPSCounter() {
    Vector2 screenSize = WorldToScreen::GetScreenSize();
    Vector2 pos = Vector2(screenSize.x - 100, 10);

    std::string fpsText = "FPS: " + std::to_string(static_cast<int>(currentFPS));
    Overlay::DrawText(pos, fpsText, ImVec4(1, 1, 0, 1), 12.0f);
}

// Glow implementation
Glow::Glow() : Feature("Glow") {}

void Glow::Update() {
    if (!config.players && !config.weapons && !config.bombs) return;

    if (config.players) {
        auto players = EntityManager::GetPlayers();
        for (auto* player : players) {
            if (ShouldGlow(player)) {
                ImVec4 color = GetEntityColor(player);
                ApplyGlow(player, color);
            }
        }
    }

    // Weapons and bombs would be handled similarly
}

void Glow::ApplyGlow(C_BaseEntity* entity, const ImVec4& color) {
    // Glow implementation would require memory patching
    // This is a placeholder
}

void Glow::RemoveGlow(C_BaseEntity* entity) {
    // Remove glow implementation
}

ImVec4 Glow::GetEntityColor(C_BaseEntity* entity) {
    C_CSPlayerPawn* player = reinterpret_cast<C_CSPlayerPawn*>(entity);
    if (!player) return config.enemyColor;

    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer) return config.enemyColor;

    if (config.teamCheck && !player->IsEnemy(localPlayer)) {
        return config.teamColor;
    }

    return config.enemyColor;
}

bool Glow::ShouldGlow(C_BaseEntity* entity) {
    if (!entity) return false;

    C_CSPlayerPawn* player = reinterpret_cast<C_CSPlayerPawn*>(entity);
    if (!player || !player->IsAlive()) return false;

    C_CSPlayerPawn* localPlayer = EntityManager::GetLocalPlayer();
    if (!localPlayer || player == localPlayer) return false;

    return true;
}
