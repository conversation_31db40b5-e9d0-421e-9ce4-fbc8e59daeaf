# G<PERSON><PERSON> de Uso - Skywere CS2 Cheat

## 🚀 <PERSON><PERSON><PERSON>

### 1. Compila<PERSON>
```bash
# Execute o script de build
build.bat
```

### 2. Injeção
1. Abra o CS2
2. Use um injetor de DLL (recomendado: Process Hacker, Extreme Injector)
3. Injete `Skywere.dll` no processo `cs2.exe`
4. Use Manual Mapping se disponível para maior segurança

### 3. Controles Básicos
- **INSERT** - Abrir/fechar menu principal
- **DELETE** - <PERSON><PERSON> pânico (desativa tudo instantaneamente)
- **END** - Descarregar cheat completamente

## 🎯 Configuração do Aimbot

### Configurações Básicas
1. Abra o menu (INSERT)
2. Vá para a aba "Aimbot"
3. <PERSON><PERSON> "Enable Aimbot"

### Parâmetros Importantes
- **FOV (1-20°):** Campo de visão para detecção
  - Valores baixos (1-5): <PERSON><PERSON> pre<PERSON>, menos detecção
  - Valores altos (10-20): <PERSON><PERSON> a<PERSON>, maior risco
  
- **Smoothness (0.1-10):** Suavidade da mira
  - 0.1-1.0: Mu<PERSON> suave, mais natural
  - 1.0-5.0: Moderado
  - 5.0+: Instantâneo, mais suspeito

- **Hitbox:** Parte do corpo para mirar
  - Head: Mais dano, mais difícil
  - Neck: Bom compromisso
  - Spine: Mais fácil de acertar

### Configurações Avançadas
- **RCS (Recoil Control):** Compensa o recoil automaticamente
- **Prediction:** Prediz movimento do inimigo
- **Visibility Check:** Só mira em alvos visíveis
- **Team Check:** Não mira em aliados

## 👁️ Configuração do ESP

### Ativação
1. Aba "ESP" no menu
2. Marque "Enable ESP"
3. Configure as opções desejadas

### Opções Disponíveis
- **Boxes:** Caixas ao redor dos jogadores
- **Names:** Nomes dos jogadores
- **Health:** Barra/número de vida
- **Armor:** Valor da armadura
- **Distance:** Distância em metros
- **Skeleton:** Esqueleto dos jogadores
- **Snaplines:** Linhas do centro da tela

### Cores Personalizadas
- **Enemy Color:** Cor para inimigos (padrão: vermelho)
- **Team Color:** Cor para aliados (padrão: verde)
- **Weapon Color:** Cor para armas (padrão: amarelo)
- **Bomb Color:** Cor para bombas (padrão: laranja)

## 🔫 Triggerbot

### Configuração
1. Aba "Triggerbot"
2. Ative "Enable Triggerbot"
3. Configure o delay (recomendado: 50-100ms)

### Uso
- Mantenha pressionado ALT (padrão)
- Mire no inimigo
- O triggerbot atirará automaticamente

### Configurações
- **Delay:** Tempo antes de atirar (ms)
- **Visibility Check:** Só atira em alvos visíveis
- **Team Check:** Não atira em aliados
- **Auto Wall:** Atira através de paredes (experimental)

## 🛠️ Funcionalidades Misc

### Bunny Hop
1. Ative "Bunny Hop" na aba Misc
2. Mantenha SPACE pressionado
3. O cheat fará os pulos automaticamente

### No Flash
- Remove o efeito de flash bang
- Configure a transparência (0.0 = sem flash, 1.0 = flash normal)

### Radar Hack
- Mostra todos os inimigos no radar do jogo
- Funciona mesmo quando não deveriam estar visíveis

### Informações na Tela
- **Watermark:** Mostra nome do cheat
- **FPS Counter:** Contador de FPS
- **Spectator List:** Lista quem está te observando

## ✨ Sistema de Glow

### Configuração
1. Aba "Glow"
2. Ative as opções desejadas
3. Configure cores e brilho

### Estilos Disponíveis
- **Outline:** Apenas contorno
- **Full:** Brilho completo
- **Pulse:** Efeito pulsante

## 💾 Sistema de Configuração

### Salvando Configurações
1. Aba "Config"
2. Digite um nome para a configuração
3. Clique em "Save"

### Carregando Configurações
1. Selecione uma configuração da lista
2. Clique em "Load"

### Configurações Padrão
- `default.json` - Configuração segura para iniciantes
- Crie suas próprias configurações personalizadas

## ⚠️ Dicas de Segurança

### Para Evitar Detecção
1. **Use valores realistas:**
   - FOV baixo (1-3°)
   - Smoothness alto (2-5)
   - Não use auto shoot

2. **Não abuse das funções:**
   - Use ESP moderadamente
   - Evite triggerbot em situações óbvias
   - Desative em rounds importantes

3. **Modo Pânico:**
   - Sempre tenha DELETE configurado
   - Use quando suspeitar de admin/overwatch
   - Pratique o uso rápido

### Configurações Seguras para MM
```json
{
  "aimbot_fov": 2.0,
  "aimbot_smoothness": 3.0,
  "aimbot_auto_shoot": false,
  "esp_enabled": false,
  "triggerbot_delay": 100.0
}
```

## 🔧 Solução de Problemas

### Cheat não funciona
1. Verifique se foi injetado corretamente
2. Execute CS2 como administrador
3. Verifique se os offsets estão atualizados

### Menu não abre
1. Tente pressionar INSERT várias vezes
2. Verifique se não há conflito com outros programas
3. Reinicie o CS2 e injete novamente

### Aimbot não funciona
1. Verifique se está habilitado
2. Teste com FOV maior temporariamente
3. Desative visibility check para testar

### ESP não aparece
1. Verifique se DirectX está funcionando
2. Teste em modo janela
3. Verifique configurações de overlay

## 📞 Suporte

Para problemas técnicos:
1. Verifique se os offsets estão atualizados
2. Teste com configuração padrão
3. Verifique logs de erro no Output do Visual Studio

## ⚖️ Lembrete Legal

- Este cheat é apenas para fins educacionais
- Uso em servidores oficiais pode resultar em ban
- Sempre respeite os termos de serviço
- Use com responsabilidade
