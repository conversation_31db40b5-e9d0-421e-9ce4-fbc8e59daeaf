#pragma once

// Windows Headers
#include <windows.h>
#include <winternl.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <shlwapi.h>

// Standard Library
#include <iostream>
#include <vector>
#include <string>
#include <memory>
#include <fstream>
#include <random>
#include <thread>
#include <chrono>
#include <algorithm>

// Forward declarations for project headers
class Injector;
class ManualMapper;
class ProcessManager;
class Utils;

// Macros
#define LOG(msg) std::cout << "[INJECTOR] " << msg << std::endl
#define LOGF(fmt, ...) printf("[INJECTOR] " fmt "\n", __VA_ARGS__)
#define ERROR(msg) std::cerr << "[ERROR] " << msg << std::endl
#define ERRORF(fmt, ...) fprintf(stderr, "[ERROR] " fmt "\n", __VA_ARGS__)

// NTDLL Function Prototypes
extern "C" {
    NTSTATUS NTAPI NtCreateThreadEx(
        PHANDLE ThreadHandle,
        ACCESS_MASK DesiredAccess,
        POBJECT_ATTRIBUTES ObjectAttributes,
        HANDLE ProcessHandle,
        PVOID StartRoutine,
        PVOID Argument,
        ULONG CreateFlags,
        SIZE_T ZeroBits,
        SIZE_T StackSize,
        SIZE_T MaximumStackSize,
        PVOID AttributeList
    );

    NTSTATUS NTAPI NtAllocateVirtualMemory(
        HANDLE ProcessHandle,
        PVOID* BaseAddress,
        ULONG_PTR ZeroBits,
        PSIZE_T RegionSize,
        ULONG AllocationType,
        ULONG Protect
    );

    NTSTATUS NTAPI NtWriteVirtualMemory(
        HANDLE ProcessHandle,
        PVOID BaseAddress,
        PVOID Buffer,
        SIZE_T BufferSize,
        PSIZE_T NumberOfBytesWritten
    );

    NTSTATUS NTAPI NtProtectVirtualMemory(
        HANDLE ProcessHandle,
        PVOID* BaseAddress,
        PSIZE_T RegionSize,
        ULONG NewProtect,
        PULONG OldProtect
    );

    NTSTATUS NTAPI NtQueryInformationProcess(
        HANDLE ProcessHandle,
        PROCESSINFOCLASS ProcessInformationClass,
        PVOID ProcessInformation,
        ULONG ProcessInformationLength,
        PULONG ReturnLength
    );
}

// Constants (defined in injector.h)
// constexpr DWORD INJECTION_TIMEOUT = 10000;
// constexpr DWORD MAX_RETRIES = 3;
// constexpr DWORD SLEEP_INTERVAL = 100;

// Forward declarations for enums and structs
enum class InjectionMethod;
enum class ProcessArchitecture;
struct InjectionConfig;
struct ProcessInfo;

// Global Variables (defined in main.cpp)
extern InjectionConfig g_config;
extern bool g_verbose;
