#include "pch.h"
#include "manual_map.h"
#include "injector.h"
#include "utils.h"

bool ManualMapper::MapDLL(HANDLE hProcess, const std::wstring& dllPath) {
    if (g_verbose) LOG("Starting manual mapping for: " + Utils::WStringToString(dllPath));
    
    // Read DLL file
    auto dllData = ReadFileToMemory(dllPath);
    if (dllData.empty()) {
        ERROR("Failed to read DLL file");
        return false;
    }
    
    return MapDLL(hProcess, dllData);
}

bool ManualMapper::MapDLL(HANDLE hProcess, const std::vector<BYTE>& dllData) {
    if (!ValidatePE(dllData)) {
        ERROR("Invalid PE file");
        return false;
    }
    
    // Map image to target process
    LPVOID imageBase = MapImage(hProcess, dllData);
    if (!imageBase) {
        ERROR("Failed to map image");
        return false;
    }
    
    if (g_verbose) LOGF("Image mapped at: 0x%p", imageBase);
    
    // Relocate image
    if (!RelocateImage(hProcess, imageBase, dllData)) {
        ERROR("Failed to relocate image");
        return false;
    }
    
    // Resolve imports
    if (!ResolveImports(hProcess, imageBase, dllData)) {
        ERROR("Failed to resolve imports");
        return false;
    }
    
    // Execute entry point
    if (!ExecuteEntryPoint(hProcess, imageBase, dllData)) {
        ERROR("Failed to execute entry point");
        return false;
    }
    
    // Apply evasion techniques
    if (g_config.eraseHeaders) {
        Injector::EraseHeaders(hProcess, imageBase);
    }
    
    if (g_config.hideFromPEB) {
        Injector::HideFromPEB(hProcess, imageBase);
    }
    
    if (g_verbose) LOG("Manual mapping completed successfully");
    return true;
}

LPVOID ManualMapper::MapImage(HANDLE hProcess, const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) {
        ERROR("Failed to get NT headers");
        return nullptr;
    }
    
    SIZE_T imageSize = ntHeaders->OptionalHeader.SizeOfImage;
    
    // Allocate memory for the image
    LPVOID imageBase = nullptr;
    
    if (g_config.randomizeHeaders) {
        // Try to allocate at a random address
        for (int i = 0; i < 10; ++i) {
            LPVOID randomBase = (LPVOID)(0x10000000ULL + (Utils::GenerateRandomDWORD() % 0x70000000ULL));
            imageBase = VirtualAllocEx(hProcess, randomBase, imageSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
            if (imageBase) break;
        }
    }
    
    if (!imageBase) {
        // Allocate at any available address
        imageBase = VirtualAllocEx(hProcess, nullptr, imageSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    }
    
    if (!imageBase) {
        LogMappingError("VirtualAllocEx", GetLastError());
        return nullptr;
    }
    
    // Copy headers
    SIZE_T headersSize = ntHeaders->OptionalHeader.SizeOfHeaders;
    if (!Injector::WriteMemory(hProcess, imageBase, dllData.data(), headersSize)) {
        LogMappingError("WriteProcessMemory (headers)", GetLastError());
        VirtualFreeEx(hProcess, imageBase, 0, MEM_RELEASE);
        return nullptr;
    }
    
    // Copy sections
    PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
    for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i, ++sectionHeader) {
        if (sectionHeader->SizeOfRawData == 0) continue;
        
        LPVOID sectionBase = (LPVOID)((ULONG_PTR)imageBase + sectionHeader->VirtualAddress);
        const BYTE* sectionData = dllData.data() + sectionHeader->PointerToRawData;
        
        if (!Injector::WriteMemory(hProcess, sectionBase, sectionData, sectionHeader->SizeOfRawData)) {
            LogMappingError("WriteProcessMemory (section)", GetLastError());
            VirtualFreeEx(hProcess, imageBase, 0, MEM_RELEASE);
            return nullptr;
        }
        
        // Set section protection
        DWORD protection = PAGE_READONLY;
        if (sectionHeader->Characteristics & IMAGE_SCN_MEM_EXECUTE) {
            if (sectionHeader->Characteristics & IMAGE_SCN_MEM_WRITE) {
                protection = PAGE_EXECUTE_READWRITE;
            } else {
                protection = PAGE_EXECUTE_READ;
            }
        } else if (sectionHeader->Characteristics & IMAGE_SCN_MEM_WRITE) {
            protection = PAGE_READWRITE;
        }
        
        DWORD oldProtection;
        if (!Injector::ProtectMemory(hProcess, sectionBase, sectionHeader->Misc.VirtualSize, protection, &oldProtection)) {
            if (g_verbose) LOG("Warning: Failed to set section protection");
        }
    }
    
    return imageBase;
}

bool ManualMapper::ResolveImports(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return false;
    
    PIMAGE_IMPORT_DESCRIPTOR importDesc = GetImportDescriptor(dllData);
    if (!importDesc) {
        // No imports to resolve
        return true;
    }
    
    while (importDesc->Name) {
        // Get module name
        const char* moduleName = (const char*)(dllData.data() + RVAToOffset(dllData, importDesc->Name));
        
        // Load the module in target process (simplified)
        HMODULE hModule = LoadLibraryA(moduleName);
        if (!hModule) {
            ERROR("Failed to load import module: " + std::string(moduleName));
            return false;
        }
        
        // Resolve function addresses
        PIMAGE_THUNK_DATA thunk = (PIMAGE_THUNK_DATA)(dllData.data() + RVAToOffset(dllData, importDesc->FirstThunk));
        PIMAGE_THUNK_DATA originalThunk = (PIMAGE_THUNK_DATA)(dllData.data() + RVAToOffset(dllData, importDesc->OriginalFirstThunk));
        
        while (originalThunk->u1.AddressOfData) {
            FARPROC functionAddress = nullptr;
            
            if (originalThunk->u1.Ordinal & IMAGE_ORDINAL_FLAG) {
                // Import by ordinal
                WORD ordinal = IMAGE_ORDINAL(originalThunk->u1.Ordinal);
                functionAddress = GetProcAddress(hModule, (LPCSTR)ordinal);
            } else {
                // Import by name
                PIMAGE_IMPORT_BY_NAME importByName = (PIMAGE_IMPORT_BY_NAME)(dllData.data() + RVAToOffset(dllData, originalThunk->u1.AddressOfData));
                functionAddress = GetProcAddress(hModule, importByName->Name);
            }
            
            if (!functionAddress) {
                ERROR("Failed to resolve import function");
                return false;
            }
            
            // Write function address to IAT
            LPVOID iatEntry = (LPVOID)((ULONG_PTR)imageBase + importDesc->FirstThunk + 
                                      ((ULONG_PTR)thunk - (ULONG_PTR)(dllData.data() + RVAToOffset(dllData, importDesc->FirstThunk))));
            
            if (!Injector::WriteMemory(hProcess, iatEntry, &functionAddress, sizeof(functionAddress))) {
                LogMappingError("WriteProcessMemory (IAT)", GetLastError());
                return false;
            }
            
            ++thunk;
            ++originalThunk;
        }
        
        ++importDesc;
    }
    
    return true;
}

bool ManualMapper::RelocateImage(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return false;
    
    ULONG_PTR preferredBase = ntHeaders->OptionalHeader.ImageBase;
    ULONG_PTR actualBase = (ULONG_PTR)imageBase;
    
    if (preferredBase == actualBase) {
        // No relocation needed
        return true;
    }
    
    LONGLONG delta = actualBase - preferredBase;
    
    PIMAGE_BASE_RELOCATION relocation = GetRelocationTable(dllData);
    if (!relocation) {
        // No relocations
        return true;
    }
    
    while (relocation->VirtualAddress) {
        LPVOID relocBase = (LPVOID)(actualBase + relocation->VirtualAddress);
        DWORD numRelocations = (relocation->SizeOfBlock - sizeof(IMAGE_BASE_RELOCATION)) / sizeof(WORD);
        WORD* relocData = (WORD*)((ULONG_PTR)relocation + sizeof(IMAGE_BASE_RELOCATION));
        
        for (DWORD i = 0; i < numRelocations; ++i) {
            WORD relocInfo = relocData[i];
            WORD type = relocInfo >> 12;
            WORD offset = relocInfo & 0xFFF;
            
            if (type == IMAGE_REL_BASED_ABSOLUTE) {
                continue;
            }
            
            LPVOID relocAddress = (LPVOID)((ULONG_PTR)relocBase + offset);
            
            if (type == IMAGE_REL_BASED_HIGHLOW || type == IMAGE_REL_BASED_DIR64) {
                ULONG_PTR value;
                SIZE_T bytesRead;
                
                if (!ReadProcessMemory(hProcess, relocAddress, &value, sizeof(value), &bytesRead)) {
                    LogMappingError("ReadProcessMemory (relocation)", GetLastError());
                    return false;
                }
                
                value += delta;
                
                if (!Injector::WriteMemory(hProcess, relocAddress, &value, sizeof(value))) {
                    LogMappingError("WriteProcessMemory (relocation)", GetLastError());
                    return false;
                }
            }
        }
        
        relocation = (PIMAGE_BASE_RELOCATION)((ULONG_PTR)relocation + relocation->SizeOfBlock);
    }
    
    return true;
}

bool ManualMapper::ExecuteEntryPoint(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return false;
    
    if (ntHeaders->OptionalHeader.AddressOfEntryPoint == 0) {
        // No entry point
        return true;
    }
    
    // Create shellcode to call DllMain
    ManualMapData mapData = {};
    mapData.imageBase = imageBase;
    mapData.fnLoadLibraryA = LoadLibraryA;
    mapData.fnGetProcAddress = GetProcAddress;
    mapData.fnDllMain = (BOOL(WINAPI*)(HMODULE, DWORD, LPVOID))((ULONG_PTR)imageBase + ntHeaders->OptionalHeader.AddressOfEntryPoint);
    
    // Allocate memory for shellcode and data
    SIZE_T shellcodeSize = (ULONG_PTR)ShellcodeEnd - (ULONG_PTR)MappingShellcode;
    SIZE_T totalSize = shellcodeSize + sizeof(ManualMapData);
    
    LPVOID shellcodeBase = VirtualAllocEx(hProcess, nullptr, totalSize, MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!shellcodeBase) {
        LogMappingError("VirtualAllocEx (shellcode)", GetLastError());
        return false;
    }
    
    // Write data structure
    if (!Injector::WriteMemory(hProcess, shellcodeBase, &mapData, sizeof(mapData))) {
        LogMappingError("WriteProcessMemory (map data)", GetLastError());
        VirtualFreeEx(hProcess, shellcodeBase, 0, MEM_RELEASE);
        return false;
    }
    
    // Write shellcode
    LPVOID shellcodeAddr = (LPVOID)((ULONG_PTR)shellcodeBase + sizeof(ManualMapData));
    if (!Injector::WriteMemory(hProcess, shellcodeAddr, (LPVOID)MappingShellcode, shellcodeSize)) {
        LogMappingError("WriteProcessMemory (shellcode)", GetLastError());
        VirtualFreeEx(hProcess, shellcodeBase, 0, MEM_RELEASE);
        return false;
    }
    
    // Execute shellcode
    HANDLE hThread = Injector::CreateRemoteThread(hProcess, (LPTHREAD_START_ROUTINE)shellcodeAddr, shellcodeBase);
    if (!hThread) {
        LogMappingError("CreateRemoteThread", GetLastError());
        VirtualFreeEx(hProcess, shellcodeBase, 0, MEM_RELEASE);
        return false;
    }
    
    // Wait for completion
    DWORD waitResult = WaitForSingleObject(hThread, INJECTION_TIMEOUT);
    CloseHandle(hThread);
    
    // Cleanup
    VirtualFreeEx(hProcess, shellcodeBase, 0, MEM_RELEASE);
    
    if (waitResult != WAIT_OBJECT_0) {
        ERROR("DllMain execution timeout");
        return false;
    }
    
    return true;
}

PIMAGE_NT_HEADERS ManualMapper::GetNtHeaders(const std::vector<BYTE>& dllData) {
    if (dllData.size() < sizeof(IMAGE_DOS_HEADER)) {
        return nullptr;
    }

    PIMAGE_DOS_HEADER dosHeader = (PIMAGE_DOS_HEADER)dllData.data();
    if (dosHeader->e_magic != IMAGE_DOS_SIGNATURE) {
        return nullptr;
    }

    if (dllData.size() < dosHeader->e_lfanew + sizeof(IMAGE_NT_HEADERS)) {
        return nullptr;
    }

    PIMAGE_NT_HEADERS ntHeaders = (PIMAGE_NT_HEADERS)(dllData.data() + dosHeader->e_lfanew);
    if (ntHeaders->Signature != IMAGE_NT_SIGNATURE) {
        return nullptr;
    }

    return ntHeaders;
}

PIMAGE_SECTION_HEADER ManualMapper::GetSectionHeader(const std::vector<BYTE>& dllData, const char* sectionName) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return nullptr;

    PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
    for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i, ++sectionHeader) {
        if (strncmp((const char*)sectionHeader->Name, sectionName, IMAGE_SIZEOF_SHORT_NAME) == 0) {
            return sectionHeader;
        }
    }

    return nullptr;
}

PIMAGE_IMPORT_DESCRIPTOR ManualMapper::GetImportDescriptor(const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return nullptr;

    DWORD importRVA = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_IMPORT].VirtualAddress;
    if (importRVA == 0) return nullptr;

    DWORD importOffset = RVAToOffset(dllData, importRVA);
    if (importOffset == 0) return nullptr;

    return (PIMAGE_IMPORT_DESCRIPTOR)(dllData.data() + importOffset);
}

PIMAGE_BASE_RELOCATION ManualMapper::GetRelocationTable(const std::vector<BYTE>& dllData) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return nullptr;

    DWORD relocRVA = ntHeaders->OptionalHeader.DataDirectory[IMAGE_DIRECTORY_ENTRY_BASERELOC].VirtualAddress;
    if (relocRVA == 0) return nullptr;

    DWORD relocOffset = RVAToOffset(dllData, relocRVA);
    if (relocOffset == 0) return nullptr;

    return (PIMAGE_BASE_RELOCATION)(dllData.data() + relocOffset);
}

std::vector<BYTE> ManualMapper::ReadFileToMemory(const std::wstring& filePath) {
    return Utils::ReadFile(filePath);
}

bool ManualMapper::ValidatePE(const std::vector<BYTE>& dllData) {
    return GetNtHeaders(dllData) != nullptr;
}

DWORD ManualMapper::RVAToOffset(const std::vector<BYTE>& dllData, DWORD rva) {
    PIMAGE_NT_HEADERS ntHeaders = GetNtHeaders(dllData);
    if (!ntHeaders) return 0;

    PIMAGE_SECTION_HEADER sectionHeader = IMAGE_FIRST_SECTION(ntHeaders);
    for (WORD i = 0; i < ntHeaders->FileHeader.NumberOfSections; ++i, ++sectionHeader) {
        if (rva >= sectionHeader->VirtualAddress &&
            rva < sectionHeader->VirtualAddress + sectionHeader->Misc.VirtualSize) {
            return rva - sectionHeader->VirtualAddress + sectionHeader->PointerToRawData;
        }
    }

    // If not in any section, assume it's in headers
    if (rva < ntHeaders->OptionalHeader.SizeOfHeaders) {
        return rva;
    }

    return 0;
}

LPVOID ManualMapper::GetProcAddressEx(HANDLE hProcess, HMODULE hModule, const char* procName) {
    // This is a simplified implementation
    // In a real scenario, you'd need to parse the export table of the remote module
    return GetProcAddress(hModule, procName);
}

DWORD WINAPI ManualMapper::MappingShellcode(ManualMapData* data) {
    if (!data) return FALSE;

    // Call DllMain with DLL_PROCESS_ATTACH
    BOOL result = data->fnDllMain((HMODULE)data->imageBase, DLL_PROCESS_ATTACH, nullptr);

    return result;
}

void ManualMapper::ShellcodeEnd() {
    // Marker function to calculate shellcode size
}

void ManualMapper::LogMappingError(const std::string& function, DWORD errorCode) {
    std::string errorMsg = "ManualMapper::" + function + " failed with error: " +
                          std::to_string(errorCode) + " (" + Utils::GetErrorString(errorCode) + ")";
    ERROR(errorMsg);
}

bool ManualMapper::ValidateMapping(HANDLE hProcess, LPVOID imageBase, const std::vector<BYTE>& dllData) {
    // Read back some data to verify the mapping
    BYTE buffer[16];
    SIZE_T bytesRead;

    if (!ReadProcessMemory(hProcess, imageBase, buffer, sizeof(buffer), &bytesRead)) {
        return false;
    }

    // Compare with original data
    return memcmp(buffer, dllData.data(), sizeof(buffer)) == 0;
}
