#include "pch.h"
#include "injector.h"
#include "process.h"
#include "utils.h"

// Global variables
InjectionConfig g_config;
bool g_verbose = false;

void PrintBanner() {
    std::wcout << L"\n";
    std::wcout << L"  ███████╗██╗  ██╗██╗   ██╗██╗    ██╗███████╗██████╗ ███████╗\n";
    std::wcout << L"  ██╔════╝██║ ██╔╝╚██╗ ██╔╝██║    ██║██╔════╝██╔══██╗██╔════╝\n";
    std::wcout << L"  ███████╗█████╔╝  ╚████╔╝ ██║ █╗ ██║█████╗  ██████╔╝█████╗  \n";
    std::wcout << L"  ╚════██║██╔═██╗   ╚██╔╝  ██║███╗██║██╔══╝  ██╔══██╗██╔══╝  \n";
    std::wcout << L"  ███████║██║  ██╗   ██║   ╚███╔███╔╝███████╗██║  ██║███████╗\n";
    std::wcout << L"  ╚══════╝╚═╝  ╚═╝   ╚═╝    ╚══╝╚══╝ ╚══════╝╚═╝  ╚═╝╚══════╝\n";
    std::wcout << L"\n";
    std::wcout << L"                    Advanced DLL Injector v2.0\n";
    std::wcout << L"                  Developed for Educational Purposes\n";
    std::wcout << L"  ================================================================\n\n";
}

void PrintUsage() {
    std::wcout << L"Usage: Injector.exe [options] <process_name> <dll_path>\n\n";
    std::wcout << L"Options:\n";
    std::wcout << L"  -m, --method <method>     Injection method (loadlibrary, manualmap, hijack, hollow)\n";
    std::wcout << L"  -p, --pid <process_id>    Target process ID instead of name\n";
    std::wcout << L"  -w, --wait                Wait for process if not running\n";
    std::wcout << L"  -s, --stealth             Enable all stealth features\n";
    std::wcout << L"  -d, --delay <ms>          Delay before injection (milliseconds)\n";
    std::wcout << L"  -v, --verbose             Enable verbose output\n";
    std::wcout << L"  -h, --help                Show this help message\n\n";
    std::wcout << L"Examples:\n";
    std::wcout << L"  Injector.exe cs2.exe Skywere.dll\n";
    std::wcout << L"  Injector.exe -m manualmap -s cs2.exe Skywere.dll\n";
    std::wcout << L"  Injector.exe -p 1234 -d 5000 Skywere.dll\n";
    std::wcout << L"  Injector.exe -w -v notepad.exe test.dll\n\n";
}

bool InteractiveMode(std::wstring& processName, std::wstring& dllPath) {
    std::wcout << L"\n=== INTERACTIVE MODE ===\n\n";

    std::wcout << L"Enter target process name (e.g., cs2.exe): ";
    std::getline(std::wcin, processName);

    if (processName.empty()) {
        std::wcout << L"Process name cannot be empty!\n";
        return false;
    }

    std::wcout << L"Enter DLL path (e.g., Skywere.dll): ";
    std::getline(std::wcin, dllPath);

    if (dllPath.empty()) {
        std::wcout << L"DLL path cannot be empty!\n";
        return false;
    }

    // Check if DLL file exists
    if (GetFileAttributesW(dllPath.c_str()) == INVALID_FILE_ATTRIBUTES) {
        std::wcout << L"DLL file not found: " << dllPath << L"\n";
        std::wcout << L"Continue anyway? (y/n): ";
        std::wstring response;
        std::getline(std::wcin, response);
        if (response != L"y" && response != L"Y") {
            return false;
        }
    }

    return true;
}

InjectionMethod ParseMethod(const std::wstring& methodStr) {
    std::wstring method = Utils::ToLower(methodStr);
    
    if (method == L"loadlibrary" || method == L"ll") {
        return InjectionMethod::LOADLIBRARY;
    } else if (method == L"manualmap" || method == L"mm") {
        return InjectionMethod::MANUAL_MAP;
    } else if (method == L"hijack" || method == L"th") {
        return InjectionMethod::THREAD_HIJACKING;
    } else if (method == L"hollow" || method == L"ph") {
        return InjectionMethod::PROCESS_HOLLOWING;
    }
    
    return InjectionMethod::MANUAL_MAP; // Default
}

bool ParseArguments(int argc, wchar_t* argv[], std::wstring& processName, std::wstring& dllPath, 
                   DWORD& processId, bool& waitForProcess, bool& useProcessId) {
    
    if (argc < 3) {
        return false;
    }
    
    InjectionConfig config;
    config.method = InjectionMethod::MANUAL_MAP;
    config.randomizeHeaders = true;
    config.eraseHeaders = true;
    config.hideFromPEB = true;
    config.antiDebug = true;
    config.delayExecution = false;
    config.delayMs = 0;
    config.scrambleImports = true;
    config.useNtApi = true;
    
    processId = 0;
    waitForProcess = false;
    useProcessId = false;
    
    for (int i = 1; i < argc; ++i) {
        std::wstring arg = argv[i];
        
        if (arg == L"-m" || arg == L"--method") {
            if (i + 1 < argc) {
                config.method = ParseMethod(argv[++i]);
            }
        } else if (arg == L"-p" || arg == L"--pid") {
            if (i + 1 < argc) {
                processId = std::wcstoul(argv[++i], nullptr, 10);
                useProcessId = true;
            }
        } else if (arg == L"-w" || arg == L"--wait") {
            waitForProcess = true;
        } else if (arg == L"-s" || arg == L"--stealth") {
            config.randomizeHeaders = true;
            config.eraseHeaders = true;
            config.hideFromPEB = true;
            config.antiDebug = true;
            config.scrambleImports = true;
        } else if (arg == L"-d" || arg == L"--delay") {
            if (i + 1 < argc) {
                config.delayMs = std::wcstoul(argv[++i], nullptr, 10);
                config.delayExecution = config.delayMs > 0;
            }
        } else if (arg == L"-v" || arg == L"--verbose") {
            g_verbose = true;
        } else if (arg == L"-h" || arg == L"--help") {
            return false;
        } else if (processName.empty() && !useProcessId) {
            processName = arg;
        } else if (dllPath.empty()) {
            dllPath = arg;
        }
    }
    
    Injector::SetConfig(config);
    Injector::SetVerbose(g_verbose);
    
    return (!processName.empty() || useProcessId) && !dllPath.empty();
}

bool PerformInjection(const std::wstring& processName, const std::wstring& dllPath, 
                     DWORD processId, bool waitForProcess, bool useProcessId) {
    
    LOG("Starting injection process...");
    
    // Validate DLL
    if (!Injector::ValidateDLL(dllPath)) {
        ERROR("Invalid DLL file: " + Utils::WStringToString(dllPath));
        return false;
    }
    
    // Wait for process if requested
    if (waitForProcess && !useProcessId) {
        LOG("Waiting for process: " + Utils::WStringToString(processName));
        if (!ProcessManager::WaitForProcess(processName, 30000)) {
            ERROR("Process not found within timeout period");
            return false;
        }
    }
    
    // Perform injection
    bool success = false;
    InjectionConfig config = Injector::GetConfig();
    
    if (useProcessId) {
        LOG("Injecting into process ID: " + std::to_string(processId));
        success = Injector::InjectDLL(processId, dllPath, config.method);
    } else {
        LOG("Injecting into process: " + Utils::WStringToString(processName));
        success = Injector::InjectDLL(processName, dllPath, config.method);
    }
    
    if (success) {
        LOG("Injection completed successfully!");
        
        // Apply post-injection stealth measures
        if (config.delayExecution) {
            LOG("Applying execution delay: " + std::to_string(config.delayMs) + "ms");
            Utils::Sleep(config.delayMs);
        }
        
    } else {
        ERROR("Injection failed!");
    }
    
    return success;
}

int wmain(int argc, wchar_t* argv[]) {
    // Set console properties
    Utils::SetConsoleTitle(L"Skywere Advanced Injector");
    
    PrintBanner();
    
    // Check for admin privileges
    if (!Utils::IsRunningAsAdmin()) {
        std::wcout << L"[WARNING] Not running as administrator. Some injection methods may fail.\n\n";
    }
    
    // Parse command line arguments
    std::wstring processName, dllPath;
    DWORD processId = 0;
    bool waitForProcess = false, useProcessId = false;

    if (argc < 2) {
        // Interactive mode when no arguments provided
        if (!InteractiveMode(processName, dllPath)) {
            std::wcout << L"Press any key to exit...\n";
            _getwch();
            return 1;
        }
    } else if (!ParseArguments(argc, argv, processName, dllPath, processId, waitForProcess, useProcessId)) {
        PrintUsage();
        std::wcout << L"Press any key to exit...\n";
        _getwch();
        return 1;
    }
    
    // Initialize injector
    if (!Injector::Initialize()) {
        ERROR("Failed to initialize injector");
        std::wcout << L"Press any key to exit...\n";
        _getwch();
        return 1;
    }
    
    // Perform anti-detection checks
    if (Utils::IsRunningInVM()) {
        std::wcout << L"[WARNING] Virtual machine detected. Injection may be monitored.\n";
    }
    
    if (Utils::IsRunningInSandbox()) {
        std::wcout << L"[WARNING] Sandbox environment detected. Injection may be monitored.\n";
    }
    
    if (Utils::IsBeingDebugged()) {
        std::wcout << L"[WARNING] Debugger detected. Injection may be monitored.\n";
    }
    
    // Perform injection
    bool success = PerformInjection(processName, dllPath, processId, waitForProcess, useProcessId);
    
    // Cleanup
    Injector::Shutdown();
    
    if (success) {
        std::wcout << L"\n[SUCCESS] DLL injected successfully!\n";
        std::wcout << L"[INFO] Press any key to exit...\n";
    } else {
        std::wcout << L"\n[FAILED] Injection failed. Check the error messages above.\n";
        std::wcout << L"[INFO] Press any key to exit...\n";
    }
    
    _getwch();
    return success ? 0 : 1;
}
