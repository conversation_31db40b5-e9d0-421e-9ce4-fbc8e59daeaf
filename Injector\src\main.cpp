#include "pch.h"
#include "injector.h"
#include "utils.h"

int wmain(int argc, wchar_t* argv[]) {
    SetConsoleTitleW(L"Skywere Injector");
    
    std::wcout << L"=== SKYWERE INJECTOR ===\n\n";
    
    std::wstring processName, dllPath;
    
    if (argc >= 3) {
        // Command line mode
        processName = argv[1];
        dllPath = argv[2];
    } else {
        // Interactive mode
        std::wcout << L"Choose option:\n";
        std::wcout << L"1 - Quick inject (cs2.exe + Skywere.dll)\n";
        std::wcout << L"2 - Custom inject\n";
        std::wcout << L"Choice: ";
        
        wchar_t choice = _getwch();
        std::wcout << choice << L"\n\n";
        
        if (choice == L'1') {
            processName = L"cs2.exe";
            dllPath = L"Skywere.dll";
            std::wcout << L"Using: cs2.exe + Skywere.dll\n\n";
        } else if (choice == L'2') {
            std::wcout << L"Process name: ";
            std::getline(std::wcin, processName);
            std::wcout << L"DLL path: ";
            std::getline(std::wcin, dllPath);
        } else {
            std::wcout << L"Invalid choice!\n";
            std::wcout << L"Press any key to exit...\n";
            _getwch();
            return 1;
        }
    }
    
    if (processName.empty() || dllPath.empty()) {
        std::wcout << L"Error: Missing process name or DLL path!\n";
        std::wcout << L"Press any key to exit...\n";
        _getwch();
        return 1;
    }
    
    std::wcout << L"Target Process: " << processName << L"\n";
    std::wcout << L"DLL Path: " << dllPath << L"\n\n";
    
    // Initialize injector
    if (!Injector::Initialize()) {
        std::wcout << L"Failed to initialize injector!\n";
        std::wcout << L"Press any key to exit...\n";
        _getwch();
        return 1;
    }
    
    // Find process
    DWORD processId = Utils::GetProcessId(processName);
    if (processId == 0) {
        std::wcout << L"Process not found: " << processName << L"\n";
        std::wcout << L"Press any key to exit...\n";
        _getwch();
        Injector::Shutdown();
        return 1;
    }
    
    std::wcout << L"Found process ID: " << processId << L"\n";
    
    // Perform injection
    std::wcout << L"Injecting DLL...\n";
    bool success = Injector::InjectDLL(processId, dllPath, InjectionMethod::LOADLIBRARY);
    
    if (success) {
        std::wcout << L"\n[SUCCESS] DLL injected successfully!\n";
    } else {
        std::wcout << L"\n[FAILED] Injection failed!\n";
    }
    
    // Cleanup
    Injector::Shutdown();
    
    std::wcout << L"Press any key to exit...\n";
    _getwch();
    
    return success ? 0 : 1;
}
