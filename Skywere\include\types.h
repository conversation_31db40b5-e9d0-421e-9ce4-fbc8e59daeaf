#pragma once

#include <windows.h>
#include <cmath>
#include <math.h>

// Entity handle utilities
class EntityHandle {
public:
    EntityHandle() : handle(0) {}
    EntityHandle(uint32_t h) : handle(h) {}

    bool IsValid() const { return handle != 0 && handle != 0xFFFFFFFF; }
    uint32_t GetIndex() const { return handle & 0x7FFF; }
    uint32_t GetSerial() const { return handle >> 15; }
    uint32_t GetHandle() const { return handle; }

    operator uint32_t() const { return handle; }
    operator bool() const { return IsValid(); }

private:
    uint32_t handle;
};

// Vector classes for game math
struct Vector2 {
    float x, y;
    
    Vector2() : x(0), y(0) {}
    Vector2(float x, float y) : x(x), y(y) {}
    
    Vector2 operator+(const Vector2& other) const { return Vector2(x + other.x, y + other.y); }
    Vector2 operator-(const Vector2& other) const { return Vector2(x - other.x, y - other.y); }
    Vector2 operator*(float scalar) const { return Vector2(x * scalar, y * scalar); }
    Vector2 operator/(float scalar) const { return Vector2(x / scalar, y / scalar); }
    
    float Length() const { return sqrtf(x * x + y * y); }
    float LengthSqr() const { return x * x + y * y; }
    Vector2 Normalized() const { float len = Length(); return len > 0 ? *this / len : Vector2(); }
};

struct Vector3 {
    float x, y, z;
    
    Vector3() : x(0), y(0), z(0) {}
    Vector3(float x, float y, float z) : x(x), y(y), z(z) {}
    
    Vector3 operator+(const Vector3& other) const { return Vector3(x + other.x, y + other.y, z + other.z); }
    Vector3 operator-(const Vector3& other) const { return Vector3(x - other.x, y - other.y, z - other.z); }
    Vector3 operator*(float scalar) const { return Vector3(x * scalar, y * scalar, z * scalar); }
    Vector3 operator/(float scalar) const { return Vector3(x / scalar, y / scalar, z / scalar); }
    
    float Length() const { return sqrtf(x * x + y * y + z * z); }
    float LengthSqr() const { return x * x + y * y + z * z; }
    float Length2D() const { return sqrtf(x * x + y * y); }
    Vector3 Normalized() const { float len = Length(); return len > 0 ? *this / len : Vector3(); }
    
    float Dot(const Vector3& other) const { return x * other.x + y * other.y + z * other.z; }
    Vector3 Cross(const Vector3& other) const {
        return Vector3(y * other.z - z * other.y, z * other.x - x * other.z, x * other.y - y * other.x);
    }
};

using QAngle = Vector3;
