{"client.dll": {"classes": {"ActiveModelConfig_t": {"fields": {"m_AssociatedEntities": 56, "m_AssociatedEntityNames": 80, "m_Handle": 40, "m_Name": 48}, "metadata": [{"name": "m_<PERSON>le", "type": "NetworkVarNames", "type_name": "ModelConfigHandle_t"}, {"name": "m_Name", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_AssociatedEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_AssociatedEntityNames", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": null}, "CAnimGraphNetworkedVariables": {"fields": {"m_OwnerOnlyPredNetBoolVariables": 248, "m_OwnerOnlyPredNetByteVariables": 272, "m_OwnerOnlyPredNetFloatVariables": 392, "m_OwnerOnlyPredNetGlobalSymbolVariables": 464, "m_OwnerOnlyPredNetIntVariables": 320, "m_OwnerOnlyPredNetQuaternionVariables": 440, "m_OwnerOnlyPredNetUInt16Variables": 296, "m_OwnerOnlyPredNetUInt32Variables": 344, "m_OwnerOnlyPredNetUInt64Variables": 368, "m_OwnerOnlyPredNetVectorVariables": 416, "m_PredNetBoolVariables": 8, "m_PredNetByteVariables": 32, "m_PredNetFloatVariables": 152, "m_PredNetGlobalSymbolVariables": 224, "m_PredNetIntVariables": 80, "m_PredNetQuaternionVariables": 200, "m_PredNetUInt16Variables": 56, "m_PredNetUInt32Variables": 104, "m_PredNetUInt64Variables": 128, "m_PredNetVectorVariables": 176, "m_flLastTeleportTime": 500, "m_nBoolVariablesCount": 488, "m_nOwnerOnlyBoolVariablesCount": 492, "m_nRandomSeedOffset": 496}, "metadata": [{"name": "m_PredNetBoolVariables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_PredNetByteVariables", "type": "NetworkVarNames", "type_name": "byte"}, {"name": "m_PredNetUInt16Variables", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_PredNetIntVariables", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_PredNetUInt32Variables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_PredNetUInt64Variables", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_PredNetFloatVariables", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_PredNetVectorVariables", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PredNetQuaternionVariables", "type": "NetworkVarNames", "type_name": "Quaternion"}, {"name": "m_PredNetGlobalSymbolVariables", "type": "NetworkVarNames", "type_name": "CGlobalSymbol"}, {"name": "m_OwnerOnlyPredNetBoolVariables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OwnerOnlyPredNetByteVariables", "type": "NetworkVarNames", "type_name": "byte"}, {"name": "m_OwnerOnlyPredNetUInt16Variables", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_OwnerOnlyPredNetIntVariables", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_OwnerOnlyPredNetUInt32Variables", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OwnerOnlyPredNetUInt64Variables", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_OwnerOnlyPredNetFloatVariables", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_OwnerOnlyPredNetVectorVariables", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_OwnerOnlyPredNetQuaternionVariables", "type": "NetworkVarNames", "type_name": "Quaternion"}, {"name": "m_OwnerOnlyPredNetGlobalSymbolVariables", "type": "NetworkVarNames", "type_name": "CGlobalSymbol"}, {"name": "m_nBoolVariablesCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOwnerOnlyBoolVariablesCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRandomSeedOffset", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flLastTeleportTime", "type": "NetworkVarNames", "type_name": "float"}], "parent": null}, "CAttributeList": {"fields": {"m_Attributes": 8, "m_pManager": 88}, "metadata": [{"name": "m_Attributes", "type": "NetworkVarNames", "type_name": "CEconItemAttribute"}], "parent": null}, "CAttributeManager": {"fields": {"m_CachedResults": 48, "m_ProviderType": 44, "m_Providers": 8, "m_bPreventLoopback": 40, "m_hOuter": 36, "m_iReapplyProvisionParity": 32}, "metadata": [{"name": "m_iReapplyProvisionParity", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOuter", "type": "NetworkVarNames", "type_name": "EHANDLE"}, {"name": "m_ProviderType", "type": "NetworkVarNames", "type_name": "attributeprovidertypes_t"}], "parent": null}, "CAttributeManager__cached_attribute_float_t": {"fields": {"flIn": 0, "flOut": 16, "iAttribHook": 8}, "metadata": [], "parent": null}, "CBaseAnimGraph": {"fields": {"m_RagdollPose": 3576, "m_bAnimGraphUpdateEnabled": 3504, "m_bAnimationUpdateScheduled": 3524, "m_bBuiltRagdoll": 3552, "m_bHasAnimatedMaterialAttributes": 3664, "m_bInitiallyPopulateInterpHistory": 3488, "m_bRagdollClientSide": 3648, "m_bSuppressAnimEventSounds": 3490, "m_flMaxSlopeDistance": 3508, "m_nForceBone": 3540, "m_pClientsideRagdoll": 3544, "m_vLastSlopeCheckPos": 3512, "m_vecForce": 3528}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bInitiallyPopulateInterpHistory", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAnimGraphUpdateEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecForce", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nForceBone", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "PhysicsRagdollPose_t"}, {"name": "m_bRagdollClientSide", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "CBaseAnimGraphController": {"fields": {"m_animGraphNetworkedVars": 24, "m_bLastUpdateSkipped": 5332, "m_bNetworkedAnimationInputsChanged": 5330, "m_bNetworkedSequenceChanged": 5331, "m_bSequenceFinished": 5288, "m_flPlaybackRate": 5316, "m_flPrevAnimUpdateTime": 5336, "m_flSeqFixedCycle": 5308, "m_flSeqStartTime": 5304, "m_flSoundSyncTime": 5292, "m_hSequence": 5300, "m_nActiveIKChainMask": 5296, "m_nAnimLoopMode": 5312, "m_nNotifyState": 5328}, "metadata": [{"name": "m_animGraphNetworkedVars", "type": "NetworkVarNames", "type_name": "CAnimGraphNetworkedVariables"}, {"name": "m_hSequence", "type": "NetworkVarNames", "type_name": "HSequence"}, {"name": "m_flSeqStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flSeqFixedCycle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nAnimLoopMode", "type": "NetworkVarNames", "type_name": "AnimLoopMode_t"}], "parent": "CSkeletonAnimationController"}, "CBasePlayerController": {"fields": {"m_CommandContext": 1400, "m_bIsHLTV": 1624, "m_bIsLocalPlayerController": 1776, "m_bKnownTeamMismatch": 1584, "m_hPawn": 1580, "m_hPredictedPawn": 1588, "m_hSplitOwner": 1596, "m_hSplitScreenPlayers": 1600, "m_iConnected": 1628, "m_iDesiredFOV": 1780, "m_iszPlayerName": 1632, "m_nFinalPredictedTick": 1392, "m_nInButtonsWhichAreToggles": 1568, "m_nSplitScreenSlot": 1592, "m_nTickBase": 1576, "m_steamID": 1768}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "m_nTickBase", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_h<PERSON>awn", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerPawn>"}, {"name": "m_bKnownTeamMismatch", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iConnected", "type": "NetworkVarNames", "type_name": "PlayerConnectedState"}, {"name": "m_iszPlayerName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_steamID", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_iDesiredFOV", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "MNetworkReplayCompatField", "type": "Unknown"}], "parent": "C_BaseEntity"}, "CBasePlayerControllerAPI": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CBasePlayerVData": {"fields": {"m_flArmDamageMultiplier": 312, "m_flChestDamageMultiplier": 280, "m_flCrouchTime": 372, "m_flDrowningDamageInterval": 348, "m_flHeadDamageMultiplier": 264, "m_flHoldBreathTime": 344, "m_flLegDamageMultiplier": 328, "m_flStomachDamageMultiplier": 296, "m_flUseAngleTolerance": 368, "m_flUseRange": 364, "m_nDrowningDamageInitial": 352, "m_nDrowningDamageMax": 356, "m_nWaterSpeed": 360, "m_sModelName": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CEntitySubclassVDataBase"}, "CBasePlayerWeaponVData": {"fields": {"m_aShootSounds": 800, "m_bAllowFlipping": 489, "m_bAutoSwitchFrom": 781, "m_bAutoSwitchTo": 780, "m_bBuiltRightHanded": 488, "m_bLinkedCooldowns": 752, "m_bReserveAmmoAsClips": 772, "m_iDefaultClip1": 764, "m_iDefaultClip2": 768, "m_iFlags": 753, "m_iMaxClip1": 756, "m_iMaxClip2": 760, "m_iPosition": 792, "m_iRumbleEffect": 784, "m_iSlot": 788, "m_iWeight": 776, "m_nPrimaryAmmoType": 754, "m_nSecondaryAmmoType": 755, "m_sMuzzleAttachment": 496, "m_sToolsOnlyOwnerModelName": 264, "m_szMuzzleFlashParticle": 528, "m_szWorldModel": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CEntitySubclassVDataBase"}, "CBaseProp": {"fields": {"m_bConformToCollisionBounds": 3984, "m_bModelOverrodeBlockLOS": 3976, "m_iShapeType": 3980, "m_mPreferredCatchTransform": 3988}, "metadata": [], "parent": "CBaseAnimGraph"}, "CBodyComponent": {"fields": {"__m_pChainEntity": 32, "m_pSceneNode": 8}, "metadata": [], "parent": "CEntityComponent"}, "CBodyComponentBaseAnimGraph": {"fields": {"m_animationController": 1168}, "metadata": [{"name": "m_animationController", "type": "NetworkVarNames", "type_name": "CBaseAnimGraphController"}], "parent": "CBodyComponentSkeletonInstance"}, "CBodyComponentBaseModelEntity": {"fields": {}, "metadata": [], "parent": "CBodyComponentSkeletonInstance"}, "CBodyComponentPoint": {"fields": {"m_sceneNode": 80}, "metadata": [{"name": "m_sceneNode", "type": "NetworkVarNames", "type_name": "CGameSceneNode"}], "parent": "CBodyComponent"}, "CBodyComponentSkeletonInstance": {"fields": {"m_skeletonInstance": 80}, "metadata": [{"name": "m_skeletonInstance", "type": "NetworkVarNames", "type_name": "CSkeletonInstance"}], "parent": "CBodyComponent"}, "CBombTarget": {"fields": {"m_bBombPlantedHere": 3376}, "metadata": [{"name": "m_bBombPlantedHere", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseTrigger"}, "CBuoyancyHelper": {"fields": {"m_flFluidDensity": 28, "m_nFluidType": 24, "m_vecFractionOfWheelSubmergedForWheelDrag": 80, "m_vecFractionOfWheelSubmergedForWheelFriction": 32, "m_vecWheelDrag": 104, "m_vecWheelFrictionScales": 56}, "metadata": [], "parent": null}, "CCSClientPointScriptEntity": {"fields": {}, "metadata": [], "parent": "CCSPointScriptEntity"}, "CCSGO_WingmanIntroCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "CCSGO_WingmanIntroCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "CCSGO_WingmanIntroCharacterPosition"}, "CCSGO_WingmanIntroTerroristPosition": {"fields": {}, "metadata": [], "parent": "CCSGO_WingmanIntroCharacterPosition"}, "CCSGameModeRules": {"fields": {"__m_pChainEntity": 8}, "metadata": [], "parent": null}, "CCSGameModeRules_ArmsRace": {"fields": {"m_WeaponSequence": 48}, "metadata": [{"name": "m_WeaponSequence", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": "CCSGameModeRules"}, "CCSGameModeRules_Deathmatch": {"fields": {"m_flDMBonusStartTime": 48, "m_flDMBonusTimeLength": 52, "m_sDMBonusWeapon": 56}, "metadata": [{"name": "m_flDMBonusStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDMBonusTimeLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_sDMBonusWeapon", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": "CCSGameModeRules"}, "CCSGameModeRules_Noop": {"fields": {}, "metadata": [], "parent": "CCSGameModeRules"}, "CCSObserver_CameraServices": {"fields": {}, "metadata": [], "parent": "CCSPlayerBase_CameraServices"}, "CCSObserver_MovementServices": {"fields": {}, "metadata": [], "parent": "CPlayer_MovementServices"}, "CCSObserver_ObserverServices": {"fields": {"m_bObserverInterpolationNeedsDeferredSetup": 164, "m_flObsInterp_PathLength": 116, "m_hLastObserverTarget": 88, "m_obsInterpState": 160, "m_qObsInterp_OrientationStart": 128, "m_qObsInterp_OrientationTravelDir": 144, "m_vecObserverInterpStartPos": 104, "m_vecObserverInterpolateOffset": 92}, "metadata": [], "parent": "CPlayer_ObserverServices"}, "CCSObserver_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayer_UseServices"}, "CCSObserver_ViewModelServices": {"fields": {}, "metadata": [], "parent": "CPlayer_ViewModelServices"}, "CCSPlayerBase_CameraServices": {"fields": {"m_flFOVRate": 540, "m_flFOVTime": 536, "m_flLastShotFOV": 548, "m_hZoomOwner": 544, "m_iFOV": 528, "m_iFOVStart": 532}, "metadata": [{"name": "m_iFOV", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iFOVStart", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flFOVTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flFOVRate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_h<PERSON><PERSON>Owner", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayer_CameraServices"}, "CCSPlayerController": {"fields": {"m_bAbandonAllowsSurrender": 2050, "m_bAbandonOffersInstantSurrender": 2051, "m_bCanControlObservedBot": 2080, "m_bCannotBeKicked": 2048, "m_bControllingBot": 2072, "m_bDisconnection1MinWarningPrinted": 2052, "m_bEverFullyConnected": 2049, "m_bEverPlayedOnTeam": 1892, "m_bFireBulletsSeedSynchronized": 2157, "m_bHasBeenControlledByPlayerThisRound": 2074, "m_bHasCommunicationAbuseMute": 1860, "m_bHasControlledBotThisRound": 2073, "m_bIsPlayerNameDirty": 2156, "m_bMvpNoMusic": 2138, "m_bPawnHasDefuser": 2104, "m_bPawnHasHelmet": 2105, "m_bPawnIsAlive": 2092, "m_bScoreReported": 2053, "m_eMvpReason": 2140, "m_eNetworkDisconnectionReason": 2044, "m_flForceTeamTime": 1884, "m_flPreviousForceJoinTeamTime": 1896, "m_hObserverPawn": 2088, "m_hOriginalControllerOfCurrentPawn": 2120, "m_hPlayerPawn": 2084, "m_iCoachingTeam": 1920, "m_iCompTeammateColor": 1888, "m_iCompetitiveRankType": 1952, "m_iCompetitiveRanking": 1944, "m_iCompetitiveRankingPredicted_Loss": 1960, "m_iCompetitiveRankingPredicted_Tie": 1964, "m_iCompetitiveRankingPredicted_Win": 1956, "m_iCompetitiveWins": 1948, "m_iDraftIndex": 2032, "m_iMVPs": 2152, "m_iMusicKitID": 2144, "m_iMusicKitMVPs": 2148, "m_iPawnArmor": 2100, "m_iPawnBotDifficulty": 2116, "m_iPawnHealth": 2096, "m_iPawnLifetimeEnd": 2112, "m_iPawnLifetimeStart": 2108, "m_iPendingTeamNum": 1880, "m_iPing": 1856, "m_iScore": 2124, "m_msQueuedModeDisconnectionTimestamp": 2036, "m_nBotsControlledThisRound": 2076, "m_nDisconnectionTick": 2056, "m_nEndMatchNextMapVote": 1968, "m_nFirstKill": 2136, "m_nKillCount": 2137, "m_nPawnCharacterDefIndex": 2106, "m_nPlayerDominated": 1928, "m_nPlayerDominatingMe": 1936, "m_nQuestProgressReason": 1980, "m_pActionTrackingServices": 1840, "m_pDamageServices": 1848, "m_pInGameMoneyServices": 1824, "m_pInventoryServices": 1832, "m_recentKillQueue": 2128, "m_rtActiveMissionPeriod": 1976, "m_sSanitizedPlayerName": 1912, "m_szClan": 1904, "m_szCrosshairCodes": 1872, "m_uiAbandonRecordedReason": 2040, "m_uiCommunicationMuteFlags": 1864, "m_unActiveQuestId": 1972, "m_unPlayerTvControlFlags": 1984}, "metadata": [{"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "m_pInGameMoneyServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_InGameMoneyServices*"}, {"name": "m_pInventoryServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_InventoryServices*"}, {"name": "m_pActionTrackingServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_ActionTrackingServices*"}, {"name": "m_pDamageServices", "type": "NetworkVarNames", "type_name": "CCSPlayerController_DamageServices*"}, {"name": "m_iPing", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bHasCommunicationAbuseMute", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_uiCommunicationMuteFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_szCrosshairCodes", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iPendingTeamNum", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_flForceTeamTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iCompTeammateColor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bEverPlayedOnTeam", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_sz<PERSON>lan", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iCoachingTeam", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPlayerDominated", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nPlayerDominatingMe", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_iCompetitiveRanking", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveWins", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankType", "type": "NetworkVarNames", "type_name": "int8"}, {"name": "m_iCompetitiveRankingPredicted_Win", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankingPredicted_Loss", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCompetitiveRankingPredicted_Tie", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchNextMapVote", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unActiveQuestId", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_rtActiveMissionPeriod", "type": "NetworkVarNames", "type_name": "RTime32"}, {"name": "m_nQuestProgressReason", "type": "NetworkVarNames", "type_name": "QuestProgress::Reason"}, {"name": "m_unPlayerTvControlFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nDisconnectionTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bControllingBot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasControlledBotThisRound", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCanControlObservedBot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hPlayerPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hObserverPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSObserverPawn>"}, {"name": "m_bPawnIsAlive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iPawnHealth", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iPawnArmor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bPawnHasDefuser", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPawnHasHelmet", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nPawnCharacterDefIndex", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_iPawnLifetimeStart", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iPawnLifetimeEnd", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iPawnBotDifficulty", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOriginalControllerOfCurrentPawn", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_iScore", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_recentKillQueue", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nFirstKill", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nKillCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_bMvpNoMusic", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_eMvpReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMusicKitID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMusicKitMVPs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMVPs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bFireBulletsSeedSynchronized", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CBasePlayerController"}, "CCSPlayerController_ActionTrackingServices": {"fields": {"m_iNumRoundKills": 272, "m_iNumRoundKillsHeadshots": 276, "m_matchStats": 144, "m_perRoundStats": 64, "m_unTotalRoundDamageDealt": 280}, "metadata": [{"name": "m_perRoundStats", "type": "NetworkVarNames", "type_name": "CSPerRoundStats_t"}, {"name": "m_matchStats", "type": "NetworkVarNames", "type_name": "CSMatchStats_t"}, {"name": "m_iNumRoundKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumRoundKillsHeadshots", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unTotalRoundDamageDealt", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_DamageServices": {"fields": {"m_DamageList": 72, "m_nSendUpdate": 64}, "metadata": [{"name": "m_nSendUpdate", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_DamageList", "type": "NetworkVarNames", "type_name": "CDamageRecord"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_InGameMoneyServices": {"fields": {"m_iAccount": 64, "m_iCashSpentThisRound": 76, "m_iStartAccount": 68, "m_iTotalCashSpent": 72}, "metadata": [{"name": "m_iAccount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iStartAccount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iTotalCashSpent", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCashSpentThisRound", "type": "NetworkVarNames", "type_name": "int"}], "parent": "CPlayerControllerComponent"}, "CCSPlayerController_InventoryServices": {"fields": {"m_nPersonaDataPublicCommendsFriendly": 104, "m_nPersonaDataPublicCommendsLeader": 96, "m_nPersonaDataPublicCommendsTeacher": 100, "m_nPersonaDataPublicLevel": 92, "m_nPersonaDataXpTrailLevel": 108, "m_rank": 68, "m_unMusicID": 64, "m_vecServerAuthoritativeWeaponSlots": 112}, "metadata": [{"name": "m_unMusicID", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_rank", "type": "NetworkVarNames", "type_name": "MedalRank_t"}, {"name": "m_nPersonaDataPublicLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsLeader", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsTeacher", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataPublicCommendsFriendly", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPersonaDataXpTrailLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecServerAuthoritativeWeaponSlots", "type": "NetworkVarNames", "type_name": "ServerAuthoritativeWeaponSlot_t"}], "parent": "CPlayerControllerComponent"}, "CCSPlayer_ActionTrackingServices": {"fields": {"m_bIsRescuing": 68, "m_hLastWeaponBeforeC4AutoSwitch": 64, "m_weaponPurchasesThisMatch": 72, "m_weaponPurchasesThisRound": 160}, "metadata": [{"name": "m_bIsRescuing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_weaponPurchasesThisMatch", "type": "NetworkVarNames", "type_name": "WeaponPurchaseTracker_t"}, {"name": "m_weaponPurchasesThisRound", "type": "NetworkVarNames", "type_name": "WeaponPurchaseTracker_t"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_BulletServices": {"fields": {"m_totalHitsOnServer": 64}, "metadata": [{"name": "m_totalHitsOnServer", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_BuyServices": {"fields": {"m_vecSellbackPurchaseEntries": 64}, "metadata": [{"name": "m_vecSellbackPurchaseEntries", "type": "NetworkVarNames", "type_name": "SellbackPurchaseEntry_t"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_CameraServices": {"fields": {"m_flDeathCamTilt": 552, "m_vClientScopeInaccuracy": 560}, "metadata": [], "parent": "CCSPlayerBase_CameraServices"}, "CCSPlayer_DamageReactServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CCSPlayer_GlowServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CCSPlayer_HostageServices": {"fields": {"m_hCarriedHostage": 64, "m_hCarriedHostageProp": 68}, "metadata": [{"name": "m_hCarriedHostage", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hCarriedHostageProp", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_ItemServices": {"fields": {"m_bHasDefuser": 64, "m_bHasHeavyArmor": 66, "m_bHasHelmet": 65}, "metadata": [{"name": "m_bHasDefuser", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHas<PERSON>elmet", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasHeavyArmor", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_ItemServices"}, "CCSPlayer_MovementServices": {"fields": {"m_StuckLast": 1132, "m_bDesiresDuck": 561, "m_bDuckOverride": 560, "m_bHasWalkMovedSinceLastJump": 601, "m_bInStuckTest": 602, "m_bOldJumpPressed": 1192, "m_bSpeedCropped": 1136, "m_duckUntilOnGround": 600, "m_fStashGrenadeParameterWhen": 1200, "m_flAccumulatedJumpError": 1244, "m_flDuckAmount": 552, "m_flDuckOffset": 564, "m_flDuckSpeed": 556, "m_flGroundMoveEfficiency": 1140, "m_flHeightAtJumpStart": 1228, "m_flJumpPressedTime": 1196, "m_flLastDuckTime": 580, "m_flMaxJumpHeightLastJump": 1236, "m_flMaxJumpHeightThisJump": 1232, "m_flOffsetTickCompleteTime": 1216, "m_flOffsetTickStashedSpeed": 1220, "m_flStamina": 1224, "m_flStaminaAtJumpStart": 1240, "m_flStuckCheckTime": 616, "m_flWaterEntryTime": 1148, "m_nButtonDownMaskPrev": 1208, "m_nDuckJumpTimeMsecs": 572, "m_nDuckTimeMsecs": 568, "m_nGameCodeHasMovedPlayerAfterCommand": 1188, "m_nJumpTimeMsecs": 576, "m_nLadderSurfacePropIndex": 548, "m_nOldWaterLevel": 1144, "m_nTraceCount": 1128, "m_vecForward": 1152, "m_vecLadderNormal": 536, "m_vecLastPositionAtFullCrouchSpeed": 592, "m_vecLeft": 1164, "m_vecUp": 1176}, "metadata": [{"name": "m_vecLadderNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nLadderSurfacePropIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flDuckAmount", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDuckSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bDuckOverride", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDesiresDuck", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nDuckTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nDuckJumpTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nJumpTimeMsecs", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flLastDuckTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nGameCodeHasMovedPlayerAfterCommand", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bOldJumpPressed", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fStashGrenadeParameterWhen", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nButtonDownMaskPrev", "type": "NetworkVarNames", "type_name": "ButtonBitMask_t"}, {"name": "m_flOffsetTickCompleteTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOffsetTickStashedSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStamina", "type": "NetworkVarNames", "type_name": "float"}], "parent": "CPlayer_MovementServices_Humanoid"}, "CCSPlayer_PingServices": {"fields": {"m_hPlayerPing": 64}, "metadata": [{"name": "m_hPlayerPing", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CCSPlayer_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayer_UseServices"}, "CCSPlayer_ViewModelServices": {"fields": {"m_hViewModel": 64}, "metadata": [{"name": "m_hViewModel", "type": "NetworkVarNames", "type_name": "CHandle<CBaseViewModel>"}], "parent": "CPlayer_ViewModelServices"}, "CCSPlayer_WaterServices": {"fields": {"m_flSwimSoundTime": 80, "m_flWaterJumpTime": 64, "m_vecWaterJumpVel": 68}, "metadata": [], "parent": "CPlayer_WaterServices"}, "CCSPlayer_WeaponServices": {"fields": {"m_bIsHoldingLookAtWeapon": 189, "m_bIsLookingAtWeapon": 188, "m_flNextAttack": 184, "m_nOldInputHistoryCount": 1112, "m_nOldShootPositionHistoryCount": 192}, "metadata": [{"name": "m_flNextAttack", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsLookingAtWeapon", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHoldingLookAtWeapon", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_WeaponServices"}, "CCSPointScript": {"fields": {"m_pParent": 248}, "metadata": [{"name": "MPulseInstanceDomainInfo", "type": "Unknown"}, {"name": "MPulseDomainHookInfo", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}, {"name": "MPulseDomainOptInFeatureTag", "type": "Unknown"}], "parent": "CBasePulseGraphInstance"}, "CCSPointScriptEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CCSPointScriptExtensions_CCSWeaponBaseVData": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_entity": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_observer": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_player": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_player_controller": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSPointScriptExtensions_weapon_cs_base": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CCSWeaponBaseVData": {"fields": {"m_DefaultLoadoutSlot": 3320, "m_GearSlot": 3312, "m_GearSlotPosition": 3316, "m_WeaponCategory": 844, "m_WeaponType": 840, "m_angPivotAngle": 3624, "m_bCannotShootUnderwater": 3355, "m_bHasBurstMode": 3353, "m_bHideViewModelWhenZoomed": 3577, "m_bIsFullAuto": 3388, "m_bIsRevolver": 3354, "m_bMeleeWeapon": 3352, "m_bUnzoomsAfterShot": 3576, "m_eSilencerType": 3376, "m_flArmorRatio": 3656, "m_flAttackMovespeedFactor": 3544, "m_flBotAudibleRange": 3560, "m_flCycleTime": 3396, "m_flFlinchVelocityModifierLarge": 3672, "m_flFlinchVelocityModifierSmall": 3676, "m_flHeadshotMultiplier": 3652, "m_flHeatPerShot": 3548, "m_flIdleInterval": 3540, "m_flInaccuracyAltSoundThreshold": 3556, "m_flInaccuracyCrouch": 3420, "m_flInaccuracyFire": 3460, "m_flInaccuracyJump": 3436, "m_flInaccuracyJumpApex": 3520, "m_flInaccuracyJumpInitial": 3516, "m_flInaccuracyLadder": 3452, "m_flInaccuracyLand": 3444, "m_flInaccuracyMove": 3468, "m_flInaccuracyPitchShift": 3552, "m_flInaccuracyReload": 3524, "m_flInaccuracyStand": 3428, "m_flIronSightFOV": 3612, "m_flIronSightLooseness": 3620, "m_flIronSightPivotForward": 3616, "m_flIronSightPullUpSpeed": 3604, "m_flIronSightPutDownSpeed": 3608, "m_flMaxSpeed": 3404, "m_flPenetration": 3660, "m_flRange": 3664, "m_flRangeModifier": 3668, "m_flRecoilAngle": 3476, "m_flRecoilAngleVariance": 3484, "m_flRecoilMagnitude": 3492, "m_flRecoilMagnitudeVariance": 3500, "m_flRecoveryTimeCrouch": 3680, "m_flRecoveryTimeCrouchFinal": 3688, "m_flRecoveryTimeStand": 3684, "m_flRecoveryTimeStandFinal": 3692, "m_flSpread": 3412, "m_flThrowVelocity": 3704, "m_flTimeToIdleAfterFire": 3536, "m_flZoomTime0": 3592, "m_flZoomTime1": 3596, "m_flZoomTime2": 3600, "m_nCrosshairDeltaDistance": 3384, "m_nCrosshairMinDistance": 3380, "m_nDamage": 3648, "m_nKillAward": 3340, "m_nNumBullets": 3392, "m_nPrice": 3336, "m_nPrimaryReserveAmmoMax": 3344, "m_nRecoilSeed": 3528, "m_nRecoveryTransitionEndBullet": 3700, "m_nRecoveryTransitionStartBullet": 3696, "m_nSecondaryReserveAmmoMax": 3348, "m_nSpreadSeed": 3532, "m_nTracerFrequency": 3508, "m_nZoomFOV1": 3584, "m_nZoomFOV2": 3588, "m_nZoomLevels": 3580, "m_sWrongTeamMsg": 3328, "m_szAimsightLensMaskModel": 1520, "m_szAnimClass": 3720, "m_szAnimExtension": 3368, "m_szEjectBrassEffect": 2192, "m_szHeatEffect": 1968, "m_szMagazineModel": 1744, "m_szMuzzleFlashParticleAlt": 2416, "m_szMuzzleFlashThirdPersonParticle": 2640, "m_szMuzzleFlashThirdPersonParticleAlt": 2864, "m_szName": 3360, "m_szPlayerModel": 1072, "m_szTracerParticle": 3088, "m_szUseRadioSubtitle": 3568, "m_szViewModel": 848, "m_szWorldDroppedModel": 1296, "m_vSmokeColor": 3708, "m_vecIronSightEyePos": 3636}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}, {"name": "MPropertySuppressBaseClassField", "type": "Unknown"}, {"name": "MPropertySuppressBaseClassField", "type": "Unknown"}], "parent": "CBasePlayerWeaponVData"}, "CCS_PortraitWorldCallbackHandler": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CCitadelSoundOpvarSetOBB": {"fields": {"m_iszOperatorName": 1416, "m_iszOpvarName": 1424, "m_iszStackName": 1408, "m_nAABBDirection": 1480, "m_vDistanceInnerMaxs": 1444, "m_vDistanceInnerMins": 1432, "m_vDistanceOuterMaxs": 1468, "m_vDistanceOuterMins": 1456}, "metadata": [{"name": "m_iszStackName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOperatorName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOpvarName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_vDistanceInnerMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceInnerMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceOuterMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDistanceOuterMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nAABBDirection", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseEntity"}, "CCollisionProperty": {"fields": {"m_CollisionGroup": 94, "m_collisionAttribute": 16, "m_flBoundingRadius": 96, "m_flCapsuleRadius": 172, "m_nEnablePhysics": 95, "m_nSolidType": 91, "m_nSurroundType": 93, "m_triggerBloat": 92, "m_usSolidFlags": 90, "m_vCapsuleCenter1": 148, "m_vCapsuleCenter2": 160, "m_vecMaxs": 76, "m_vecMins": 64, "m_vecSpecifiedSurroundingMaxs": 112, "m_vecSpecifiedSurroundingMins": 100, "m_vecSurroundingMaxs": 124, "m_vecSurroundingMins": 136}, "metadata": [{"name": "m_collisionAttribute", "type": "NetworkVarNames", "type_name": "VPhysicsCollisionAttribute_t"}, {"name": "m_vecMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_usSolidFlags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSolidType", "type": "NetworkVarNames", "type_name": "SolidType_t"}, {"name": "m_triggerBloat", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSurroundType", "type": "NetworkVarNames", "type_name": "SurroundingBoundsType_t"}, {"name": "m_CollisionGroup", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nEnablePhysics", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_vecSpecifiedSurroundingMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecSpecifiedSurroundingMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vCapsuleCenter1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vCapsuleCenter2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flCapsuleRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": null}, "CDamageRecord": {"fields": {"m_DamagerXuid": 72, "m_PlayerDamager": 40, "m_PlayerRecipient": 44, "m_RecipientXuid": 80, "m_bIsOtherEnemy": 108, "m_hPlayerControllerDamager": 48, "m_hPlayerControllerRecipient": 52, "m_iActualHealthRemoved": 96, "m_iBulletsDamage": 88, "m_iDamage": 92, "m_iLastBulletUpdate": 104, "m_iNumHits": 100, "m_killType": 109, "m_szPlayerDamagerName": 56, "m_szPlayerRecipientName": 64}, "metadata": [{"name": "m_PlayerDamager", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_PlayerRecipient", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hPlayerControllerDamager", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_hPlayerControllerRecipient", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}, {"name": "m_szPlayerDamagerName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_szPlayerRecipientName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_DamagerXuid", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_RecipientXuid", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_iDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iActualHealthRemoved", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumHits", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iLastBulletUpdate", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsOtherEnemy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_killType", "type": "NetworkVarNames", "type_name": "EKillTypes_t"}], "parent": null}, "CEconItemAttribute": {"fields": {"m_bSetBonus": 64, "m_flInitialValue": 56, "m_flValue": 52, "m_iAttributeDefinitionIndex": 48, "m_nRefundableCurrency": 60}, "metadata": [{"name": "m_iAttributeDefinitionIndex", "type": "NetworkVarNames", "type_name": "attrib_definition_index_t"}, {"name": "m_flValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInitialValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRefundableCurrency", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bSetBonus", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "CEffectData": {"fields": {"m_fFlags": 99, "m_flMagnitude": 68, "m_flRadius": 72, "m_flScale": 64, "m_hEntity": 56, "m_hOtherEntity": 60, "m_iEffectName": 108, "m_nAttachmentIndex": 100, "m_nAttachmentName": 104, "m_nColor": 98, "m_nDamageType": 88, "m_nEffectIndex": 80, "m_nExplosionType": 110, "m_nHitBox": 96, "m_nMaterial": 94, "m_nPenetrate": 92, "m_nSurfaceProp": 76, "m_vAngles": 44, "m_vNormal": 32, "m_vOrigin": 8, "m_vStart": 20}, "metadata": [{"name": "m_v<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vStart", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_hEntity", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_hOtherEntity", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_flScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMagnitude", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nSurfaceProp", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_nEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinition"}, {"name": "m_nDamageType", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nPenetrate", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nMaterial", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nHitBox", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nColor", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_fFlags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nAttachmentIndex", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_nAttachmentName", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_iEffectName", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nExplosionType", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": null}, "CEntityComponent": {"fields": {}, "metadata": [], "parent": null}, "CEntityIdentity": {"fields": {"m_PathIndex": 64, "m_designerName": 32, "m_fDataObjectTypes": 60, "m_flags": 48, "m_name": 24, "m_nameStringableIndex": 20, "m_pNext": 96, "m_pNextByClass": 112, "m_pPrev": 88, "m_pPrevByClass": 104, "m_worldGroupId": 56}, "metadata": [{"name": "m_nameStringableIndex", "type": "NetworkVarNames", "type_name": "int32"}], "parent": null}, "CEntityInstance": {"fields": {"m_CScriptComponent": 40, "m_bVisibleinPVS": 48, "m_iszPrivateVScripts": 8, "m_pEntity": 16}, "metadata": [{"name": "m_pEntity", "type": "NetworkVarNames", "type_name": "CEntityIdentity*"}, {"name": "m_CScriptComponent", "type": "NetworkVarNames", "type_name": "CScriptComponent::Storage_t"}], "parent": null}, "CEnvSoundscape": {"fields": {"m_OnPlay": 1384, "m_bDisabled": 1524, "m_bOverrideWithEvent": 1440, "m_flRadius": 1424, "m_hProxySoundscape": 1520, "m_positionNames": 1456, "m_soundEventHash": 1536, "m_soundEventName": 1432, "m_soundscapeEntityListId": 1448, "m_soundscapeIndex": 1444, "m_soundscapeName": 1528}, "metadata": [], "parent": "C_BaseEntity"}, "CEnvSoundscapeAlias_snd_soundscape": {"fields": {}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeProxy": {"fields": {"m_MainSoundscapeName": 1544}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeProxyAlias_snd_soundscape_proxy": {"fields": {}, "metadata": [], "parent": "CEnvSoundscapeProxy"}, "CEnvSoundscapeTriggerable": {"fields": {}, "metadata": [], "parent": "CEnvSoundscape"}, "CEnvSoundscapeTriggerableAlias_snd_soundscape_triggerable": {"fields": {}, "metadata": [], "parent": "CEnvSoundscapeTriggerable"}, "CFuncWater": {"fields": {"m_BuoyancyHelper": 3368}, "metadata": [], "parent": "C_BaseModelEntity"}, "CGameSceneNode": {"fields": {"m_angAbsRotation": 220, "m_angRotation": 192, "m_bBoneMergeFlex": 0, "m_bDebugAbsOriginChanges": 238, "m_bDirtyBoneMergeBoneToRoot": 0, "m_bDirtyBoneMergeInfo": 0, "m_bDirtyHierarchy": 0, "m_bDormant": 239, "m_bForceParentToBeNetworked": 240, "m_bNetworkedAnglesChanged": 0, "m_bNetworkedPositionChanged": 0, "m_bNetworkedScaleChanged": 0, "m_bWillBeCallingPostDataUpdate": 0, "m_flAbsScale": 232, "m_flClientLocalScale": 320, "m_flScale": 204, "m_flZOffset": 316, "m_hParent": 120, "m_hierarchyAttachName": 312, "m_nDoNotSetAnimTimeInInvalidatePhysicsCount": 245, "m_nHierarchicalDepth": 243, "m_nHierarchyType": 244, "m_nLatchAbsOrigin": 0, "m_nParentAttachmentOrBone": 236, "m_name": 248, "m_nodeToWorld": 16, "m_pChild": 64, "m_pNextSibling": 72, "m_pOwner": 48, "m_pParent": 56, "m_vRenderOrigin": 324, "m_vecAbsOrigin": 208, "m_vecOrigin": 136}, "metadata": [{"name": "m_hParent", "type": "NetworkVarNames", "type_name": "CGameSceneNodeHandle"}, {"name": "m_vec<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "CNetworkOriginCellCoordQuantizedVector"}, {"name": "m_angRotation", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_flScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_name", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_hierarchyAttachName", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": null}, "CGameSceneNodeHandle": {"fields": {"m_hOwner": 8, "m_name": 12}, "metadata": [{"name": "m_hOwner", "type": "NetworkVarNames", "type_name": "CEntityHandle"}, {"name": "m_name", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": null}, "CGlowProperty": {"fields": {"m_bEligibleForScreenHighlight": 80, "m_bFlashing": 68, "m_bGlowing": 81, "m_fGlowColor": 8, "m_flGlowStartTime": 76, "m_flGlowTime": 72, "m_glowColorOverride": 64, "m_iGlowTeam": 52, "m_iGlowType": 48, "m_nGlowRange": 56, "m_nGlowRangeMin": 60}, "metadata": [{"name": "m_iGlowType", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iGlowTeam", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nGlowRange", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nGlowRangeMin", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_glowColorOverride", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bFlashing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGlowTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flGlowStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEligibleForScreenHighlight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "CGrenadeTracer": {"fields": {"m_flTracerDuration": 3392, "m_nType": 3396}, "metadata": [], "parent": "C_BaseModelEntity"}, "CHitboxComponent": {"fields": {"m_bvDisabledHitGroups": 36}, "metadata": [{"name": "m_bvDisabledHitGroups", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "CEntityComponent"}, "CHostageRescueZone": {"fields": {}, "metadata": [], "parent": "CHostageRescueZoneShim"}, "CHostageRescueZoneShim": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "CInfoDynamicShadowHint": {"fields": {"m_bDisabled": 1384, "m_flRange": 1388, "m_hLight": 1400, "m_nImportance": 1392, "m_nLightChoice": 1396}, "metadata": [], "parent": "C_PointEntity"}, "CInfoDynamicShadowHintBox": {"fields": {"m_vBoxMaxs": 1420, "m_vBoxMins": 1408}, "metadata": [], "parent": "CInfoDynamicShadowHint"}, "CInfoOffscreenPanoramaTexture": {"fields": {"m_RenderAttrName": 1408, "m_TargetEntities": 1416, "m_bCheckCSSClasses": 1824, "m_bDisabled": 1384, "m_nResolutionX": 1388, "m_nResolutionY": 1392, "m_nTargetChangeCount": 1440, "m_szLayoutFileName": 1400, "m_vecCSSClasses": 1448}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nResolutionX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nResolutionY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_szLayoutFileName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_RenderAttrName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_TargetEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_nTargetChangeCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_PointEntity"}, "CInfoParticleTarget": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "CInfoTarget": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "CInfoWorldLayer": {"fields": {"m_bCreateAsChildSpawnGroup": 1442, "m_bEntitiesSpawned": 1441, "m_bWorldLayerActuallyVisible": 1448, "m_bWorldLayerVisible": 1440, "m_hLayerSpawnGroup": 1444, "m_layerName": 1432, "m_pOutputOnEntitiesSpawned": 1384, "m_worldName": 1424}, "metadata": [{"name": "m_worldName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_layerName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bWorldLayerVisible", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bEntitiesSpawned", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "CLightComponent": {"fields": {"__m_pChainEntity": 56, "m_Color": 117, "m_Pattern": 216, "m_SecondaryColor": 121, "m_SkyAmbientBounce": 396, "m_SkyColor": 388, "m_bEnabled": 308, "m_bFlicker": 309, "m_bMixedShadows": 401, "m_bPrecomputedFieldsValid": 310, "m_bRenderDiffuse": 192, "m_bRenderToCubemaps": 280, "m_bRenderTransmissive": 200, "m_bUseSecondaryColor": 400, "m_bUsesBakedShadowing": 268, "m_flAttenuation0": 148, "m_flAttenuation1": 152, "m_flAttenuation2": 156, "m_flBrightness": 128, "m_flBrightnessMult": 136, "m_flBrightnessScale": 132, "m_flCapsuleLength": 408, "m_flFadeMaxDist": 296, "m_flFadeMinDist": 292, "m_flFalloff": 144, "m_flFogContributionStength": 380, "m_flLightStyleStartTime": 404, "m_flMinRoughness": 412, "m_flNearClipPlane": 384, "m_flOrthoLightHeight": 208, "m_flOrthoLightWidth": 204, "m_flPhi": 164, "m_flPrecomputedMaxRange": 372, "m_flRange": 140, "m_flShadowCascadeCrossFade": 228, "m_flShadowCascadeDistance0": 236, "m_flShadowCascadeDistance1": 240, "m_flShadowCascadeDistance2": 244, "m_flShadowCascadeDistance3": 248, "m_flShadowCascadeDistanceFade": 232, "m_flShadowFadeMaxDist": 304, "m_flShadowFadeMinDist": 300, "m_flSkyIntensity": 392, "m_flTheta": 160, "m_hLightCookie": 168, "m_nBakedShadowIndex": 276, "m_nCascadeRenderStaticObjects": 224, "m_nCascades": 176, "m_nCastShadows": 180, "m_nDirectLight": 284, "m_nFogLightingMode": 376, "m_nIndirectLight": 288, "m_nRenderSpecular": 196, "m_nShadowCascadeResolution0": 252, "m_nShadowCascadeResolution1": 256, "m_nShadowCascadeResolution2": 260, "m_nShadowCascadeResolution3": 264, "m_nShadowHeight": 188, "m_nShadowPriority": 272, "m_nShadowWidth": 184, "m_nStyle": 212, "m_vPrecomputedBoundsMaxs": 324, "m_vPrecomputedBoundsMins": 312, "m_vPrecomputedOBBAngles": 348, "m_vPrecomputedOBBExtent": 360, "m_vPrecomputedOBBOrigin": 336}, "metadata": [{"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_SecondaryColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessMult", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fl<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation0", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation1", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAttenuation2", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTheta", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flPhi", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hLightCookie", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_nCascades", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCastShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowWidth", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowHeight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderDiffuse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nRenderSpecular", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderTransmissive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flOrthoLightWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOrthoLightHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nStyle", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_<PERSON>tern", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_nCascadeRenderStaticObjects", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flShadowCascadeCrossFade", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistanceFade", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance0", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance1", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance2", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowCascadeDistance3", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nShadowCascadeResolution0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowCascadeResolution3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bUsesBakedShadowing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nShadowPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBakedShadowIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderToCubemaps", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nDirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFadeMinDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeMaxDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeMinDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeMaxDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bFlicker", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPrecomputed<PERSON><PERSON>sValid", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vPrecomputedBoundsMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedBoundsMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flPrecomputedMaxRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogLightingMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogContributionStength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flNearClipPlane", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_SkyColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flSkyIntensity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_SkyAmbientBounce", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bUseSecondaryColor", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMixedShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flLightStyleStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flCapsuleLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinRoughness", "type": "NetworkVarNames", "type_name": "float"}], "parent": "CEntityComponent"}, "CLogicRelay": {"fields": {"m_OnSpawn": 1424, "m_OnTrigger": 1384, "m_bDisabled": 1464, "m_bFastRetrigger": 1467, "m_bPassthoughCaller": 1468, "m_bTriggerOnce": 1466, "m_bWaitForRefire": 1465}, "metadata": [], "parent": "CLogicalEntity"}, "CLogicalEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "CMapInfo": {"fields": {"m_bDisableAutoGeneratedDMSpawns": 1397, "m_bFadePlayerVisibilityFarZ": 1408, "m_bRainTraceToSkyEnabled": 1409, "m_bUseNormalSpawnsForDM": 1396, "m_flBombRadius": 1388, "m_flBotMaxVisionDistance": 1400, "m_iBuyingStatus": 1384, "m_iHostageCount": 1404, "m_iPetPopulation": 1392}, "metadata": [], "parent": "C_PointEntity"}, "CModelState": {"fields": {"m_MeshGroupMask": 408, "m_ModelName": 168, "m_bClientClothCreationSuppressed": 232, "m_hModel": 160, "m_nClothUpdateFlags": 540, "m_nForceLOD": 539, "m_nIdealMotionType": 538}, "metadata": [{"name": "m_hModel", "type": "NetworkVarNames", "type_name": "HModelStrong"}, {"name": "m_bClientClothCreationSuppressed", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_MeshGroupMask", "type": "NetworkVarNames", "type_name": "MeshGroupMask_t"}, {"name": "m_nIdealMotionType", "type": "NetworkVarNames", "type_name": "int8"}], "parent": null}, "CNetworkedSequenceOperation": {"fields": {"m_bDiscontinuity": 29, "m_bSequenceChangeNetworked": 28, "m_flCycle": 16, "m_flPrevCycle": 12, "m_flPrevCycleForAnimEventDetection": 36, "m_flPrevCycleFromDiscontinuity": 32, "m_flWeight": 20, "m_hSequence": 8}, "metadata": [{"name": "m_hSequence", "type": "NetworkVarNames", "type_name": "HSequence"}, {"name": "m_flPrevCycle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCycle", "type": "NetworkVarNames", "type_name": "float32"}], "parent": null}, "CPathSimple": {"fields": {"m_pathString": 1472}, "metadata": [{"name": "m_pathString", "type": "NetworkVarNames", "type_name": "CUtlString"}], "parent": "C_BaseEntity"}, "CPathSimpleAPI": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CPlayer_AutoaimServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_CameraServices": {"fields": {"m_CurrentFog": 320, "m_OverrideFogColor": 433, "m_PlayerFog": 88, "m_PostProcessingVolumes": 288, "m_angDemoViewAngles": 504, "m_audio": 168, "m_bOverrideFogColor": 428, "m_bOverrideFogStartEnd": 453, "m_fOverrideFogEnd": 480, "m_fOverrideFogStart": 460, "m_flCsViewPunchAngleTickRatio": 80, "m_flOldPlayerViewOffsetZ": 316, "m_flOldPlayerZ": 312, "m_hActivePostProcessingVolume": 500, "m_hColorCorrectionCtrl": 152, "m_hOldFogController": 424, "m_hTonemapController": 160, "m_hViewEntity": 156, "m_nCsViewPunchAngleTick": 76, "m_vecCsViewPunchAngle": 64}, "metadata": [{"name": "m_vecCsViewPunchAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_nCsViewPunchAngleTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flCsViewPunchAngleTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_PlayerFog", "type": "NetworkVarNames", "type_name": "fogplayerparams_t"}, {"name": "m_hColorCorrectionCtrl", "type": "NetworkVarNames", "type_name": "CHandle<CColorCorrection>"}, {"name": "m_hViewEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hTonemapController", "type": "NetworkVarNames", "type_name": "CHandle<CTonemapController2>"}, {"name": "m_audio", "type": "NetworkVarNames", "type_name": "audioparams_t"}, {"name": "m_PostProcessingVolumes", "type": "NetworkVarNames", "type_name": "CHandle<C_PostProcessingVolume>"}], "parent": "CPlayerPawnComponent"}, "CPlayer_FlashlightServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_ItemServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_MovementServices": {"fields": {"m_arrForceSubtickMoveWhen": 412, "m_flForwardMove": 428, "m_flLeftMove": 432, "m_flMaxspeed": 408, "m_flUpMove": 436, "m_nButtonDoublePressed": 120, "m_nButtons": 72, "m_nImpulse": 64, "m_nLastCommandNumberProcessed": 384, "m_nQueuedButtonChangeMask": 112, "m_nQueuedButtonDownMask": 104, "m_nToggleButtonDownMask": 392, "m_pButtonPressedCmdNumber": 128, "m_vecLastMovementImpulses": 440, "m_vecOldViewAngles": 452}, "metadata": [{"name": "m_nToggleButtonDownMask", "type": "NetworkVarNames", "type_name": "ButtonBitMask_t"}, {"name": "m_flMaxspeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_arrForceSubtickMoveWhen", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "CPlayerPawnComponent"}, "CPlayer_MovementServices_Humanoid": {"fields": {"m_bDucked": 492, "m_bDucking": 493, "m_bInCrouch": 480, "m_bInDuckJump": 494, "m_flCrouchTransitionStartTime": 488, "m_flFallVelocity": 476, "m_flStepSoundTime": 472, "m_flSurfaceFriction": 508, "m_groundNormal": 496, "m_nCrouchState": 484, "m_nStepside": 528, "m_surfaceProps": 512}, "metadata": [{"name": "m_flFallVelocity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bIn<PERSON>rouch", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nCrouchState", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flCrouchTransitionStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bDucked", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDucking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInDuckJump", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CPlayer_MovementServices"}, "CPlayer_ObserverServices": {"fields": {"m_bForcedObserverMode": 76, "m_flObserverChaseDistance": 80, "m_flObserverChaseDistanceCalcTime": 84, "m_hObserverTarget": 68, "m_iObserverLastMode": 72, "m_iObserverMode": 64}, "metadata": [{"name": "m_iObserverMode", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hObserverTarget", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CPlayerPawnComponent"}, "CPlayer_UseServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_ViewModelServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_WaterServices": {"fields": {}, "metadata": [], "parent": "CPlayerPawnComponent"}, "CPlayer_WeaponServices": {"fields": {"m_hActiveWeapon": 88, "m_hLastWeapon": 92, "m_hMyWeapons": 64, "m_iAmmo": 96}, "metadata": [{"name": "m_hMyWeapons", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePlayerWeapon>"}, {"name": "m_hActiveWeapon", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerWeapon>"}, {"name": "m_hLastWeapon", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerWeapon>"}, {"name": "m_iAmmo", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": "CPlayerPawnComponent"}, "CPointChildModifier": {"fields": {"m_bOrphanInsteadOfDeletingChildrenOnRemove": 1384}, "metadata": [], "parent": "C_PointEntity"}, "CPointOffScreenIndicatorUi": {"fields": {"m_bBeenEnabled": 3984, "m_bHide": 3985, "m_flSeenTargetTime": 3988, "m_pTargetPanel": 3992}, "metadata": [], "parent": "C_PointClientUIWorldPanel"}, "CPointTemplate": {"fields": {"m_ScriptCallbackScope": 1520, "m_ScriptSpawnCallback": 1512, "m_SpawnedEntityHandles": 1488, "m_bAsynchronouslySpawnEntities": 1412, "m_clientOnlyEntityBehavior": 1456, "m_createdSpawnGroupHandles": 1464, "m_flTimeoutInterval": 1408, "m_iszEntityFilterName": 1400, "m_iszSource2EntityLumpName": 1392, "m_iszWorldName": 1384, "m_ownerSpawnGroupType": 1460, "m_pOutputOnSpawned": 1416}, "metadata": [], "parent": "CLogicalEntity"}, "CPointTemplateAPI": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CPrecipitationVData": {"fields": {"m_bBatchSameVolumeType": 272, "m_flInnerDistance": 264, "m_nAttachType": 268, "m_nRTEnvCP": 276, "m_nRTEnvCPComponent": 280, "m_szModifier": 288, "m_szParticlePrecipitationEffect": 40}, "metadata": [{"name": "MGetKV3ClassDefaults", "type": "Unknown"}], "parent": "CEntitySubclassVDataBase"}, "CProjectedTextureBase": {"fields": {"m_LightColor": 36, "m_SpotlightTextureName": 84, "m_bAlwaysUpdate": 17, "m_bCameraSpace": 28, "m_bEnableShadows": 24, "m_bFlipHorizontal": 620, "m_bLightOnlyTarget": 26, "m_bLightWorld": 27, "m_bSimpleProjection": 25, "m_bState": 16, "m_bVolumetric": 52, "m_flAmbient": 80, "m_flBrightnessScale": 32, "m_flColorTransitionTime": 76, "m_flFarZ": 608, "m_flFlashlightTime": 64, "m_flIntensity": 40, "m_flLightFOV": 20, "m_flLinearAttenuation": 44, "m_flNearZ": 604, "m_flNoiseStrength": 60, "m_flPlaneOffset": 72, "m_flProjectionSize": 612, "m_flQuadraticAttenuation": 48, "m_flRotation": 616, "m_flVolumetricIntensity": 56, "m_hTargetEntity": 12, "m_nNumPlanes": 68, "m_nShadowQuality": 600, "m_nSpotlightTextureFrame": 596}, "metadata": [{"name": "m_hTargetEntity", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_bState", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAlwaysUpdate", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flLightFOV", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bEnableShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSimpleProjection", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLightOnlyTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLightWorld", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCameraSpace", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_LightColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flIntensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flLinearAttenuation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flQuadraticAttenuation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bVolumetric", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flVolumetricIntensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flNoiseStrength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFlashlightTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNumPlanes", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flPlaneOffset", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flColorTransitionTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flAmbient", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_SpotlightTextureName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_nSpotlightTextureFrame", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nShadowQuality", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flNearZ", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFarZ", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flProjectionSize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRotation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bFlipHorizontal", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "CPropDataComponent": {"fields": {"m_bSpawnMotionDisabled": 52, "m_flDmgModBullet": 16, "m_flDmgModClub": 20, "m_flDmgModExplosive": 24, "m_flDmgModFire": 28, "m_iszBasePropData": 40, "m_iszPhysicsDamageTableName": 32, "m_nDisableTakePhysicsDamageSpawnFlag": 56, "m_nInteractions": 48, "m_nMotionDisabledSpawnFlag": 60}, "metadata": [], "parent": "CEntityComponent"}, "CRagdollManager": {"fields": {"m_iCurrentMaxRagdollCount": 1384}, "metadata": [{"name": "m_iCurrentMaxRagdollCount", "type": "NetworkVarNames", "type_name": "int8"}], "parent": "C_BaseEntity"}, "CRenderComponent": {"fields": {"__m_pChainEntity": 16, "m_bEnableRendering": 96, "m_bInterpolationReadyToDraw": 176, "m_bIsRenderingWithViewModels": 80, "m_nSplitscreenFlags": 84}, "metadata": [], "parent": "CEntityComponent"}, "CSMatchStats_t": {"fields": {"m_iEnemy3Ks": 112, "m_iEnemy4Ks": 108, "m_iEnemy5Ks": 104, "m_iEnemyKnifeKills": 116, "m_iEnemyTaserKills": 120}, "metadata": [{"name": "m_iEnemy5Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemy4Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemy3Ks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemyKnifeKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemyTaserKills", "type": "NetworkVarNames", "type_name": "int"}], "parent": "CSPerRoundStats_t"}, "CSPerRoundStats_t": {"fields": {"m_iAssists": 56, "m_iCashEarned": 88, "m_iDamage": 60, "m_iDeaths": 52, "m_iEnemiesFlashed": 96, "m_iEquipmentValue": 64, "m_iHeadShotKills": 80, "m_iKillReward": 72, "m_iKills": 48, "m_iLiveTime": 76, "m_iMoneySaved": 68, "m_iObjective": 84, "m_iUtilityDamage": 92}, "metadata": [{"name": "m_i<PERSON><PERSON>s", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iDeaths", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iAssists", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEquipmentValue", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMoneySaved", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iKillReward", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iLiveTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iHeadShotKills", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iObjective", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iCashEarned", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iUtilityDamage", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEnemiesFlashed", "type": "NetworkVarNames", "type_name": "int"}], "parent": null}, "CScriptComponent": {"fields": {"m_scriptClassName": 48}, "metadata": [], "parent": "CEntityComponent"}, "CServerOnlyModelEntity": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "CSharedGapTypeQueryRegistration": {"fields": {}, "metadata": [{"name": "MGapTypeQueriesForScopeSingleton", "type": "Unknown"}], "parent": null}, "CSkeletonInstance": {"fields": {"m_bDirtyMotionType": 0, "m_bDisableSolidCollisionsForHierarchy": 930, "m_bIsAnimationEnabled": 928, "m_bIsGeneratingLatchedParentSpaceState": 0, "m_bUseParentRenderBounds": 929, "m_materialGroup": 932, "m_modelState": 368, "m_nHitboxSet": 936}, "metadata": [{"name": "m_modelState", "type": "NetworkVarNames", "type_name": "CModelState"}, {"name": "m_bIsAnimationEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseParentRenderBounds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_materialGroup", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_nHitboxSet", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": "CGameSceneNode"}, "CSkyboxReference": {"fields": {"m_hSkyCamera": 1388, "m_worldGroupId": 1384}, "metadata": [], "parent": "C_BaseEntity"}, "CSpriteOriented": {"fields": {}, "metadata": [], "parent": "C_Sprite"}, "CTakeDamageInfoAPI": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "CTimeline": {"fields": {"m_bStopped": 544, "m_flFinalValue": 536, "m_flInterval": 532, "m_flValues": 16, "m_nBucketCount": 528, "m_nCompressionType": 540, "m_nValueCounts": 272}, "metadata": [{"name": "m_flValues", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nValueCounts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBucketCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flInterval", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFinalValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nCompressionType", "type": "NetworkVarNames", "type_name": "TimelineCompression_t"}, {"name": "m_bStopped", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "IntervalTimer"}, "CWaterSplasher": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_AK47": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_AttributeContainer": {"fields": {"m_Item": 80, "m_iExternalItemProviderRegisteredToken": 1176, "m_ullRegisteredAsItemID": 1184}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_Item", "type": "NetworkVarNames", "type_name": "CEconItemView"}], "parent": "CAttributeManager"}, "C_BarnLight": {"fields": {"m_Color": 3376, "m_LightStyleEvents": 3456, "m_LightStyleString": 3416, "m_LightStyleTargets": 3480, "m_QueuedLightStyleStrings": 3432, "m_StyleEvent": 3504, "m_VisClusters": 4176, "m_bContactShadow": 3748, "m_bEnabled": 3368, "m_bFogMixedShadows": 3796, "m_bInitialBoneSetup": 4168, "m_bPrecomputedFieldsValid": 3816, "m_fAlternateColorBrightness": 3776, "m_flBounceScale": 3756, "m_flBrightness": 3384, "m_flBrightnessScale": 3388, "m_flColorTemperature": 3380, "m_flFadeSizeEnd": 3804, "m_flFadeSizeStart": 3800, "m_flFogScale": 3792, "m_flFogStrength": 3784, "m_flLightStyleStartTime": 3424, "m_flLuminaireAnisotropy": 3408, "m_flLuminaireSize": 3404, "m_flMinRoughness": 3760, "m_flRange": 3704, "m_flShadowFadeSizeEnd": 3812, "m_flShadowFadeSizeStart": 3808, "m_flShape": 3672, "m_flSkirt": 3684, "m_flSkirtNear": 3688, "m_flSoftX": 3676, "m_flSoftY": 3680, "m_hLightCookie": 3664, "m_nBakeSpecularToCubemaps": 3720, "m_nBakedShadowIndex": 3396, "m_nBounceLight": 3752, "m_nCastShadows": 3736, "m_nColorMode": 3372, "m_nDirectLight": 3392, "m_nFog": 3780, "m_nFogShadows": 3788, "m_nLuminaireShape": 3400, "m_nPrecomputedSubFrusta": 3880, "m_nShadowMapSize": 3740, "m_nShadowPriority": 3744, "m_vAlternateColor": 3764, "m_vBakeSpecularToCubemapsSize": 3724, "m_vPrecomputedBoundsMaxs": 3832, "m_vPrecomputedBoundsMins": 3820, "m_vPrecomputedOBBAngles": 3856, "m_vPrecomputedOBBAngles0": 3896, "m_vPrecomputedOBBAngles1": 3932, "m_vPrecomputedOBBAngles2": 3968, "m_vPrecomputedOBBAngles3": 4004, "m_vPrecomputedOBBAngles4": 4040, "m_vPrecomputedOBBAngles5": 4076, "m_vPrecomputedOBBExtent": 3868, "m_vPrecomputedOBBExtent0": 3908, "m_vPrecomputedOBBExtent1": 3944, "m_vPrecomputedOBBExtent2": 3980, "m_vPrecomputedOBBExtent3": 4016, "m_vPrecomputedOBBExtent4": 4052, "m_vPrecomputedOBBExtent5": 4088, "m_vPrecomputedOBBOrigin": 3844, "m_vPrecomputedOBBOrigin0": 3884, "m_vPrecomputedOBBOrigin1": 3920, "m_vPrecomputedOBBOrigin2": 3956, "m_vPrecomputedOBBOrigin3": 3992, "m_vPrecomputedOBBOrigin4": 4028, "m_vPrecomputedOBBOrigin5": 4064, "m_vShear": 3708, "m_vSizeParams": 3692}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nColorMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flColorTemperature", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nDirectLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nBakedShadowIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nLuminaireShape", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flLuminaireSize", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flLuminaireAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_LightStyleString", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_flLightStyleStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_QueuedLightStyleStrings", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_LightStyleEvents", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_LightStyleTargets", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_hLightCookie", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_flShape", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSoftX", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSoftY", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSkirt", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSkirtNear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vSizeParams", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vShear", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nBakeSpecularToCubemaps", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBakeSpecularToCubemapsSize", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nCastShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowMapSize", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nShadowPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bContactShadow", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nBounceLight", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flBounceScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinRoughness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vAlternateColor", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fAlternateColorBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFog", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogShadows", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bFogMixedShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFadeSizeStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeSizeEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeSizeStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flShadowFadeSizeEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bPrecomputed<PERSON><PERSON>sValid", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vPrecomputedBoundsMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedBoundsMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nPrecomputedSubFrusta", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vPrecomputedOBBOrigin0", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles0", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent0", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles1", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent1", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles2", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent2", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin3", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles3", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent3", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin4", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles4", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent4", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBOrigin5", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vPrecomputedOBBAngles5", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_vPrecomputedOBBExtent5", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_VisClusters", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": "C_BaseModelEntity"}, "C_BaseButton": {"fields": {"m_glowEntity": 3368, "m_szDisplayText": 3376, "m_usable": 3372}, "metadata": [{"name": "m_glowEntity", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}, {"name": "m_usable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szDisplayText", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseToggle"}, "C_BaseCSGrenade": {"fields": {"m_bClientPredictDelete": 6944, "m_bIsHeldByPlayer": 6946, "m_bJumpThrow": 6948, "m_bJustPulledPin": 6972, "m_bPinPulled": 6947, "m_bRedraw": 6945, "m_bThrowAnimating": 6949, "m_fDropTime": 6964, "m_fPinPullTime": 6968, "m_fThrowTime": 6952, "m_flNextHoldFrac": 6980, "m_flThrowStrength": 6956, "m_flThrowStrengthApproach": 6960, "m_hSwitchToWeaponAfterThrow": 6984, "m_nNextHoldTick": 6976}, "metadata": [{"name": "m_bRedraw", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHeldByPlayer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPinPulled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bJumpThrow", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bThrowAnimating", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fThrowTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flThrowStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flThrowStrengthApproach", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fDropTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_fPinPullTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bJustPulledPin", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nNextHoldTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextHoldFrac", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hSwitchToWeaponAfterThrow", "type": "NetworkVarNames", "type_name": "CHandle<CCSWeaponBase>"}], "parent": "C_CSWeaponBase"}, "C_BaseCSGrenadeProjectile": {"fields": {"flNextTrailLineTime": 4536, "m_arrTrajectoryTrailPointCreationTimes": 4584, "m_arrTrajectoryTrailPoints": 4560, "m_bCanCreateGrenadeTrail": 4541, "m_bExplodeEffectBegan": 4540, "m_flSpawnTime": 4520, "m_flTrajectoryTrailEffectCreationTime": 4608, "m_hSnapshotTrajectoryParticleSnapshot": 4552, "m_nBounces": 4488, "m_nExplodeEffectIndex": 4496, "m_nExplodeEffectTickBegin": 4504, "m_nSnapshotTrajectoryEffectIndex": 4544, "m_vInitialPosition": 4464, "m_vInitialVelocity": 4476, "m_vecExplodeEffectOrigin": 4508, "vecLastTrailLinePos": 4524}, "metadata": [{"name": "m_vInitialPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vInitialVelocity", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nBounces", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nExplodeEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_nExplodeEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vecExplodeEffectOrigin", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseGrenade"}, "C_BaseClientUIEntity": {"fields": {"m_DialogXMLName": 3384, "m_PanelClassName": 3392, "m_PanelID": 3400, "m_bEnabled": 3376}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_DialogXMLName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_PanelClassName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_PanelID", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseModelEntity"}, "C_BaseCombatCharacter": {"fields": {"m_flWaterNextTraceTime": 4420, "m_flWaterWorldZ": 4416, "m_hMyWearables": 4384, "m_leftFootAttachment": 4408, "m_nWaterWakeMode": 4412, "m_rightFootAttachment": 4409}, "metadata": [{"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "m_hMyWearables", "type": "NetworkVarNames", "type_name": "CHandle<C_EconWearable>"}], "parent": "C_BaseFlex"}, "C_BaseDoor": {"fields": {"m_bIsUsable": 3368}, "metadata": [{"name": "m_bIsUsable", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseToggle"}, "C_BaseEntity": {"fields": {"m_CBodyComponent": 56, "m_DataChangeEventRef": 1316, "m_EntClientFlags": 992, "m_ListEntry": 960, "m_MoveCollide": 1092, "m_MoveType": 1093, "m_NetworkTransmitComponent": 64, "m_Particles": 1176, "m_aThinkFunctions": 912, "m_bAnimTimeChanged": 1357, "m_bAnimatedEveryTick": 1128, "m_bApplyLayerMatchIDToModel": 883, "m_bClientSideRagdoll": 994, "m_bDisabledContextThinks": 936, "m_bHasAddedVarsToInterpolation": 950, "m_bHasSuccessfullyInterpolated": 949, "m_bInterpolateEvenWithNoModel": 881, "m_bPredictable": 1153, "m_bPredictionEligible": 882, "m_bRenderEvenWhenNotSuccessfullyInterpolated": 951, "m_bRenderWithViewModels": 1154, "m_bSimulationTimeChanged": 1358, "m_bTakesDamage": 841, "m_dependencies": 1320, "m_fBBoxVisFlags": 1152, "m_fEffects": 1100, "m_fFlags": 1004, "m_flAnimTime": 940, "m_flCreateTime": 984, "m_flElasticity": 1116, "m_flFriction": 1112, "m_flGravityScale": 1120, "m_flNavIgnoreUntilTime": 1132, "m_flProxyRandomValue": 872, "m_flSimulationTime": 944, "m_flSpeed": 988, "m_flTimeScale": 1124, "m_flWaterLevel": 1096, "m_hEffectEntity": 1084, "m_hGroundEntity": 1104, "m_hOldMoveParent": 1168, "m_hOwnerEntity": 1088, "m_hSceneObjectController": 860, "m_hThink": 1136, "m_iCurrentThinkContext": 908, "m_iEFlags": 876, "m_iHealth": 836, "m_iMaxHealth": 832, "m_iTeamNum": 995, "m_lifeState": 840, "m_nActualMoveType": 1094, "m_nBloodType": 1376, "m_nCreationTick": 1344, "m_nFirstPredictableCommand": 1160, "m_nGroundBodyIndex": 1108, "m_nInterpolationLatchDirtyFlags": 952, "m_nLastPredictableCommand": 1164, "m_nLastThinkTick": 800, "m_nNextScriptVarRecordID": 1288, "m_nNextThinkTick": 1000, "m_nNoInterpolationTick": 864, "m_nPlatformType": 856, "m_nSceneObjectOverrideFlags": 948, "m_nSimulationTick": 904, "m_nSplitUserPlayerPredictionSlot": 1156, "m_nSubclassID": 888, "m_nTakeDamageFlags": 848, "m_nVisibilityNoInterpolationTick": 868, "m_nWaterType": 880, "m_pCollision": 824, "m_pGameSceneNode": 808, "m_pRenderComponent": 816, "m_sUniqueHammerID": 1368, "m_spawnflags": 996, "m_tokLayerMatchID": 884, "m_ubInterpolationFrame": 857, "m_vecAbsVelocity": 1008, "m_vecAngVelocity": 1304, "m_vecBaseVelocity": 1072, "m_vecPredictedScriptFloatIDs": 1240, "m_vecPredictedScriptFloats": 1216, "m_vecVelocity": 1024}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_CBodyComponent", "type": "NetworkVarNames", "type_name": "CBodyComponent::Storage_t"}, {"name": "m_iMaxHealth", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iHealth", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_lifeState", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_bTakesDamage", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nTakeDamageFlags", "type": "NetworkVarNames", "type_name": "TakeDamageFlags_t"}, {"name": "m_nPlatformType", "type": "NetworkVarNames", "type_name": "EntityPlatformTypes_t"}, {"name": "m_ubInterpolationFrame", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nSubclassID", "type": "NetworkVarNames", "type_name": "EntitySubclassID_t"}, {"name": "m_flAnimTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSimulationTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCreateTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bClientSideRagdoll", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iTeamNum", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_spawnflags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nNextThinkTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_fFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_vecBaseVelocity", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_hEffectEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hOwnerEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_MoveCollide", "type": "NetworkVarNames", "type_name": "MoveCollide_t"}, {"name": "m_MoveType", "type": "NetworkVarNames", "type_name": "MoveType_t"}, {"name": "m_flWaterLevel", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fEffects", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_hGroundEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nGroundBodyIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFriction", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flElasticity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flGravityScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flTimeScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bAnimatedEveryTick", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flNavIgnoreUntilTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nBloodType", "type": "NetworkVarNames", "type_name": "BloodType"}], "parent": "CEntityInstance"}, "C_BaseEntityAPI": {"fields": {}, "metadata": [{"name": "MPulseProvideFeatureTag", "type": "Unknown"}, {"name": "MPulseLibraryBindings", "type": "Unknown"}], "parent": null}, "C_BaseFire": {"fields": {"m_flScale": 1384, "m_flScaleTime": 1392, "m_flStartScale": 1388, "m_nFlags": 1396}, "metadata": [{"name": "m_flScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flStartScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flScaleTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFlags", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "C_BaseEntity"}, "C_BaseFlex": {"fields": {"m_CachedViewTarget": 4140, "m_PhonemeClasses": 4288, "m_bResetFlexWeightsOnModelChange": 4182, "m_blinktime": 4160, "m_blinktoggle": 4040, "m_flBlinkAmount": 4176, "m_flJawOpenAmount": 4172, "m_flexWeight": 3992, "m_iBlink": 4156, "m_iEyeAttachment": 4181, "m_iJawOpen": 4168, "m_iMouthAttachment": 4180, "m_mEyeOcclusionRendererCameraToBoneTransform": 4212, "m_nEyeOcclusionRendererBone": 4208, "m_nLastFlexUpdateFrameCount": 4136, "m_nNextSceneEventId": 4152, "m_prevblinktoggle": 4164, "m_vEyeOcclusionRendererHalfExtent": 4260, "m_vLookTargetPosition": 4016}, "metadata": [{"name": "m_flexWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_blinktoggle", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CBaseAnimGraph"}, "C_BaseFlex__Emphasized_Phoneme": {"fields": {"m_bBasechecked": 29, "m_bRequired": 28, "m_bValid": 30, "m_flAmount": 24, "m_sClassName": 0}, "metadata": [], "parent": null}, "C_BaseGrenade": {"fields": {"m_DmgRadius": 4388, "m_ExplosionSound": 4416, "m_bHasWarnedAI": 4384, "m_bIsLive": 4386, "m_bIsSmokeGrenade": 4385, "m_flDamage": 4400, "m_flDetonateTime": 4392, "m_flNextAttack": 4452, "m_flWarnAITime": 4396, "m_hOriginalThrower": 4456, "m_hThrower": 4428, "m_iszBounceSound": 4408}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_bIsLive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_DmgRadius", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flDetonateTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDamage", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hThrower", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}], "parent": "C_BaseFlex"}, "C_BaseModelEntity": {"fields": {"m_CHitboxComponent": 2648, "m_CRenderComponent": 2640, "m_ClientOverrideTint": 3304, "m_Collision": 2896, "m_ConfigEntitiesToPropagateMaterialDecalsTo": 3224, "m_Glow": 3072, "m_LastHitGroup": 2688, "m_bAllowFadeInView": 2746, "m_bInitModelEffects": 2728, "m_bIsStaticProp": 2729, "m_bNoInterpolate": 2889, "m_bRenderToCubemaps": 2888, "m_bUseClientOverrideTint": 3308, "m_clrRender": 2776, "m_fadeMaxDist": 3168, "m_fadeMinDist": 3164, "m_flDecalHealBloodRate": 3212, "m_flDecalHealHeightRate": 3216, "m_flFadeScale": 3172, "m_flGlowBackfaceMult": 3160, "m_flShadowStrength": 3176, "m_iOldHealth": 2740, "m_nAddDecal": 3184, "m_nDecalsAdded": 2736, "m_nLastAddDecal": 2732, "m_nObjectCulling": 3180, "m_nRenderFX": 2745, "m_nRenderMode": 2744, "m_pClientAlphaProperty": 3296, "m_vDecalForwardAxis": 3200, "m_vDecalPosition": 3188, "m_vecRenderAttributes": 2784, "m_vecViewOffset": 3248}, "metadata": [{"name": "m_CRenderComponent", "type": "NetworkVarNames", "type_name": "CRenderComponent::Storage_t"}, {"name": "m_CHitboxComponent", "type": "NetworkVarNames", "type_name": "CHitboxComponent::Storage_t"}, {"name": "m_nRenderMode", "type": "NetworkVarNames", "type_name": "RenderMode_t"}, {"name": "m_nRenderFX", "type": "NetworkVarNames", "type_name": "RenderFx_t"}, {"name": "m_clr<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_vecRenderAttributes", "type": "NetworkVarNames", "type_name": "EntityRenderAttribute_t"}, {"name": "m_bRenderToCubemaps", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoInterpolate", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Collision", "type": "NetworkVarNames", "type_name": "CCollisionProperty"}, {"name": "m_<PERSON>low", "type": "NetworkVarNames", "type_name": "CGlowProperty"}, {"name": "m_flGlowBackfaceMult", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fadeMinDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fadeMaxDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flShadowStrength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nObjectCulling", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nAddDecal", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vDecalPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vDecalForwardAxis", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flDecalHealBloodRate", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDecalHealHeightRate", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_ConfigEntitiesToPropagateMaterialDecalsTo", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseModelEntity>"}], "parent": "C_BaseEntity"}, "C_BasePlayerPawn": {"fields": {"m_ServerViewAngleChanges": 4600, "m_bIsSwappingToPredictableController": 4928, "m_flDeathTime": 4856, "m_flFOVSensitivityAdjust": 4892, "m_flLastCameraSetupTime": 4888, "m_flMouseSensitivity": 4896, "m_flOldSimulationTime": 4912, "m_flPredictionErrorTime": 4872, "m_hController": 4924, "m_iHideHUD": 4708, "m_nHighestConsumedServerViewAngleChangeIndex": 4680, "m_nLastExecutedCommandNumber": 4916, "m_nLastExecutedCommandTick": 4920, "m_pAutoaimServices": 4536, "m_pCameraServices": 4576, "m_pFlashlightServices": 4568, "m_pItemServices": 4528, "m_pMovementServices": 4584, "m_pObserverServices": 4544, "m_pUseServices": 4560, "m_pWaterServices": 4552, "m_pWeaponServices": 4520, "m_skybox3d": 4712, "m_vOldOrigin": 4900, "m_vecLastCameraSetupLocalOrigin": 4876, "m_vecPredictionError": 4860, "v_angle": 4684, "v_anglePrevious": 4696}, "metadata": [{"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_pWeaponServices", "type": "NetworkVarNames", "type_name": "CPlayer_WeaponServices*"}, {"name": "m_pItemServices", "type": "NetworkVarNames", "type_name": "CPlayer_ItemServices*"}, {"name": "m_pAutoaimServices", "type": "NetworkVarNames", "type_name": "CPlayer_AutoaimServices*"}, {"name": "m_pObserverServices", "type": "NetworkVarNames", "type_name": "CPlayer_ObserverServices*"}, {"name": "m_pWaterServices", "type": "NetworkVarNames", "type_name": "CPlayer_WaterServices*"}, {"name": "m_pUseServices", "type": "NetworkVarNames", "type_name": "CPlayer_UseServices*"}, {"name": "m_pFlashlightServices", "type": "NetworkVarNames", "type_name": "CPlayer_FlashlightServices*"}, {"name": "m_pCameraServices", "type": "NetworkVarNames", "type_name": "CPlayer_CameraServices*"}, {"name": "m_pMovementServices", "type": "NetworkVarNames", "type_name": "CPlayer_MovementServices*"}, {"name": "m_ServerViewAngleChanges", "type": "NetworkVarNames", "type_name": "ViewAngleServerChange_t"}, {"name": "m_iHideHUD", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_skybox3d", "type": "NetworkVarNames", "type_name": "sky3dparams_t"}, {"name": "m_flDeathTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_hController", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerController>"}], "parent": "C_BaseCombatCharacter"}, "C_BasePlayerWeapon": {"fields": {"m_flNextPrimaryAttackTickRatio": 5740, "m_flNextSecondaryAttackTickRatio": 5748, "m_iClip1": 5752, "m_iClip2": 5756, "m_nNextPrimaryAttackTick": 5736, "m_nNextSecondaryAttackTick": 5744, "m_pReserveAmmo": 5760}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkUserGroupProxy", "type": "Unknown"}, {"name": "m_nNextPrimaryAttackTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextPrimaryAttackTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNextSecondaryAttackTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flNextSecondaryAttackTickRatio", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iClip1", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iClip2", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_pReserveAmmo", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_EconEntity"}, "C_BasePropDoor": {"fields": {"m_bLocked": 4669, "m_closedAngles": 4684, "m_closedPosition": 4672, "m_eDoorState": 4664, "m_hMaster": 4696, "m_modelChanged": 4668, "m_vWhereToSetLightingOrigin": 4700}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_eDoorState", "type": "NetworkVarNames", "type_name": "DoorState_t"}, {"name": "m_bLocked", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_closedPosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_closedAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_hMaster", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePropDoor>"}], "parent": "C_DynamicProp"}, "C_BaseToggle": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_BaseTrigger": {"fields": {"m_bClientSidePredicted": 3369, "m_bDisabled": 3368}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bClientSidePredicted", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseToggle"}, "C_BaseViewModel": {"fields": {"m_flAnimationStartTime": 4004, "m_hControlPanel": 4072, "m_hOldLayerSequence": 4060, "m_hWeapon": 4008, "m_iCameraAttachment": 4032, "m_nAnimationParity": 4000, "m_nOldAnimationParity": 4056, "m_nViewModelIndex": 3996, "m_oldLayer": 4064, "m_oldLayerStartTime": 4068, "m_previousCycle": 4052, "m_previousElapsedDuration": 4048, "m_sAnimationPrefix": 4024, "m_sVMName": 4016, "m_vecLastCameraAngles": 4036, "m_vecLastFacing": 3984}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_nViewModelIndex", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nAnimationParity", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flAnimationStartTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hWeapon", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerWeapon>"}, {"name": "m_hControlPanel", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}], "parent": "CBaseAnimGraph"}, "C_Beam": {"fields": {"m_bTurnedOff": 3536, "m_fAmplitude": 3516, "m_fEndWidth": 3504, "m_fFadeLength": 3508, "m_fHaloScale": 3512, "m_fSpeed": 3524, "m_fStartFrame": 3520, "m_fWidth": 3500, "m_flDamage": 3380, "m_flFireTime": 3376, "m_flFrame": 3528, "m_flFrameRate": 3368, "m_flHDRColorScale": 3372, "m_hAttachEntity": 3448, "m_hBaseMaterial": 3424, "m_hEndEntity": 3552, "m_nAttachIndex": 3488, "m_nBeamFlags": 3444, "m_nBeamType": 3440, "m_nClipStyle": 3532, "m_nHaloIndex": 3432, "m_nNumBeamEnts": 3384, "m_queryHandleHalo": 3388, "m_vecEndPos": 3540}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_flFrameRate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nNumBeamEnts", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hBaseMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nHaloIndex", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nBeamType", "type": "NetworkVarNames", "type_name": "BeamType_t"}, {"name": "m_nBeamFlags", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_hAttachEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nAttachIndex", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_fWidth", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fEndWidth", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fFadeLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fHaloScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fAmplitude", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fStartFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nClipStyle", "type": "NetworkVarNames", "type_name": "BeamClipStyle_t"}, {"name": "m_bTurnedOff", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecEndPos", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseModelEntity"}, "C_Breakable": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_BreakableProp": {"fields": {"m_BreakableContentsType": 4264, "m_CPropDataComponent": 4040, "m_OnBreak": 4104, "m_OnHealthChanged": 4144, "m_OnTakeDamage": 4184, "m_PerformanceMode": 4256, "m_bHasBreakPiecesOrCommands": 4288, "m_explodeDamage": 4292, "m_explodeRadius": 4296, "m_explosionBuildupSound": 4312, "m_explosionCustomEffect": 4320, "m_explosionCustomSound": 4328, "m_explosionDelay": 4304, "m_explosionModifier": 4336, "m_flDefBurstScale": 4236, "m_flDefaultFadeScale": 4352, "m_flLastPhysicsInfluenceTime": 4348, "m_flPressureDelay": 4232, "m_flPreventDamageBeforeTime": 4260, "m_hBreaker": 4252, "m_hFlareEnt": 4360, "m_hLastAttacker": 4356, "m_hPhysicsAttacker": 4344, "m_iMinHealthDmg": 4228, "m_impactEnergyScale": 4224, "m_noGhostCollision": 4364, "m_strBreakableContentsParticleOverride": 4280, "m_strBreakableContentsPropGroupOverride": 4272, "m_vDefBurstOffset": 4240}, "metadata": [{"name": "m_CPropDataComponent", "type": "NetworkVarNames", "type_name": "CPropDataComponent::Storage_t"}, {"name": "m_noGhostCollision", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "CBaseProp"}, "C_BulletHitModel": {"fields": {"m_bIsHit": 4032, "m_flTimeCreated": 4036, "m_hPlayerParent": 4028, "m_iBoneIndex": 4024, "m_matLocal": 3976, "m_vecStartPos": 4040}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_C4": {"fields": {"m_activeLightParticleIndex": 6976, "m_bBombPlacedAnimation": 6992, "m_bBombPlanted": 7035, "m_bIsPlantingViaUse": 6993, "m_bPlayedArmingBeeps": 7028, "m_bStartedArming": 6984, "m_eActiveLightEffect": 6980, "m_entitySpottedState": 7000, "m_fArmedTime": 6988, "m_nSpotRules": 7024, "m_szScreenText": 6944}, "metadata": [{"name": "m_bStartedArming", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fArmedTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bBombPlacedAnimation", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsPlantingViaUse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}], "parent": "C_CSWeaponBase"}, "C_CS2WeaponModuleBase": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_CSGOViewModel": {"fields": {"m_bNeedToQueueHighResComposite": 4136, "m_bShouldIgnoreOffsetAndAccuracy": 4129, "m_nLastKnownAssociatedWeaponEntIndex": 4132, "m_vLoweredWeaponOffset": 4216}, "metadata": [{"name": "m_bShouldIgnoreOffsetAndAccuracy", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_PredictedViewModel"}, "C_CSGO_CounterTerroristTeamIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_CounterTerroristWingmanIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_EndOfMatchCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_EndOfMatchCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_EndOfMatchLineupEnd": {"fields": {}, "metadata": [], "parent": "C_CSGO_EndOfMatchLineupEndpoint"}, "C_CSGO_EndOfMatchLineupEndpoint": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_EndOfMatchLineupStart": {"fields": {}, "metadata": [], "parent": "C_CSGO_EndOfMatchLineupEndpoint"}, "C_CSGO_MapPreviewCameraPath": {"fields": {"m_bConstantSpeed": 1394, "m_bLoop": 1392, "m_bVerticalFOV": 1393, "m_flDuration": 1396, "m_flPathDuration": 1468, "m_flPathLength": 1464, "m_flZFar": 1384, "m_flZNear": 1388}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_MapPreviewCameraPathNode": {"fields": {"m_flCameraSpeed": 1424, "m_flEaseIn": 1428, "m_flEaseOut": 1432, "m_flFOV": 1420, "m_nPathIndex": 1392, "m_szParentPathUniqueID": 1384, "m_vInTangentLocal": 1396, "m_vInTangentWorld": 1436, "m_vOutTangentLocal": 1408, "m_vOutTangentWorld": 1448}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSGO_PreviewModel": {"fields": {"m_animgraph": 4384, "m_animgraphCharacterModeString": 4392, "m_defaultAnim": 4400, "m_flInitialModelScale": 4412, "m_nDefaultAnimLoopMode": 4408, "m_sInitialWeaponState": 4416}, "metadata": [], "parent": "C_BaseFlex"}, "C_CSGO_PreviewModelAlias_csgo_item_previewmodel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewModel"}, "C_CSGO_PreviewPlayer": {"fields": {"m_animgraph": 14912, "m_animgraphCharacterModeString": 14920, "m_flInitialModelScale": 14928}, "metadata": [], "parent": "C_CSPlayerPawn"}, "C_CSGO_PreviewPlayerAlias_csgo_player_previewmodel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewPlayer"}, "C_CSGO_TeamIntroCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_TeamIntroCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "C_CSGO_TeamIntroTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamIntroCharacterPosition"}, "C_CSGO_TeamPreviewCamera": {"fields": {"m_bDofEnabled": 1500, "m_flDofFarBlurry": 1516, "m_flDofFarCrisp": 1512, "m_flDofNearBlurry": 1504, "m_flDofNearCrisp": 1508, "m_flDofTiltToGround": 1520, "m_nVariant": 1496}, "metadata": [], "parent": "C_CSGO_MapPreviewCameraPath"}, "C_CSGO_TeamPreviewCharacterPosition": {"fields": {"m_agentItem": 1416, "m_glovesItem": 2512, "m_nOrdinal": 1392, "m_nRandom": 1388, "m_nVariant": 1384, "m_sWeaponName": 1400, "m_weaponItem": 3608, "m_xuid": 1408}, "metadata": [{"name": "m_nVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRandom", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOrdinal", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sWeaponName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_xuid", "type": "NetworkVarNames", "type_name": "XUID"}, {"name": "m_agentItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_glovesItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_weaponItem", "type": "NetworkVarNames", "type_name": "CEconItemView"}], "parent": "C_BaseEntity"}, "C_CSGO_TeamPreviewModel": {"fields": {}, "metadata": [], "parent": "C_CSGO_PreviewPlayer"}, "C_CSGO_TeamSelectCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_TeamSelectCharacterPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCharacterPosition"}, "C_CSGO_TeamSelectCounterTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamSelectCharacterPosition"}, "C_CSGO_TeamSelectTerroristPosition": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamSelectCharacterPosition"}, "C_CSGO_TerroristTeamIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGO_TerroristWingmanIntroCamera": {"fields": {}, "metadata": [], "parent": "C_CSGO_TeamPreviewCamera"}, "C_CSGameRules": {"fields": {"m_MatchDevice": 168, "m_MinimapVerticalSectionHeights": 3124, "m_RetakeRules": 3496, "m_TeamRespawnWaveTimes": 2844, "m_arrProhibitedItemIndices": 2244, "m_arrTournamentActiveCasterAccounts": 2444, "m_bAnyHostageReached": 148, "m_bBombDropped": 2468, "m_bBombPlanted": 2469, "m_bCTCantBuy": 2481, "m_bCTTimeOutActive": 78, "m_bFreezePeriod": 64, "m_bGameRestart": 116, "m_bHasMatchStarted": 172, "m_bHasTriggeredRoundStartMusic": 3460, "m_bIsDroppingItems": 2240, "m_bIsHltvActive": 2242, "m_bIsQuestEligible": 2241, "m_bIsQueuedMatchmaking": 152, "m_bIsValveDS": 160, "m_bLogoMap": 161, "m_bMapHasBombTarget": 149, "m_bMapHasBuyZone": 151, "m_bMapHasRescueZone": 150, "m_bMarkClientStopRecordAtRoundEnd": 3288, "m_bMatchWaitingForResume": 97, "m_bPlayAllStepSoundsOnServer": 162, "m_bRoundEndNoMusic": 3844, "m_bRoundEndShowTimerDefend": 3800, "m_bServerPaused": 76, "m_bSpawnedTerrorHuntHeavy": 3156, "m_bSwitchingTeamsAtRoundReset": 3461, "m_bTCantBuy": 2480, "m_bTeamIntroPeriod": 3788, "m_bTechnicalTimeOut": 96, "m_bTerroristTimeOutActive": 77, "m_bWarmupPeriod": 65, "m_eRoundEndReason": 3796, "m_eRoundWinReason": 2476, "m_fMatchStartTime": 104, "m_fRoundStartTime": 108, "m_fWarmupPeriodEnd": 68, "m_fWarmupPeriodStart": 72, "m_flCMMItemDropRevealEndTime": 2236, "m_flCMMItemDropRevealStartTime": 2232, "m_flCTTimeOutRemaining": 84, "m_flGameStartTime": 120, "m_flLastPerfSampleTime": 20256, "m_flNextRespawnWave": 2972, "m_flRestartRoundTime": 112, "m_flTerroristTimeOutRemaining": 80, "m_gamePhase": 128, "m_iHostagesRemaining": 144, "m_iMatchStats_PlayersAlive_CT": 2604, "m_iMatchStats_PlayersAlive_T": 2724, "m_iMatchStats_RoundResults": 2484, "m_iNumConsecutiveCTLoses": 3252, "m_iNumConsecutiveTerroristLoses": 3256, "m_iRoundEndFunFactData1": 3820, "m_iRoundEndFunFactData2": 3824, "m_iRoundEndFunFactData3": 3828, "m_iRoundEndFunFactPlayerSlot": 3816, "m_iRoundEndLegacy": 3848, "m_iRoundEndPlayerCount": 3840, "m_iRoundEndTimerTime": 3804, "m_iRoundEndWinnerTeam": 3792, "m_iRoundStartRoundNumber": 3856, "m_iRoundTime": 100, "m_iRoundWinStatus": 2472, "m_iSpectatorSlotCount": 164, "m_nCTTeamIntroVariant": 3784, "m_nCTTimeOuts": 92, "m_nEndMatchMapGroupVoteOptions": 3208, "m_nEndMatchMapGroupVoteTypes": 3168, "m_nEndMatchMapVoteWinner": 3248, "m_nHalloweenMaskListSeed": 2464, "m_nMatchAbortedEarlyReason": 3456, "m_nMatchEndCount": 3776, "m_nNextMapInMapgroup": 176, "m_nOvertimePlaying": 140, "m_nQueuedMatchmakingMode": 156, "m_nRoundEndCount": 3852, "m_nRoundStartCount": 3860, "m_nRoundsPlayedThisPhase": 136, "m_nTTeamIntroVariant": 3780, "m_nTerroristTimeOuts": 88, "m_nTournamentPredictionsPct": 2228, "m_numBestOfMaps": 2460, "m_pGameModeRules": 3488, "m_sRoundEndFunFactToken": 3808, "m_sRoundEndMessage": 3832, "m_szMatchStatTxt": 1204, "m_szTournamentEventName": 180, "m_szTournamentEventStage": 692, "m_szTournamentPredictionsTxt": 1716, "m_timeUntilNextPhaseStarts": 124, "m_totalRoundsPlayed": 132, "m_ullLocalMatchID": 3160, "m_vMinimapMaxs": 3112, "m_vMinimapMins": 3100}, "metadata": [{"name": "m_bFreezePeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bWarmupPeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fWarmupPeriodEnd", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_fWarmupPeriodStart", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bServerPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bTerroristTimeOutActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCTTimeOutActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTerroristTimeOutRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flCTTimeOutRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nTerroristTimeOuts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCTTimeOuts", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bTechnicalTimeOut", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMatchWaitingForResume", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fMatchStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fRoundStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flRestartRoundTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bGameRestart", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGameStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_timeUntilNextPhaseStarts", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_gamePhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_totalRoundsPlayed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundsPlayedThisPhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nOvertimePlaying", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iHostagesRemaining", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAnyHostageReached", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasBombTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasRescueZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMapHasBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsQueuedMatchmaking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nQueuedMatchmakingMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsValveDS", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLogoMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPlayAllStepSoundsOnServer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iSpectatorSlotCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_MatchDevice", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bHasMatchStarted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nNextMapInMapgroup", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_szTournamentEventName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTournamentEventStage", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szMatchStatTxt", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTournamentPredictionsTxt", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_nTournamentPredictionsPct", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flCMMItemDropRevealStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flCMMItemDropRevealEndTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsDroppingItems", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsQuestEligible", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsHltvActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_arrProhibitedItemIndices", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_arrTournamentActiveCasterAccounts", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_numBestOfMaps", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nHalloweenMaskListSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bBombDropped", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bBombPlanted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundWinStatus", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_eRoundWinReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_b<PERSON>ant<PERSON>uy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bCTCantBuy", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iMatchStats_RoundResults", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMatchStats_PlayersAlive_CT", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMatchStats_PlayersAlive_T", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_TeamRespawnWaveTimes", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flNextRespawnWave", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_vMinimapMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMinimapMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_MinimapVerticalSectionHeights", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nEndMatchMapGroupVoteTypes", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchMapGroupVoteOptions", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEndMatchMapVoteWinner", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumConsecutiveCTLoses", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumConsecutiveTerroristLoses", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMatchAbortedEarlyReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_pGameModeRules", "type": "NetworkVarNames", "type_name": "CCSGameModeRules*"}, {"name": "m_RetakeRules", "type": "NetworkVarNames", "type_name": "CRetakeGameRules"}, {"name": "m_nMatchEndCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nTTeamIntroVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCTTeamIntroVariant", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bTeamIntroPeriod", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndWinnerTeam", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_eRoundEndReason", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRoundEndShowTimerDefend", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndTimerTime", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sRoundEndFunFactToken", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_iRoundEndFunFactPlayerSlot", "type": "NetworkVarNames", "type_name": "CPlayerSlot"}, {"name": "m_iRoundEndFunFactData1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRoundEndFunFactData2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRoundEndFunFactData3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_sRoundEndMessage", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_iRoundEndPlayerCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRoundEndNoMusic", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRoundEndLegacy", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundEndCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_iRoundStartRoundNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nRoundStartCount", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": "C_TeamplayRules"}, "C_CSGameRulesProxy": {"fields": {"m_pGameRules": 1384}, "metadata": [{"name": "m_pGameRules", "type": "NetworkVarNames", "type_name": "C_CSGameRules*"}], "parent": "C_GameRulesProxy"}, "C_CSMinimapBoundary": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_CSObserverPawn": {"fields": {"m_hDetectParentChange": 5392}, "metadata": [{"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}], "parent": "C_CSPlayerPawnBase"}, "C_CSPlayerPawn": {"fields": {"m_ArmorValue": 9244, "m_EconGloves": 5992, "m_GunGameImmunityColor": 8976, "m_PredictedDamageTags": 9400, "m_RetakesMVPBoostExtraUtility": 5952, "m_aimPunchAngle": 5508, "m_aimPunchAngleVel": 5520, "m_aimPunchCache": 5544, "m_aimPunchTickBase": 5532, "m_aimPunchTickFraction": 5536, "m_angShootAngleHistory": 9324, "m_angStashedShootAngles": 9288, "m_bGrenadeParametersStashed": 9284, "m_bHasDeathInfo": 9261, "m_bHasFemaleVoice": 5456, "m_bInBombZone": 5585, "m_bInBuyZone": 5504, "m_bInHostageRescueZone": 5584, "m_bInLanding": 5576, "m_bInNoDefuseArea": 9204, "m_bIsBuyMenuOpen": 5586, "m_bIsDefusing": 9194, "m_bIsGrabbingHostage": 9195, "m_bIsScoped": 9192, "m_bIsWalking": 9080, "m_bKilledByHeadshot": 9241, "m_bLastHeadBoneTransformIsValid": 8848, "m_bLeftHanded": 8897, "m_bMustSyncRagdollState": 7089, "m_bNeedToReApplyGloves": 5984, "m_bOldIsScoped": 9260, "m_bOnGroundLastTick": 8856, "m_bPrevDefuser": 5486, "m_bPrevHelmet": 5487, "m_bPreviouslyInBuyZone": 5505, "m_bRagdollDamageHeadshot": 7184, "m_bResumeZoom": 9193, "m_bRetakesHasDefuseKit": 5944, "m_bRetakesMVPLastRound": 5945, "m_bSkipOneHeadConstraintUpdate": 8896, "m_bWaitForNoAttack": 9232, "m_entitySpottedState": 9168, "m_fSwitchedHandednessTime": 8900, "m_flDeathInfoTime": 9264, "m_flEmitSoundTime": 9200, "m_flFlinchStack": 9216, "m_flHealthShotBoostExpirationTime": 5448, "m_flHitHeading": 9224, "m_flLandingStartTime": 5580, "m_flLandingTimeSeconds": 5460, "m_flLastFiredWeaponTime": 5452, "m_flNextSprayDecalTime": 5592, "m_flOldFallVelocity": 5464, "m_flSlopeDropHeight": 9128, "m_flSlopeDropOffset": 9112, "m_flTimeOfLastInjury": 5588, "m_flVelocityModifier": 9220, "m_flViewmodelFOV": 8916, "m_flViewmodelOffsetX": 8904, "m_flViewmodelOffsetY": 8908, "m_flViewmodelOffsetZ": 8912, "m_grenadeParameterStashTime": 9280, "m_iBlockingUseActionInProgress": 9196, "m_iRetakesMVPBoostItem": 5948, "m_iRetakesOffering": 5936, "m_iRetakesOfferingCard": 5940, "m_iShotsFired": 9212, "m_ignoreLadderJumpTime": 9236, "m_lastLandTime": 8852, "m_nEconGlovesChanged": 7088, "m_nHighestAppliedDamageTagTick": 9484, "m_nHitBodyPart": 9228, "m_nLastKillerIndex": 9256, "m_nPrevArmorVal": 5488, "m_nPrevGrenadeAmmoCount": 5492, "m_nPrevHighestReceivedDamageTagTick": 9480, "m_nRagdollDamageBone": 7092, "m_nWhichBombZone": 9208, "m_pActionTrackingServices": 5432, "m_pBulletServices": 5400, "m_pBuyServices": 5416, "m_pDamageReactServices": 5440, "m_pGlowServices": 5424, "m_pHostageServices": 5408, "m_qDeathEyeAngles": 8884, "m_szLastPlaceName": 5468, "m_szRagdollDamageWeaponName": 7120, "m_thirdPersonHeading": 9088, "m_unCurrentEquipmentValue": 9248, "m_unFreezetimeEndEquipmentValue": 9252, "m_unPreviousWeaponHash": 5496, "m_unRoundStartEquipmentValue": 9250, "m_unWeaponHash": 5500, "m_vHeadConstraintOffset": 9144, "m_vRagdollDamageForce": 7096, "m_vRagdollDamagePosition": 7108, "m_vRagdollServerOrigin": 7188, "m_vecBulletHitModels": 9056, "m_vecDeathInfoOrigin": 9268, "m_vecPlayerPatchEconIndices": 8920, "m_vecStashedGrenadeThrowPosition": 9300, "m_vecStashedVelocity": 9312, "m_vecThrowPositionHistory": 9348, "m_vecVelocityHistory": 9372}, "metadata": [{"name": "m_pBulletServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_BulletServices*"}, {"name": "m_pHostageServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_HostageServices*"}, {"name": "m_pBuyServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_BuyServices*"}, {"name": "m_pGlowServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_GlowServices*"}, {"name": "m_pActionTrackingServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_ActionTrackingServices*"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkVarTypeOverride", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_flHealthShotBoostExpirationTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bHasFemaleVoice", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szLastPlaceName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bInBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_aimPunchAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_aimPunchAngleVel", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_aimPunchTickBase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_aimPunchTickFraction", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInHostageRescueZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInBombZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsBuyMenuOpen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeOfLastInjury", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flNextSprayDecalTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iRetakesOffering", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iRetakesOfferingCard", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRetakesHasDefuseKit", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRetakesMVPLastRound", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iRetakesMVPBoostItem", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_RetakesMVPBoostExtraUtility", "type": "NetworkVarNames", "type_name": "loadout_slot_t"}, {"name": "m_EconGloves", "type": "NetworkVarNames", "type_name": "CEconItemView"}, {"name": "m_nEconGlovesChanged", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nRagdollDamageBone", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vRagdollDamageForce", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vRagdollDamagePosition", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_szRagdollDamageWeaponName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bRagdollDamageHeadshot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vRagdollServerOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "MNetworkReplayCompatField", "type": "Unknown"}, {"name": "m_q<PERSON>eathEyeAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_bLeftHanded", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fSwitchedHandednessTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flViewmodelOffsetX", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelOffsetY", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelOffsetZ", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flViewmodelFOV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vecPlayerPatchEconIndices", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_GunGameImmunityColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bIsWalking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_bIsScoped", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bResumeZoom", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsDefusing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsGrabbingHostage", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iBlockingUseActionInProgress", "type": "NetworkVarNames", "type_name": "CSPlayerBlockingUseAction_t"}, {"name": "m_flEmitSoundTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bInNoDefuseArea", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nWhichBombZone", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iShotsFired", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFlinchStack", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flVelocityModifier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHitHeading", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nHitBodyPart", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bWaitForNoAttack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bKilledByHeadshot", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_ArmorValue", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_unCurrentEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_unRoundStartEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_unFreezetimeEndEquipmentValue", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nLastKillerIndex", "type": "NetworkVarNames", "type_name": "CEntityIndex"}, {"name": "m_PredictedDamageTags", "type": "NetworkVarNames", "type_name": "PredictedDamageTag_t"}], "parent": "C_CSPlayerPawnBase"}, "C_CSPlayerPawnBase": {"fields": {"m_angEyeAngles": 5176, "m_bCachedPlaneIsValid": 5021, "m_bClipHitStaticWorld": 5020, "m_bDeferStartMusicOnWarmup": 5284, "m_bFlashBuildUp": 5124, "m_bFlashDspHasBeenCleared": 5125, "m_bFlashScreenshotHasBeenGrabbed": 5126, "m_bGuardianShouldSprayCustomXMark": 5376, "m_bGunGameImmunity": 5052, "m_bHasMovedSinceSpawn": 5053, "m_bIsRescuing": 5040, "m_bScreenTearFrameCaptured": 5108, "m_bShouldAutobuyDMWeapons": 5204, "m_bShouldAutobuyNow": 5205, "m_cycleLatch": 5288, "m_delayTargetIDTimer": 5216, "m_fImmuneToGunGameDamageTime": 5044, "m_fImmuneToGunGameDamageTimeLast": 5048, "m_fMolotovDamageTime": 5060, "m_fMolotovUseTime": 5056, "m_fNextThinkPushAway": 5200, "m_fRenderingClipPlane": 4976, "m_flClientDeathTime": 5104, "m_flCurrentMusicStartTime": 5276, "m_flDeathCCWeight": 5160, "m_flFlashBangTime": 5112, "m_flFlashDuration": 5132, "m_flFlashMaxAlpha": 5128, "m_flFlashOverlayAlpha": 5120, "m_flFlashScreenshotAlpha": 5116, "m_flHealthFadeAlpha": 5144, "m_flHealthFadeValue": 5140, "m_flLastSmokeAge": 5300, "m_flLastSmokeOverlayAlpha": 5296, "m_flLastSpawnTimeIndex": 5068, "m_flMusicRoundStartTime": 5280, "m_flNextMagDropTime": 5328, "m_flPrevMatchEndTime": 5168, "m_flPrevRoundEndTime": 5164, "m_flProgressBarStartTime": 5076, "m_hOriginalController": 5384, "m_holdTargetIDTimer": 5248, "m_iHealthBarRenderMaskIndex": 5136, "m_iIDEntIndex": 5208, "m_iOldIDEntIndex": 5244, "m_iPlayerState": 5036, "m_iProgressBarDuration": 5072, "m_iTargetItemEntIdx": 5240, "m_iThrowGrenadeCounter": 5064, "m_nLastClipPlaneSetupFrame": 4992, "m_nLastMagDropAttachmentIndex": 5332, "m_nPlayerInfernoBodyFx": 5320, "m_nPlayerInfernoFootFx": 5324, "m_nPlayerSmokedFx": 5316, "m_pClippingWeapon": 5024, "m_pPingServices": 4960, "m_pViewModelServices": 4968, "m_previousPlayerState": 5032, "m_serverIntendedCycle": 5292, "m_vLastSmokeOverlayColor": 5304, "m_vecIntroStartEyePosition": 5080, "m_vecIntroStartPlayerForward": 5092, "m_vecLastAliveLocalVelocity": 5336, "m_vecLastClipCameraForward": 5008, "m_vecLastClipCameraPos": 4996}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_pPingServices", "type": "NetworkVarNames", "type_name": "CCSPlayer_PingServices*"}, {"name": "m_pViewModelServices", "type": "NetworkVarNames", "type_name": "CPlayer_ViewModelServices*"}, {"name": "m_iPlayerState", "type": "NetworkVarNames", "type_name": "CSPlayerState"}, {"name": "m_bIsRescuing", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fImmuneToGunGameDamageTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bGunGameImmunity", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasMovedSinceSpawn", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fMolotovUseTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fMolotovDamageTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_iThrowGrenadeCounter", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iProgressBarDuration", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flProgressBarStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFlashMaxAlpha", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFlashDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_cycleLatch", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hOriginalController", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerController>"}], "parent": "C_BasePlayerPawn"}, "C_CSPlayerResource": {"fields": {"m_bEndMatchNextMapAllVoted": 1528, "m_bHostageAlive": 1384, "m_bombsiteCenterA": 1456, "m_bombsiteCenterB": 1468, "m_foundGoalPositions": 1529, "m_hostageRescueX": 1480, "m_hostageRescueY": 1496, "m_hostageRescueZ": 1512, "m_iHostageEntityIDs": 1408, "m_isHostageFollowingSomeone": 1396}, "metadata": [{"name": "m_bHostageAlive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_isHostageFollowingSomeone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iHostageEntityIDs", "type": "NetworkVarNames", "type_name": "CEntityIndex"}, {"name": "m_bombsiteCenterA", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bombsiteCenterB", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_hostageRescueX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hostageRescueY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hostageRescueZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bEndMatchNextMapAllVoted", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_CSTeam": {"fields": {"m_bSurrendered": 2084, "m_iClanID": 2232, "m_numMapVictories": 2080, "m_scoreFirstHalf": 2088, "m_scoreOvertime": 2096, "m_scoreSecondHalf": 2092, "m_szClanTeamname": 2100, "m_szTeamFlagImage": 2236, "m_szTeamLogoImage": 2244, "m_szTeamMatchStat": 1568}, "metadata": [{"name": "m_szTeamMatchStat", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_numMapVictories", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bSurrendered", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_scoreFirstHalf", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_scoreSecondHalf", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_scoreOvertime", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_szClanTeamname", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_iClanID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_szTeamFlagImage", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_szTeamLogoImage", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_Team"}, "C_CSWeaponBase": {"fields": {"m_ClientPreviousWeaponState": 5976, "m_IronSightController": 6624, "m_OnPlayerPickup": 6032, "m_bBurstMode": 6120, "m_bClearWeaponIdentifyingUGC": 6320, "m_bDroppedNearBuyZone": 6160, "m_bFireOnEmpty": 6024, "m_bInReload": 6136, "m_bIsHauledBack": 6144, "m_bOldFirstPersonSpectatedState": 6322, "m_bReloadVisuallyComplete": 6137, "m_bReloadsWithClips": 6016, "m_bSilencerOn": 6145, "m_bUIWeapon": 6323, "m_bVisualsDataSet": 6321, "m_bWasOwnedByCT": 6388, "m_bWasOwnedByTerrorist": 6389, "m_donated": 6380, "m_ePlayerFireEvent": 5864, "m_ePlayerFireEventAttackType": 5868, "m_fAccuracyPenalty": 6096, "m_fAccuracySmoothedForZoom": 6104, "m_fLastShotTime": 6384, "m_fScopeZoomEndTime": 6108, "m_flCrosshairDistance": 5984, "m_flDroppedAtTime": 6140, "m_flFireSequenceStartTime": 5852, "m_flGunAccuracyPositionDeprecated": 6004, "m_flLastAccuracyUpdateTime": 6100, "m_flLastBurstModeChangeTime": 6124, "m_flLastLOSTraceFailureTime": 6816, "m_flLastMagDropRequestTime": 6912, "m_flNextAttackRenderTimeOffset": 6164, "m_flNextClientFireBulletTime": 6404, "m_flNextClientFireBulletTime_Repredict": 6408, "m_flPostponeFireReadyFrac": 6132, "m_flRecoilIndex": 6116, "m_flTimeSilencerSwitchComplete": 6148, "m_flTimeWeaponIdle": 6020, "m_flTurningInaccuracy": 6092, "m_flTurningInaccuracyDelta": 6076, "m_flWatTickOffset": 6916, "m_gunHeat": 6392, "m_hCurrentThirdPersonSequence": 5912, "m_hPrevOwner": 6340, "m_iAlpha": 5992, "m_iAmmoLastCheck": 5988, "m_iCrosshairTextureID": 6000, "m_iIronSightMode": 6800, "m_iMostRecentTeamNumber": 6156, "m_iNumEmptyAttacks": 6820, "m_iOriginalTeamNumber": 6152, "m_iRecoilIndex": 6112, "m_iScopeTextureID": 5996, "m_iState": 5980, "m_lastSmokeTime": 6400, "m_nCustomEconReloadEventId": 6324, "m_nDropTick": 6344, "m_nFireSequenceStartTimeAck": 5860, "m_nFireSequenceStartTimeChange": 5856, "m_nLastEmptySoundCmdNum": 6008, "m_nPostponeFireReadyTicks": 6128, "m_nSilencerBoneIndex": 5916, "m_nViewModelIndex": 6012, "m_nextPrevOwnerUseTime": 6336, "m_seqFirePrimary": 5876, "m_seqFireSecondary": 5880, "m_seqIdle": 5872, "m_smokeAttachments": 6396, "m_thirdPersonFireSequences": 5888, "m_thirdPersonSequences": 5920, "m_vecTurningInaccuracyEyeDirLast": 6080, "m_weaponMode": 6072}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_flFireSequenceStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFireSequenceStartTimeChange", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_ePlayerFireEvent", "type": "NetworkVarNames", "type_name": "PlayerAnimEvent_t"}, {"name": "m_ePlayerFireEventAttackType", "type": "NetworkVarNames", "type_name": "WeaponAttackType_t"}, {"name": "m_iState", "type": "NetworkVarNames", "type_name": "CSWeaponState_t"}, {"name": "m_nViewModelIndex", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flTimeWeaponIdle", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_weaponMode", "type": "NetworkVarNames", "type_name": "CSWeaponMode"}, {"name": "m_fAccuracyPenalty", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_iRecoilIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flRecoilIndex", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bBurstMode", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nPostponeFireReadyTicks", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_flPostponeFireReadyFrac", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInReload", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bReloadVisuallyComplete", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDroppedAtTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsHauledBack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSilencerOn", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeSilencerSwitchComplete", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iOriginalTeamNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iMostRecentTeamNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bDroppedNearBuyZone", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nextPrevOwnerUseTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_hPrevOwner", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_nDropTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_fLastShotTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iIronSightMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNumEmptyAttacks", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BasePlayerWeapon"}, "C_CSWeaponBaseGun": {"fields": {"m_bNeedsBoltAction": 6973, "m_iBurstShotsRemaining": 6948, "m_iSilencerBodygroup": 6952, "m_inPrecache": 6972, "m_silencedModelIndex": 6968, "m_zoomLevel": 6944}, "metadata": [{"name": "m_zoomLevel", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iBurstShotsRemaining", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bNeedsBoltAction", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBase"}, "C_Chicken": {"fields": {"m_AttributeManager": 4672, "m_bAttributesInitialized": 5864, "m_bIsPreviewModel": 5872, "m_hHolidayHatAddon": 4656, "m_hWaterWakeParticles": 5868, "m_jumpedThisFrame": 4660, "m_leader": 4664}, "metadata": [{"name": "m_jumpedThis<PERSON><PERSON>e", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_leader", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}], "parent": "C_DynamicProp"}, "C_ClientRagdoll": {"fields": {"m_bFadeOut": 3976, "m_bFadingOut": 4006, "m_bImportant": 3977, "m_bReleaseRagdoll": 4004, "m_flEffectTime": 3980, "m_flScaleEnd": 4008, "m_flScaleTimeEnd": 4088, "m_flScaleTimeStart": 4048, "m_gibDespawnTime": 3984, "m_iCurrentFriction": 3988, "m_iEyeAttachment": 4005, "m_iFrictionAnimState": 4000, "m_iMaxFriction": 3996, "m_iMinFriction": 3992}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_ColorCorrection": {"fields": {"m_MaxFalloff": 1400, "m_MinFalloff": 1396, "m_bClientSide": 1934, "m_bEnabled": 1932, "m_bEnabledOnClient": 1936, "m_bExclusive": 1935, "m_bFadingIn": 1944, "m_bMaster": 1933, "m_flCurWeight": 1416, "m_flCurWeightOnClient": 1940, "m_flFadeDuration": 1956, "m_flFadeInDuration": 1404, "m_flFadeOutDuration": 1408, "m_flFadeStartTime": 1952, "m_flFadeStartWeight": 1948, "m_flMaxWeight": 1412, "m_netlookupFilename": 1420, "m_vecOrigin": 1384}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_<PERSON><PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeInDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMaxWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flCurWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_netlookupFilename", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bClientSide", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExclusive", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_ColorCorrectionVolume": {"fields": {"m_FadeDuration": 3400, "m_LastEnterTime": 3380, "m_LastEnterWeight": 3376, "m_LastExitTime": 3388, "m_LastExitWeight": 3384, "m_MaxWeight": 3396, "m_Weight": 3404, "m_bEnabled": 3392, "m_lookupFilename": 3408}, "metadata": [{"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_MaxWeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_FadeDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Weight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_lookupFilename", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseTrigger"}, "C_CsmFovOverride": {"fields": {"m_cameraName": 1384, "m_flCsmFovOverrideValue": 1392}, "metadata": [], "parent": "C_BaseEntity"}, "C_DEagle": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_DecoyGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_DecoyProjectile": {"fields": {"m_flTimeParticleEffectSpawn": 4656, "m_nClientLastKnownDecoyShotTick": 4620, "m_nDecoyShotTick": 4616}, "metadata": [{"name": "m_nDecoyShotTick", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_DynamicLight": {"fields": {"m_Exponent": 3376, "m_Flags": 3368, "m_InnerAngle": 3380, "m_LightStyle": 3369, "m_OuterAngle": 3384, "m_Radius": 3372, "m_SpotRadius": 3388}, "metadata": [{"name": "m_Flags", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_LightStyle", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_Exponent", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_InnerAngle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_OuterAngle", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_SpotRadius", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_DynamicProp": {"fields": {"m_OnAnimReachedEnd": 4536, "m_OnAnimReachedStart": 4496, "m_bCreateNonSolid": 4592, "m_bFiredStartEndOutput": 4590, "m_bForceNpcExclude": 4591, "m_bIsOverrideProp": 4593, "m_bRandomizeCycle": 4588, "m_bStartDisabled": 4589, "m_bUseAnimGraph": 4369, "m_bUseHitboxesForRenderBox": 4368, "m_glowColor": 4608, "m_iCachedFrameCount": 4616, "m_iInitialGlowState": 4596, "m_iszIdleAnim": 4576, "m_nGlowRange": 4600, "m_nGlowRangeMin": 4604, "m_nGlowTeam": 4612, "m_nIdleAnimLoopMode": 4584, "m_pOutputAnimBegun": 4376, "m_pOutputAnimLoopCycleOver": 4456, "m_pOutputAnimOver": 4416, "m_vecCachedRenderMaxs": 4632, "m_vecCachedRenderMins": 4620}, "metadata": [{"name": "m_bUseHitboxesForRenderBox", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseAnimGraph", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BreakableProp"}, "C_DynamicPropAlias_cable_dynamic": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_DynamicPropAlias_dynamic_prop": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_DynamicPropAlias_prop_dynamic_override": {"fields": {}, "metadata": [], "parent": "C_DynamicProp"}, "C_EconEntity": {"fields": {"m_AttributeManager": 4424, "m_OriginalOwnerXuidHigh": 5620, "m_OriginalOwnerXuidLow": 5616, "m_bAttachmentDirty": 5680, "m_bAttributesInitialized": 4416, "m_bClientside": 5640, "m_bParticleSystemsCreated": 5641, "m_flFallbackWear": 5632, "m_flFlexDelayTime": 4400, "m_flFlexDelayedWeight": 4408, "m_hOldProvidee": 5704, "m_hViewmodelAttachment": 5672, "m_iNumOwnerValidationRetries": 5688, "m_iOldTeam": 5676, "m_nFallbackPaintKit": 5624, "m_nFallbackSeed": 5628, "m_nFallbackStatTrak": 5636, "m_nUnloadedModelIndex": 5684, "m_vecAttachedModels": 5712, "m_vecAttachedParticles": 5648}, "metadata": [{"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}, {"name": "m_OriginalOwnerXuidLow", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_OriginalOwnerXuidHigh", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nFallbackPaintKit", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nFallbackSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFallbackWear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFallbackStatTrak", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseFlex"}, "C_EconEntity__AttachedModelData_t": {"fields": {"m_iModelDisplayFlags": 0}, "metadata": [], "parent": null}, "C_EconItemView": {"fields": {"m_AttributeList": 528, "m_NetworkedDynamicAttributes": 624, "m_bDisallowSOC": 489, "m_bInitialized": 488, "m_bInitializedTags": 1088, "m_bInventoryImageRgbaRequested": 96, "m_bInventoryImageTriedCache": 97, "m_bIsStoreItem": 490, "m_bIsTradeItem": 491, "m_bRestoreCustomMaterialAfterPrecache": 440, "m_iAccountID": 472, "m_iEntityLevel": 448, "m_iEntityQuality": 444, "m_iEntityQuantity": 492, "m_iInventoryPosition": 476, "m_iItemDefinitionIndex": 442, "m_iItemID": 456, "m_iItemIDHigh": 464, "m_iItemIDLow": 468, "m_iOriginOverride": 504, "m_iQualityOverride": 500, "m_iRarityOverride": 496, "m_nInventoryImageRgbaHeight": 132, "m_nInventoryImageRgbaWidth": 128, "m_szCurrentLoadCachedFileName": 136, "m_szCustomName": 720, "m_szCustomNameOverride": 881, "m_unClientFlags": 508, "m_unOverrideStyle": 509}, "metadata": [{"name": "m_iItemDefinitionIndex", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_iEntityQuality", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEntityLevel", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iItemIDHigh", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iItemIDLow", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iAccountID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iInventoryPosition", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bInitialized", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_AttributeList", "type": "NetworkVarNames", "type_name": "CAttributeList"}, {"name": "m_NetworkedDynamicAttributes", "type": "NetworkVarNames", "type_name": "CAttributeList"}, {"name": "m_szCustomName", "type": "NetworkVarNames", "type_name": "char"}], "parent": "IEconItemInterface"}, "C_EconWearable": {"fields": {"m_bAlwaysAllow": 5740, "m_nForceSkin": 5736}, "metadata": [], "parent": "C_EconEntity"}, "C_EntityDissolve": {"fields": {"m_bCoreExplode": 3428, "m_bLinkedToServerEnt": 3429, "m_flFadeInLength": 3384, "m_flFadeInStart": 3380, "m_flFadeOutLength": 3400, "m_flFadeOutModelLength": 3392, "m_flFadeOutModelStart": 3388, "m_flFadeOutStart": 3396, "m_flNextSparkTime": 3404, "m_flStartTime": 3376, "m_nDissolveType": 3408, "m_nMagnitude": 3424, "m_vDissolverOrigin": 3412}, "metadata": [{"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flFadeInStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeInLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutModelStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutModelLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutStart", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeOutLength", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nDissolveType", "type": "NetworkVarNames", "type_name": "EntityDisolveType_t"}, {"name": "m_vDissolverOrigin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nMagnitude", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": "C_BaseModelEntity"}, "C_EntityFlame": {"fields": {"m_bCheapEffect": 1428, "m_hEntAttached": 1384, "m_hOldAttached": 1424}, "metadata": [{"name": "m_hEntAttached", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_bCheapEffect", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCombinedLightProbeVolume": {"fields": {"m_Entity_Color": 5576, "m_Entity_bCustomCubemapTexture": 5592, "m_Entity_bEnabled": 5737, "m_Entity_bMoveable": 5656, "m_Entity_bStartDisabled": 5672, "m_Entity_flBrightness": 5580, "m_Entity_flEdgeFadeDist": 5676, "m_Entity_hCubemapTexture": 5584, "m_Entity_hLightProbeDirectLightIndicesTexture": 5608, "m_Entity_hLightProbeDirectLightScalarsTexture": 5616, "m_Entity_hLightProbeDirectLightShadowsTexture": 5624, "m_Entity_hLightProbeTexture": 5600, "m_Entity_nEnvCubeMapArrayIndex": 5664, "m_Entity_nHandshake": 5660, "m_Entity_nLightProbeAtlasX": 5704, "m_Entity_nLightProbeAtlasY": 5708, "m_Entity_nLightProbeAtlasZ": 5712, "m_Entity_nLightProbeSizeX": 5692, "m_Entity_nLightProbeSizeY": 5696, "m_Entity_nLightProbeSizeZ": 5700, "m_Entity_nPriority": 5668, "m_Entity_vBoxMaxs": 5644, "m_Entity_vBoxMins": 5632, "m_Entity_vEdgeFadeDists": 5680}, "metadata": [{"name": "m_Entity_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_Entity_flBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_hCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_bCustomCubemapTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_hLightProbeTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightIndicesTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightScalarsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightShadowsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nEnvCubeMapArrayIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_flEdgeFadeDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vEdgeFadeDists", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_nLightProbeSizeX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCubemap": {"fields": {"m_Entity_bCopyDiffuseFromDefaultCubemap": 1592, "m_Entity_bCustomCubemapTexture": 1520, "m_Entity_bDefaultEnvMap": 1589, "m_Entity_bDefaultSpecEnvMap": 1590, "m_Entity_bEnabled": 1608, "m_Entity_bIndoorCubeMap": 1591, "m_Entity_bMoveable": 1552, "m_Entity_bStartDisabled": 1588, "m_Entity_flDiffuseScale": 1584, "m_Entity_flEdgeFadeDist": 1568, "m_Entity_flInfluenceRadius": 1524, "m_Entity_hCubemapTexture": 1512, "m_Entity_nEnvCubeMapArrayIndex": 1560, "m_Entity_nHandshake": 1556, "m_Entity_nPriority": 1564, "m_Entity_vBoxProjectMaxs": 1540, "m_Entity_vBoxProjectMins": 1528, "m_Entity_vEdgeFadeDists": 1572}, "metadata": [{"name": "m_Entity_hCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_bCustomCubemapTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_flInfluenceRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vBoxProjectMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxProjectMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nEnvCubeMapArrayIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_flEdgeFadeDist", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_vEdgeFadeDists", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_flDiffuseScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bDefaultEnvMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bDefaultSpecEnvMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bIndoorCubeMap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bCopyDiffuseFromDefaultCubemap", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvCubemapBox": {"fields": {}, "metadata": [], "parent": "C_EnvCubemap"}, "C_EnvCubemapFog": {"fields": {"m_bActive": 1420, "m_bFirstTime": 1457, "m_bHasHeightFogEnd": 1456, "m_bHeightFogEnabled": 1396, "m_bStartDisabled": 1421, "m_flEndDistance": 1384, "m_flFogFalloffExponent": 1392, "m_flFogHeightEnd": 1404, "m_flFogHeightExponent": 1412, "m_flFogHeightStart": 1408, "m_flFogHeightWidth": 1400, "m_flFogMaxOpacity": 1424, "m_flLODBias": 1416, "m_flStartDistance": 1388, "m_hFogCubemapTexture": 1448, "m_hSkyMaterial": 1432, "m_iszSkyEntity": 1440, "m_nCubemapSourceType": 1428}, "metadata": [{"name": "m_flEndDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHeightFogEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogHeightWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogHeightExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flLODBias", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogMaxOpacity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nCubemapSourceType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_hSkyMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_iszSkyEntity", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_hFogCubemapTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_bHasHeightFogEnd", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvDecal": {"fields": {"m_bProjectOnCharacters": 3393, "m_bProjectOnWater": 3394, "m_bProjectOnWorld": 3392, "m_flDepth": 3384, "m_flDepthSortBias": 3396, "m_flHeight": 3380, "m_flWidth": 3376, "m_hDecalMaterial": 3368, "m_nRenderOrder": 3388}, "metadata": [{"name": "m_hDecalMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRenderOrder", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bProjectOnWorld", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bProjectOnCharacters", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bProjectOnWater", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDepthSortBias", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseModelEntity"}, "C_EnvDetailController": {"fields": {"m_flFadeEndDist": 1388, "m_flFadeStartDist": 1384}, "metadata": [{"name": "m_flFadeStartDist", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFadeEndDist", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseEntity"}, "C_EnvLightProbeVolume": {"fields": {"m_Entity_bEnabled": 5553, "m_Entity_bMoveable": 5504, "m_Entity_bStartDisabled": 5516, "m_Entity_hLightProbeDirectLightIndicesTexture": 5456, "m_Entity_hLightProbeDirectLightScalarsTexture": 5464, "m_Entity_hLightProbeDirectLightShadowsTexture": 5472, "m_Entity_hLightProbeTexture": 5448, "m_Entity_nHandshake": 5508, "m_Entity_nLightProbeAtlasX": 5532, "m_Entity_nLightProbeAtlasY": 5536, "m_Entity_nLightProbeAtlasZ": 5540, "m_Entity_nLightProbeSizeX": 5520, "m_Entity_nLightProbeSizeY": 5524, "m_Entity_nLightProbeSizeZ": 5528, "m_Entity_nPriority": 5512, "m_Entity_vBoxMaxs": 5492, "m_Entity_vBoxMins": 5480}, "metadata": [{"name": "m_Entity_hLightProbeTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightIndicesTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightScalarsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_hLightProbeDirectLightShadowsTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_Entity_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_Entity_bMoveable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nHandshake", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nPriority", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_Entity_nLightProbeSizeX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeSizeZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_nLightProbeAtlasZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_Entity_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvParticleGlow": {"fields": {"m_ColorTint": 4836, "m_flAlphaScale": 4824, "m_flRadiusScale": 4828, "m_flSelfIllumScale": 4832, "m_hTextureOverride": 4840}, "metadata": [{"name": "m_flAlphaScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRadiusScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSelfIllumScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_ColorTint", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_hTextureOverride", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}], "parent": "C_ParticleSystem"}, "C_EnvProjectedTexture": {"fields": {}, "metadata": [], "parent": "C_ModelPointEntity"}, "C_EnvScreenOverlay": {"fields": {"m_bIsActive": 1512, "m_bWasActive": 1513, "m_flCurrentOverlayTime": 1524, "m_flOverlayTimes": 1464, "m_flStartTime": 1504, "m_iCachedDesiredOverlay": 1516, "m_iCurrentOverlay": 1520, "m_iDesiredOverlay": 1508, "m_iszOverlayNames": 1384}, "metadata": [{"name": "m_iszOverlayNames", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_flOverlayTimes", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iDesiredOverlay", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_bIsActive", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_PointEntity"}, "C_EnvSky": {"fields": {"m_bEnabled": 3420, "m_bStartDisabled": 3384, "m_flBrightnessScale": 3396, "m_flFogMaxEnd": 3416, "m_flFogMaxStart": 3412, "m_flFogMinEnd": 3408, "m_flFogMinStart": 3404, "m_hSkyMaterial": 3368, "m_hSkyMaterialLightingOnly": 3376, "m_nFogType": 3400, "m_vTintColor": 3385, "m_vTintColorLightingOnly": 3389}, "metadata": [{"name": "m_hSkyMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hSkyMaterialLightingOnly", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vTintColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_vTintColorLightingOnly", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flBrightnessScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFogType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFogMinStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMinEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_EnvVolumetricFogController": {"fields": {"m_bActive": 1456, "m_bEnableIndirect": 1497, "m_bFirstTime": 1536, "m_bIndirectUseLPVs": 1498, "m_bIsMaster": 1499, "m_bStartDisabled": 1496, "m_fFirstVolumeSliceThickness": 1416, "m_fNoiseSpeed": 1516, "m_fNoiseStrength": 1520, "m_flAnisotropy": 1388, "m_flDefaultAnisotropy": 1484, "m_flDefaultDrawDistance": 1492, "m_flDefaultScattering": 1488, "m_flDrawDistance": 1396, "m_flFadeInEnd": 1404, "m_flFadeInStart": 1400, "m_flFadeSpeed": 1392, "m_flIndirectStrength": 1408, "m_flScattering": 1384, "m_flStartAnisoTime": 1460, "m_flStartAnisotropy": 1472, "m_flStartDrawDistance": 1480, "m_flStartDrawDistanceTime": 1468, "m_flStartScatterTime": 1464, "m_flStartScattering": 1476, "m_hFogIndirectTexture": 1504, "m_nForceRefreshCount": 1512, "m_nIndirectTextureDimX": 1420, "m_nIndirectTextureDimY": 1424, "m_nIndirectTextureDimZ": 1428, "m_nVolumeDepth": 1412, "m_vBoxMaxs": 1444, "m_vBoxMins": 1432, "m_vNoiseScale": 1524}, "metadata": [{"name": "m_flScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeInStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeInEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flIndirectStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nVolumeDepth", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fFirstVolumeSliceThickness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nIndirectTextureDimX", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectTextureDimY", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nIndirectTextureDimZ", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStartAnisoTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartScatterTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartDrawDistanceTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultAnisotropy", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultScattering", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefaultDrawDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bEnableIndirect", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIndirectUseLPVs", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hFogIndirectTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_nForceRefreshCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_fNoiseSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fNoiseStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vNoiseScale", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseEntity"}, "C_EnvVolumetricFogVolume": {"fields": {"m_bActive": 1384, "m_bAllowLPVIndirect": 1451, "m_bOverrideIndirectLightStrength": 1448, "m_bOverrideNoiseStrength": 1450, "m_bOverrideSunLightStrength": 1449, "m_bStartDisabled": 1412, "m_fHeightFogEdgeWidth": 1432, "m_fIndirectLightStrength": 1436, "m_fNoiseStrength": 1444, "m_fSunLightStrength": 1440, "m_flFalloffExponent": 1424, "m_flHeightFogDepth": 1428, "m_flStrength": 1416, "m_nFalloffShape": 1420, "m_vBoxMaxs": 1400, "m_vBoxMins": 1388}, "metadata": [{"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vBoxMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vBoxMaxs", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nFalloffShape", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeightFogDepth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fHeightFogEdgeWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fIndirectLightStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fSunLightStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fNoiseStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bOverrideIndirectLightStrength", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOverrideSunLightStrength", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOverrideNoiseStrength", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAllowLPVIndirect", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_EnvWind": {"fields": {"m_EnvWindShared": 1384}, "metadata": [{"name": "m_EnvWindShared", "type": "NetworkVarNames", "type_name": "CEnvWindShared"}], "parent": "C_BaseEntity"}, "C_EnvWindClientside": {"fields": {"m_EnvWindShared": 1384}, "metadata": [{"name": "m_EnvWindShared", "type": "NetworkVarNames", "type_name": "CEnvWindShared"}], "parent": "C_BaseEntity"}, "C_EnvWindShared": {"fields": {"m_CurrentSwayVector": 80, "m_PrevSwayVector": 92, "m_bGusting": 132, "m_currentWindVector": 68, "m_flAveWindSpeed": 128, "m_flGustDuration": 36, "m_flInitialWindSpeed": 108, "m_flMaxGustDelay": 32, "m_flMinGustDelay": 28, "m_flSimTime": 120, "m_flStartTime": 8, "m_flSwayTime": 116, "m_flSwitchTime": 124, "m_flVariationTime": 112, "m_flWindAngleVariation": 136, "m_flWindSpeed": 64, "m_flWindSpeedVariation": 140, "m_hEntOwner": 144, "m_iGustDirChange": 40, "m_iInitialWindDir": 104, "m_iMaxGust": 26, "m_iMaxWind": 18, "m_iMinGust": 24, "m_iMinWind": 16, "m_iWindDir": 60, "m_iWindSeed": 12, "m_iszGustSound": 56, "m_location": 44, "m_windRadius": 20}, "metadata": [{"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_iWindSeed", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_iMinWind", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iMaxWind", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_windRadius", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_iMinGust", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iMaxGust", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_flMinGustDelay", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flMaxGustDelay", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flGustDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iGustDirChange", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_location", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_iInitialWindDir", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_flInitialWindSpeed", "type": "NetworkVarNames", "type_name": "float32"}], "parent": null}, "C_EnvWindShared__WindAveEvent_t": {"fields": {"m_flAveWindSpeed": 4, "m_flStartWindSpeed": 0}, "metadata": [], "parent": null}, "C_EnvWindShared__WindVariationEvent_t": {"fields": {"m_flWindAngleVariation": 0, "m_flWindSpeedVariation": 4}, "metadata": [], "parent": null}, "C_FireCrackerBlast": {"fields": {}, "metadata": [], "parent": "C_Inferno"}, "C_FireFromAboveSprite": {"fields": {}, "metadata": [], "parent": "C_Sprite"}, "C_FireSmoke": {"fields": {"m_bClipTested": 1452, "m_bFadingOut": 1453, "m_flChildFlameSpread": 1428, "m_flClipPerc": 1448, "m_flScaleEnd": 1416, "m_flScaleRegister": 1408, "m_flScaleStart": 1412, "m_flScaleTimeEnd": 1424, "m_flScaleTimeStart": 1420, "m_nFlameFromAboveModelIndex": 1404, "m_nFlameModelIndex": 1400, "m_pFireOverlay": 1464, "m_tParticleSpawn": 1456}, "metadata": [{"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_nFlameModelIndex", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_nFlameFromAboveModelIndex", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "C_BaseFire"}, "C_FireSprite": {"fields": {"m_bFadeFromAbove": 3652, "m_vecMoveDir": 3640}, "metadata": [], "parent": "C_Sprite"}, "C_Fish": {"fields": {"m_actualAngles": 4076, "m_actualPos": 4064, "m_angle": 4120, "m_angles": 4000, "m_averageError": 4212, "m_buoyancy": 4024, "m_deathAngle": 4020, "m_deathDepth": 4016, "m_errorHistory": 4124, "m_errorHistoryCount": 4208, "m_errorHistoryIndex": 4204, "m_gotUpdate": 4104, "m_localLifeState": 4012, "m_poolOrigin": 4088, "m_pos": 3976, "m_vel": 3988, "m_waterLevel": 4100, "m_wigglePhase": 4056, "m_wiggleRate": 4060, "m_wiggleTimer": 4032, "m_x": 4108, "m_y": 4112, "m_z": 4116}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_pool<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_waterLevel", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_x", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_y", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_z", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_angle", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "CBaseAnimGraph"}, "C_Flashbang": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_FlashbangProjectile": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenadeProjectile"}, "C_FogController": {"fields": {"m_bUseAngles": 1488, "m_fog": 1384, "m_iChangedVariables": 1492}, "metadata": [{"name": "m_fog", "type": "NetworkVarNames", "type_name": "fogparams_t"}], "parent": "C_BaseEntity"}, "C_FootstepControl": {"fields": {"m_destination": 3384, "m_source": 3376}, "metadata": [{"name": "m_source", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_destination", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseTrigger"}, "C_FuncBrush": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_FuncConveyor": {"fields": {"m_flCurrentConveyorOffset": 3432, "m_flCurrentConveyorSpeed": 3436, "m_flTargetSpeed": 3388, "m_flTransitionStartSpeed": 3400, "m_hConveyorModels": 3408, "m_nTransitionDurationTicks": 3396, "m_nTransitionStartTick": 3392, "m_vecMoveDirEntitySpace": 3376}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_vecMoveDirEntitySpace", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flTargetSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nTransitionStartTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "m_nTransitionDurationTicks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flTransitionStartSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hConveyorModels", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "C_BaseModelEntity"}, "C_FuncElectrifiedVolume": {"fields": {"m_EffectName": 3376, "m_bState": 3384, "m_nAmbientEffect": 3368}, "metadata": [{"name": "m_EffectName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bState", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_FuncBrush"}, "C_FuncLadder": {"fields": {"m_Dismounts": 3384, "m_bDisabled": 3448, "m_bFakeLadder": 3449, "m_bHasSlack": 3450, "m_flAutoRideSpeed": 3444, "m_vecLadderDir": 3368, "m_vecLocalTop": 3408, "m_vecPlayerMountPositionBottom": 3432, "m_vecPlayerMountPositionTop": 3420}, "metadata": [{"name": "m_vecLadderDir", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecPlayerMountPositionTop", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecPlayerMountPositionBottom", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flAutoRideSpeed", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_b<PERSON>ake<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_FuncMonitor": {"fields": {"m_bDraw3DSkybox": 3397, "m_bEnabled": 3396, "m_bRenderShadows": 3380, "m_bUseUniqueColorTarget": 3381, "m_brushModelName": 3384, "m_hTargetCamera": 3392, "m_nResolutionEnum": 3376, "m_targetCamera": 3368}, "metadata": [{"name": "m_targetCamera", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_nResolutionEnum", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bRenderShadows", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseUniqueColorTarget", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_brushModelName", "type": "NetworkVarNames", "type_name": "CUtlString"}, {"name": "m_hTargetCamera", "type": "NetworkVarNames", "type_name": "EHANDLE"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDraw3DSkybox", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_FuncBrush"}, "C_FuncMoveLinear": {"fields": {}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}], "parent": "C_BaseToggle"}, "C_FuncMover": {"fields": {}, "metadata": [], "parent": "C_BaseToggle"}, "C_FuncRotating": {"fields": {}, "metadata": [{"name": "MNetworkOverride", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}], "parent": "C_BaseModelEntity"}, "C_FuncTrackTrain": {"fields": {"m_flLineLength": 3376, "m_flRadius": 3372, "m_nLongAxis": 3368}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_GameRules": {"fields": {"__m_pChainEntity": 8, "m_bGamePaused": 56, "m_nPauseStartTick": 52, "m_nTotalPausedTicks": 48}, "metadata": [{"name": "m_nTotalPausedTicks", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPauseStartTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bGamePaused", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "C_GameRulesProxy": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_GlobalLight": {"fields": {"m_WindClothForceHandle": 2608}, "metadata": [], "parent": "C_BaseEntity"}, "C_GradientFog": {"fields": {"m_bGradientFogNeedsTextures": 1442, "m_bHeightFogEnabled": 1400, "m_bIsEnabled": 1441, "m_bStartDisabled": 1440, "m_flFadeTime": 1436, "m_flFarZ": 1412, "m_flFogEndDistance": 1396, "m_flFogEndHeight": 1408, "m_flFogFalloffExponent": 1420, "m_flFogMaxOpacity": 1416, "m_flFogStartDistance": 1392, "m_flFogStartHeight": 1404, "m_flFogStrength": 1432, "m_flFogVerticalExponent": 1424, "m_fogColor": 1428, "m_hGradientFogTexture": 1384}, "metadata": [{"name": "m_hGradientFogTexture", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_flFogStartDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEndDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHeightFogEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFogStartHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEndHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFarZ", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxOpacity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogFalloffExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogVerticalExponent", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_fogColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flFogStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_HEGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_HEGrenadeProjectile": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenadeProjectile"}, "C_HandleTest": {"fields": {"m_Handle": 1384, "m_bSendHandle": 1388}, "metadata": [{"name": "m_<PERSON>le", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_bSend<PERSON>andle", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_Hostage": {"fields": {"m_bHandsHaveBeenCut": 4596, "m_blinkTimer": 4640, "m_chestAttachment": 4706, "m_entitySpottedState": 4520, "m_eyeAttachment": 4705, "m_fLastGrabTime": 4604, "m_fNewestAlphaThinkTime": 4720, "m_flDeadOrRescuedTime": 4632, "m_flDropStartTime": 4628, "m_flGrabSuccessTime": 4624, "m_flRescueStartTime": 4620, "m_hHostageGrabber": 4600, "m_isInit": 4704, "m_isRescued": 4588, "m_jumpedThisFrame": 4589, "m_leader": 4544, "m_lookAroundTimer": 4680, "m_lookAt": 4664, "m_nHostageState": 4592, "m_pPredictionOwner": 4712, "m_reuseTimer": 4552, "m_vecGrabbedPos": 4608, "m_vel": 4576}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkOverride", "type": "Unknown"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_leader", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_reuseTimer", "type": "NetworkVarNames", "type_name": "CountdownTimer"}, {"name": "m_vel", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_isRescued", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_jumpedThis<PERSON><PERSON>e", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nHostageState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bHandsHaveBeenCut", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hHostageGrabber", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_flRescueStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flGrabSuccessTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flDropStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}], "parent": "C_BaseCombatCharacter"}, "C_HostageCarriableProp": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_IncendiaryGrenade": {"fields": {}, "metadata": [], "parent": "C_MolotovGrenade"}, "C_Inferno": {"fields": {"m_BurnNormal": 5080, "m_bFireIsBurning": 5016, "m_bInPostEffectTime": 5860, "m_blosCheck": 33524, "m_drawableCount": 33520, "m_fireCount": 5848, "m_fireParentPositions": 4248, "m_firePositions": 3480, "m_flLastGrassBurnThink": 33564, "m_hInfernoClimbingOutlinePointsSnapshot": 3464, "m_hInfernoDecalsSnapshot": 3472, "m_hInfernoFillerPointsSnapshot": 3448, "m_hInfernoOutlinePointsSnapshot": 3456, "m_hInfernoPointsSnapshot": 3440, "m_lastFireCount": 5864, "m_maxBounds": 33552, "m_maxFireHalfWidth": 33532, "m_maxFireHeight": 33536, "m_minBounds": 33540, "m_nFireEffectTickBegin": 5868, "m_nFireLifetime": 5856, "m_nInfernoType": 5852, "m_nfxFireDamageEffect": 3432, "m_nlosperiod": 33528}, "metadata": [{"name": "m_firePositions", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fireParentPositions", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bFireIsBurning", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_BurnNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_fireCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nInfernoType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nFireLifetime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bInPostEffectTime", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nFireEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseModelEntity"}, "C_InfoInstructorHintHostageRescueZone": {"fields": {}, "metadata": [], "parent": "C_PointEntity"}, "C_InfoLadderDismount": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_InfoVisibilityBox": {"fields": {"m_bEnabled": 1404, "m_nMode": 1388, "m_vBoxSize": 1392}, "metadata": [{"name": "m_nMode", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vBoxSize", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_Item": {"fields": {"m_pReticleHintTextName": 5736}, "metadata": [], "parent": "C_EconEntity"}, "C_ItemDogtags": {"fields": {"m_KillingPlayer": 5996, "m_OwningPlayer": 5992}, "metadata": [{"name": "m_OwningPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_KillingPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}], "parent": "C_Item"}, "C_Item_Healthshot": {"fields": {}, "metadata": [], "parent": "C_WeaponBaseItem"}, "C_KeychainModule": {"fields": {"m_nKeychainDefID": 3984, "m_nKeychainSeed": 3988}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_Knife": {"fields": {"m_bFirstAttack": 6944}, "metadata": [{"name": "m_bFirstAttack", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBase"}, "C_LightDirectionalEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LightEntity": {"fields": {"m_CLightComponent": 3368}, "metadata": [{"name": "m_CLightComponent", "type": "NetworkVarNames", "type_name": "CLightComponent::Storage_t"}], "parent": "C_BaseModelEntity"}, "C_LightEnvironmentEntity": {"fields": {}, "metadata": [], "parent": "C_LightDirectionalEntity"}, "C_LightGlow": {"fields": {"m_GlowOverlay": 3400, "m_flGlowProxySize": 3388, "m_flHDRColorScale": 3392, "m_nHorizontalSize": 3368, "m_nMaxDist": 3380, "m_nMinDist": 3376, "m_nOuterMaxDist": 3384, "m_nVerticalSize": 3372}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_nHorizontalSize", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nVerticalSize", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nMinDist", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nMaxDist", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nOuterMaxDist", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flGlowProxySize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_LightOrthoEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LightSpotEntity": {"fields": {}, "metadata": [], "parent": "C_LightEntity"}, "C_LocalTempEntity": {"fields": {"bounceFactor": 4000, "die": 3980, "fadeSpeed": 3996, "flags": 3976, "hitSound": 4004, "m_bParticleCollision": 4088, "m_flFrame": 4064, "m_flFrameMax": 3984, "m_flFrameRate": 4060, "m_flSpriteScale": 4052, "m_iLastCollisionFrame": 4092, "m_nFlickerFrame": 4056, "m_pszImpactEffect": 4072, "m_pszParticleEffect": 4080, "m_vLastCollisionOrigin": 4096, "m_vecNormal": 4040, "m_vecPrevAbsOrigin": 4120, "m_vecTempEntAcceleration": 4132, "m_vecTempEntAngVelocity": 4024, "m_vecTempEntVelocity": 4108, "priority": 4008, "tempent_renderamt": 4036, "tentOffset": 4012, "x": 3988, "y": 3992}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_MapPreviewParticleSystem": {"fields": {}, "metadata": [], "parent": "C_ParticleSystem"}, "C_MapVetoPickController": {"fields": {"m_bDisabledHud": 3756, "m_nAccountIDs": 1692, "m_nCurrentPhase": 3740, "m_nDraftType": 1400, "m_nMapId0": 1948, "m_nMapId1": 2204, "m_nMapId2": 2460, "m_nMapId3": 2716, "m_nMapId4": 2972, "m_nMapId5": 3228, "m_nPhaseDurationTicks": 3748, "m_nPhaseStartTick": 3744, "m_nPostDataUpdateTick": 3752, "m_nStartingSide0": 3484, "m_nTeamWinningCoinToss": 1404, "m_nTeamWithFirstChoice": 1408, "m_nVoteMapIdsList": 1664}, "metadata": [{"name": "m_nDraftType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTeamWinningCoinToss", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTeamWithFirstChoice", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoteMapIdsList", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nAccountIDs", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId1", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId2", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId3", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId4", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nMapId5", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nStartingSide0", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nCurrentPhase", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPhaseStartTick", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPhaseDurationTicks", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseEntity"}, "C_ModelPointEntity": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_MolotovGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_MolotovProjectile": {"fields": {"m_bIsIncGrenade": 4616}, "metadata": [{"name": "m_bIsIncGrenade", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_Multimeter": {"fields": {"m_hTargetC4": 3984}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_MultiplayRules": {"fields": {}, "metadata": [], "parent": "C_GameRules"}, "C_NametagModule": {"fields": {"m_strNametagString": 3984}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_NetTestBaseCombatCharacter": {"fields": {}, "metadata": [], "parent": "C_BaseCombatCharacter"}, "C_OmniLight": {"fields": {"m_bShowLight": 4216, "m_flInnerAngle": 4208, "m_flOuterAngle": 4212}, "metadata": [{"name": "m_flInnerAngle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flOuterAngle", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bShowLight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BarnLight"}, "C_ParticleSystem": {"fields": {"m_bActive": 3880, "m_bAnimateDuringGameplayPause": 3892, "m_bFrozen": 3881, "m_bNoFreeze": 4221, "m_bNoRamp": 4222, "m_bNoSave": 4220, "m_bOldActive": 4800, "m_bOldFrozen": 4801, "m_bStartActive": 4223, "m_clrTint": 4764, "m_flFreezeTransitionDuration": 3884, "m_flPreSimTime": 3908, "m_flStartTime": 3904, "m_hControlPointEnts": 3964, "m_iEffectIndex": 3896, "m_iServerControlPointAssignments": 3960, "m_iszControlPointNames": 4232, "m_iszEffectName": 4224, "m_nDataCP": 4744, "m_nStopType": 3888, "m_nTintCP": 4760, "m_szSnapshotFileName": 3368, "m_vServerControlPoints": 3912, "m_vecDataCPValue": 4748}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_szSnapshotFileName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_b<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFreezeTransitionDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nStopType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAnimateDuringGameplayPause", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flPreSimTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_vServerControlPoints", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_iServerControlPointAssignments", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hControlPointEnts", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_bNoSave", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoFreeze", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoRamp", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_PathParticleRope": {"fields": {"m_ColorTint": 1444, "m_PathNodes_Color": 1536, "m_PathNodes_Name": 1408, "m_PathNodes_PinEnabled": 1560, "m_PathNodes_Position": 1464, "m_PathNodes_RadiusScale": 1584, "m_PathNodes_TangentIn": 1488, "m_PathNodes_TangentOut": 1512, "m_bStartActive": 1392, "m_flMaxSimulationTime": 1396, "m_flParticleSpacing": 1432, "m_flRadius": 1440, "m_flSlack": 1436, "m_iEffectIndex": 1456, "m_iszEffectName": 1400, "m_nEffectState": 1448}, "metadata": [{"name": "m_flParticleSpacing", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSlack", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_ColorTint", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_nEffectState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iEffectIndex", "type": "NetworkVarNames", "type_name": "HParticleSystemDefinitionStrong"}, {"name": "m_PathNodes_Position", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_TangentIn", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_TangentOut", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_Color", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_PathNodes_PinEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_PathNodes_RadiusScale", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PathParticleRopeAlias_path_particle_rope_clientside": {"fields": {}, "metadata": [], "parent": "C_PathParticleRope"}, "C_PhysBox": {"fields": {}, "metadata": [], "parent": "C_Breakable"}, "C_PhysMagnet": {"fields": {"m_aAttachedObjects": 4000, "m_aAttachedObjectsFromServer": 3976}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_PhysPropClientside": {"fields": {"m_fDeathTime": 4372, "m_flTouchDelta": 4368, "m_inertiaScale": 4376, "m_nDamageType": 4404, "m_vecDamageDirection": 4392, "m_vecDamagePosition": 4380}, "metadata": [], "parent": "C_BreakableProp"}, "C_PhysicsProp": {"fields": {"m_bAwake": 4368}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_bAwake", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BreakableProp"}, "C_PhysicsPropMultiplayer": {"fields": {}, "metadata": [], "parent": "C_PhysicsProp"}, "C_PlantedC4": {"fields": {"m_AttributeManager": 4080, "m_bBeingDefused": 4044, "m_bBombDefused": 4068, "m_bBombTicking": 3984, "m_bC4Activated": 4056, "m_bCannotBeDefused": 4036, "m_bExplodeWarning": 4052, "m_bHasExploded": 4037, "m_bRadarFlash": 5280, "m_bTenSecWarning": 4057, "m_bTriggerWarning": 4048, "m_entitySpottedState": 4000, "m_fLastDefuseTime": 5288, "m_flC4Blow": 4032, "m_flC4ExplodeSpectateDuration": 5328, "m_flDefuseCountDown": 4064, "m_flDefuseLength": 4060, "m_flNextBeep": 4028, "m_flNextGlow": 4024, "m_flNextRadarFlashTime": 5276, "m_flTimerLength": 4040, "m_hBombDefuser": 4072, "m_hControlPanel": 4076, "m_hDefuserMultimeter": 5272, "m_nBombSite": 3988, "m_nSourceSoundscapeHash": 3992, "m_pBombDefuser": 5284, "m_pPredictionOwner": 5296, "m_vecC4ExplodeSpectateAng": 5316, "m_vecC4ExplodeSpectatePos": 5304}, "metadata": [{"name": "m_bBombTicking", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nBombSite", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nSourceSoundscapeHash", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_entitySpottedState", "type": "NetworkVarNames", "type_name": "EntitySpottedState_t"}, {"name": "m_flC4Blow", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bCannotBeDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bHasExploded", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimer<PERSON><PERSON>th", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bBeingDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDefuseLength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDefuseCountDown", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bBombDefused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hBombDefuser", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hControlPanel", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_AttributeManager", "type": "NetworkVarNames", "type_name": "CAttributeContainer"}], "parent": "CBaseAnimGraph"}, "C_PlayerPing": {"fields": {"m_bUrgent": 1444, "m_hPingedEntity": 1436, "m_hPlayer": 1432, "m_iType": 1440, "m_szPlaceName": 1445}, "metadata": [{"name": "m_hPlayer", "type": "NetworkVarNames", "type_name": "CHandle<CCSPlayerPawn>"}, {"name": "m_hPingedEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_iType", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_b<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_szPlaceName", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseEntity"}, "C_PlayerSprayDecal": {"fields": {"m_SprayRenderHelper": 3592, "m_flCreationTime": 3444, "m_nEntity": 3436, "m_nHitbox": 3440, "m_nPlayer": 3432, "m_nTintID": 3448, "m_nUniqueID": 3368, "m_nVersion": 3452, "m_rtGcTime": 3380, "m_ubSignature": 3453, "m_unAccountID": 3372, "m_unTraceID": 3376, "m_vecEndPos": 3384, "m_vecLeft": 3408, "m_vecNormal": 3420, "m_vecStart": 3396}, "metadata": [{"name": "m_nUniqueID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_unAccountID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unTraceID", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_rtGcTime", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_vecEndPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecStart", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecLeft", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecNormal", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_nPlayer", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nEntity", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nHitbox", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_flCreationTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nTintID", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVersion", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_ubSignature", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": "C_ModelPointEntity"}, "C_PlayerVisibility": {"fields": {"m_bIsEnabled": 1401, "m_bStartDisabled": 1400, "m_flFadeTime": 1396, "m_flFogDistanceMultiplier": 1388, "m_flFogMaxDensityMultiplier": 1392, "m_flVisibilityStrength": 1384}, "metadata": [{"name": "m_flVisibilityStrength", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogDistanceMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxDensityMultiplier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFadeTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStartDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bIsEnabled", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_PointCamera": {"fields": {"m_DegreesPerSecond": 1464, "m_FOV": 1384, "m_FogColor": 1393, "m_Resolution": 1388, "m_TargetFOV": 1460, "m_bActive": 1412, "m_bAlignWithParent": 1437, "m_bCanHLTVUse": 1436, "m_bDofEnabled": 1438, "m_bFogEnable": 1392, "m_bIsOn": 1468, "m_bNoSky": 1420, "m_bUseScreenAspectRatio": 1413, "m_fBrightness": 1424, "m_flAspectRatio": 1416, "m_flDofFarBlurry": 1452, "m_flDofFarCrisp": 1448, "m_flDofNearBlurry": 1440, "m_flDofNearCrisp": 1444, "m_flDofTiltToGround": 1456, "m_flFogEnd": 1404, "m_flFogMaxDensity": 1408, "m_flFogStart": 1400, "m_flZFar": 1428, "m_flZNear": 1432, "m_pNext": 1472}, "metadata": [{"name": "m_FOV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Resolution", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bFogEnable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_FogColor", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_flFogStart", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogEnd", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFogMaxDensity", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseScreenAspectRatio", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flAspectRatio", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bNoSky", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_fBrightness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flZFar", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flZNear", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bCanHLTVUse", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAlignWithParent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDofEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flDofNearBlurry", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofNearCrisp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofFarCrisp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofFarBlurry", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDofTiltToGround", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PointCameraVFOV": {"fields": {"m_flVerticalFOV": 1480}, "metadata": [], "parent": "C_PointCamera"}, "C_PointClientUIDialog": {"fields": {"m_bStartEnabled": 3420, "m_hActivator": 3416}, "metadata": [{"name": "m_hActivator", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIHUD": {"fields": {"m_bAllowInteractionFromAllSceneWorlds": 3848, "m_bCheckCSSClasses": 3424, "m_bIgnoreInput": 3808, "m_flDPI": 3820, "m_flDepthOffset": 3828, "m_flHeight": 3816, "m_flInteractDistance": 3824, "m_flWidth": 3812, "m_unHorizontalAlign": 3836, "m_unOrientation": 3844, "m_unOwnerContext": 3832, "m_unVerticalAlign": 3840, "m_vecCSSClasses": 3856}, "metadata": [{"name": "m_bIgnoreInput", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDPI", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInteractDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_unOwnerContext", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unHorizontalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unVerticalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unOrientation", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bAllowInteractionFromAllSceneWorlds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIWorldPanel": {"fields": {"m_anchorDeltaTransform": 3440, "m_bAllowInteractionFromAllSceneWorlds": 3928, "m_bCheckCSSClasses": 3426, "m_bDisableMipGen": 3967, "m_bExcludeFromSaveGames": 3964, "m_bFollowPlayerAcrossTeleport": 3890, "m_bForceRecreateNextUpdate": 3424, "m_bGrabbable": 3965, "m_bIgnoreInput": 3888, "m_bLit": 3889, "m_bMoveViewToPlayerNextThink": 3425, "m_bNoDepth": 3961, "m_bOnlyRenderToTexture": 3966, "m_bOpaque": 3960, "m_bRenderBackface": 3962, "m_bUseOffScreenIndicator": 3963, "m_flDPI": 3900, "m_flDepthOffset": 3908, "m_flHeight": 3896, "m_flInteractDistance": 3904, "m_flWidth": 3892, "m_nExplicitImageLayout": 3968, "m_pOffScreenIndicator": 3848, "m_unHorizontalAlign": 3916, "m_unOrientation": 3924, "m_unOwnerContext": 3912, "m_unVerticalAlign": 3920, "m_vecCSSClasses": 3936}, "metadata": [{"name": "m_bIgnoreInput", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bLit", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bFollowPlayerAcrossTeleport", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDPI", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flInteractDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_unOwnerContext", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unHorizontalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unVerticalAlign", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_unOrientation", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_bAllowInteractionFromAllSceneWorlds", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecCSSClasses", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_bOpaque", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNo<PERSON><PERSON>h", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRenderBackface", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUseOffScreenIndicator", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExcludeFromSaveGames", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bGrabbable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bOnlyRenderToTexture", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bDisableMipGen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nExplicitImageLayout", "type": "NetworkVarNames", "type_name": "int32"}], "parent": "C_BaseClientUIEntity"}, "C_PointClientUIWorldTextPanel": {"fields": {"m_messageText": 3984}, "metadata": [{"name": "m_messageText", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_PointClientUIWorldPanel"}, "C_PointCommentaryNode": {"fields": {"m_bActive": 3984, "m_bListenedTo": 4032, "m_bRestartAfterRestore": 4052, "m_bWasActive": 3985, "m_flEndTime": 3988, "m_flStartTime": 3992, "m_flStartTimeInCommentary": 3996, "m_hViewPosition": 4048, "m_iNodeNumber": 4024, "m_iNodeNumberMax": 4028, "m_iszCommentaryFile": 4000, "m_iszSpeakers": 4016, "m_iszTitle": 4008}, "metadata": [{"name": "m_bActive", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_flStartTimeInCommentary", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_iszCommentaryFile", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszTitle", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszSpeakers", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iNodeNumber", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iNodeNumberMax", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bListenedTo", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hViewPosition", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}], "parent": "CBaseAnimGraph"}, "C_PointEntity": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_PointValueRemapper": {"fields": {"m_bDisabled": 1384, "m_bDisabledOld": 1385, "m_bEngaged": 1480, "m_bFirstUpdate": 1481, "m_bRequiresUseKey": 1412, "m_bUpdateOnClient": 1386, "m_flCurrentMomentum": 1464, "m_flDisengageDistance": 1404, "m_flEngageDistance": 1408, "m_flInputOffset": 1476, "m_flMaximumChangePerSecond": 1400, "m_flMomentumModifier": 1456, "m_flPreviousUpdateTickTime": 1488, "m_flPreviousValue": 1484, "m_flRatchetOffset": 1472, "m_flSnapValue": 1460, "m_hOutputEntities": 1424, "m_hRemapLineEnd": 1396, "m_hRemapLineStart": 1392, "m_nHapticsType": 1448, "m_nInputType": 1388, "m_nMomentumType": 1452, "m_nOutputType": 1416, "m_nRatchetType": 1468, "m_vecPreviousTestPoint": 1492}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bUpdateOnClient", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nInputType", "type": "NetworkVarNames", "type_name": "ValueRemapperInputType_t"}, {"name": "m_hRemapLineStart", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_hRemapLineEnd", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_flMaximumChangePerSecond", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDisengageDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flEngageDistance", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bRequiresUseKey", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nOutputType", "type": "NetworkVarNames", "type_name": "ValueRemapperOutputType_t"}, {"name": "m_hOutputEntities", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_nHapticsType", "type": "NetworkVarNames", "type_name": "ValueRemapperHapticsType_t"}, {"name": "m_nMomentumType", "type": "NetworkVarNames", "type_name": "ValueRemapperMomentumType_t"}, {"name": "m_flMomentumModifier", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flSnapValue", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_nRatchetType", "type": "NetworkVarNames", "type_name": "ValueRemapperRatchetType_t"}, {"name": "m_flInputOffset", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_PointWorldText": {"fields": {"m_BackgroundMaterialName": 3976, "m_Color": 4072, "m_FontName": 3912, "m_bDrawBackground": 4056, "m_bEnabled": 4040, "m_bForceRecreateNextUpdate": 3376, "m_bFullbright": 4041, "m_flBackgroundBorderHeight": 4064, "m_flBackgroundBorderWidth": 4060, "m_flBackgroundWorldToUV": 4068, "m_flDepthOffset": 4052, "m_flFontSize": 4048, "m_flWorldUnitsPerPx": 4044, "m_messageText": 3400, "m_nJustifyHorizontal": 4076, "m_nJustifyVertical": 4080, "m_nReorientMode": 4084}, "metadata": [{"name": "m_messageText", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_FontName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_BackgroundMaterialName", "type": "NetworkVarNames", "type_name": "char"}, {"name": "m_bEnabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_b<PERSON><PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flWorldUnitsPerPx", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFontSize", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDepthOffset", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bDrawBackground", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flBackgroundBorderWidth", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBackgroundBorderHeight", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flBackgroundWorldToUV", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_Color", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_nJustifyHorizontal", "type": "NetworkVarNames", "type_name": "PointWorldTextJustifyHorizontal_t"}, {"name": "m_nJustifyVertical", "type": "NetworkVarNames", "type_name": "PointWorldTextJustifyVertical_t"}, {"name": "m_nReorientMode", "type": "NetworkVarNames", "type_name": "PointWorldTextReorientMode_t"}], "parent": "C_ModelPointEntity"}, "C_PortraitWorldCallbackHandler": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_PostProcessingVolume": {"fields": {"m_bExposureControl": 3437, "m_bMaster": 3436, "m_flExposureCompensation": 3420, "m_flExposureFadeSpeedDown": 3428, "m_flExposureFadeSpeedUp": 3424, "m_flFadeDuration": 3400, "m_flMaxExposure": 3416, "m_flMaxLogExposure": 3408, "m_flMinExposure": 3412, "m_flMinLogExposure": 3404, "m_flRate": 3440, "m_flTonemapEVSmoothingRange": 3432, "m_flTonemapMinAvgLum": 3452, "m_flTonemapPercentBrightPixels": 3448, "m_flTonemapPercentTarget": 3444, "m_hPostSettings": 3392}, "metadata": [{"name": "m_hPostSettings", "type": "NetworkVarNames", "type_name": "HPostProcessingStrong"}, {"name": "m_flFadeDuration", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinLogExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMaxLogExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMinExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flMaxExposure", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureCompensation", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureFadeSpeedUp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureFadeSpeedDown", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapEVSmoothingRange", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bMaster", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bExposureControl", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flRate", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapPercentTarget", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapPercentBrightPixels", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapMinAvgLum", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseTrigger"}, "C_Precipitation": {"fields": {"m_bActiveParticlePrecipEmitter": 3448, "m_bHasSimulatedSinceLastSceneObjectUpdate": 3450, "m_bParticlePrecipInitialized": 3449, "m_flDensity": 3376, "m_flParticleInnerDist": 3392, "m_nAvailableSheetSequencesMaxIndex": 3452, "m_pParticleDef": 3400, "m_tParticlePrecipTraceTimer": 3440}, "metadata": [], "parent": "C_BaseTrigger"}, "C_PrecipitationBlocker": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_PredictedViewModel": {"fields": {"m_currentSpeed": 4104, "m_targetSpeed": 4092, "m_vPredictedLagOffset": 4080}, "metadata": [], "parent": "C_BaseViewModel"}, "C_PropDoorRotating": {"fields": {}, "metadata": [], "parent": "C_BasePropDoor"}, "C_RagdollProp": {"fields": {"m_flBlendWeight": 4032, "m_flBlendWeightCurrent": 4044, "m_hRagdollSource": 4036, "m_iEyeAttachment": 4040, "m_parentPhysicsBoneIndices": 4048, "m_ragAngles": 4008, "m_ragPos": 3984, "m_worldSpaceBoneComputationOrder": 4072}, "metadata": [{"name": "m_ragPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_ragAngles", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "m_flBlendWeight", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_hRagdollSource", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": "CBaseAnimGraph"}, "C_RagdollPropAttached": {"fields": {"m_attachmentPointBoneSpace": 4104, "m_attachmentPointRagdollSpace": 4116, "m_bHasParent": 4144, "m_boneIndexAttached": 4096, "m_parentTime": 4140, "m_ragdollAttachedObjectIndex": 4100, "m_vecOffset": 4128}, "metadata": [{"name": "m_boneIndexAttached", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_ragdollAttachedObjectIndex", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_attachmentPointBoneSpace", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_attachmentPointRagdollSpace", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_RagdollProp"}, "C_RectLight": {"fields": {"m_bShowLight": 4208}, "metadata": [{"name": "m_bShowLight", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BarnLight"}, "C_RetakeGameRules": {"fields": {"m_bBlockersPresent": 252, "m_bRoundInProgress": 253, "m_iBombSite": 260, "m_iFirstSecondHalfRound": 256, "m_nMatchSeed": 248}, "metadata": [{"name": "m_nMatchSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bBlockersPresent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bRoundInProgress", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iFirstSecondHalfRound", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iBombSite", "type": "NetworkVarNames", "type_name": "int"}], "parent": null}, "C_RopeKeyframe": {"fields": {"m_LightValues": 4072, "m_LinksTouchingSomething": 3376, "m_PhysicsDelegate": 4224, "m_RopeFlags": 3432, "m_RopeLength": 4208, "m_Slack": 4210, "m_Subdiv": 4206, "m_TextureHeight": 4248, "m_TextureScale": 4212, "m_Width": 4220, "m_bApplyWind": 3384, "m_bConstrainBetweenEndpoints": 4360, "m_bEndPointAttachmentAnglesDirty": 0, "m_bEndPointAttachmentPositionsDirty": 0, "m_bNewDataThisFrame": 0, "m_bPhysicsInitted": 0, "m_bPrevEndPointPos": 3396, "m_fLockedPoints": 4216, "m_fPrevLockedPoints": 3388, "m_flCurScroll": 3424, "m_flCurrentGustLifetime": 4280, "m_flCurrentGustTimer": 4276, "m_flScrollSpeed": 3428, "m_flTimeToNextGust": 4284, "m_hEndPoint": 4200, "m_hMaterial": 4240, "m_hStartPoint": 4196, "m_iEndAttachment": 4205, "m_iForcePointMoveCounter": 3392, "m_iRopeMaterialModelIndex": 3440, "m_iStartAttachment": 4204, "m_nChangeCount": 4217, "m_nLinksTouchingSomething": 3380, "m_nSegments": 4192, "m_vCachedEndPointAttachmentAngle": 4336, "m_vCachedEndPointAttachmentPos": 4312, "m_vColorMod": 4300, "m_vPrevEndPointPos": 3400, "m_vWindDir": 4288, "m_vecImpulse": 4252, "m_vecPreviousImpulse": 4264}, "metadata": [{"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_flScrollSpeed", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_RopeFlags", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_iRopeMaterialModelIndex", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_nSegments", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_hStartPoint", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_hEndPoint", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseEntity>"}, {"name": "m_iStartAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_iEndAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_Subdiv", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_RopeLength", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "m_<PERSON>lack", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "m_TextureScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_fLockedPoints", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nChangeCount", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_Width", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bConstrainBetweenEndpoints", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseModelEntity"}, "C_RopeKeyframe__CPhysicsDelegate": {"fields": {"m_pKeyframe": 8}, "metadata": [], "parent": null}, "C_SceneEntity": {"fields": {"m_QueuedEvents": 1448, "m_bAutogenerated": 1395, "m_bClientOnly": 1402, "m_bIsPlayingBack": 1392, "m_bMultiplayer": 1394, "m_bPaused": 1393, "m_bWasPlaying": 1432, "m_flCurrentTime": 1472, "m_flForceClientTime": 1396, "m_hActorList": 1408, "m_hOwner": 1404, "m_nSceneStringIndex": 1400}, "metadata": [{"name": "m_bIsPlayingBack", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bMultiplayer", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bAutogenerated", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flForceClientTime", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nSceneStringIndex", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_hActorList", "type": "NetworkVarNames", "type_name": "CHandle<C_BaseFlex>"}], "parent": "C_PointEntity"}, "C_SceneEntity__QueuedEvents_t": {"fields": {"starttime": 0}, "metadata": [], "parent": null}, "C_ShatterGlassShardPhysics": {"fields": {"m_ShardDesc": 4384}, "metadata": [{"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "m_ShardDesc", "type": "NetworkVarNames", "type_name": "shard_model_desc_t"}], "parent": "C_PhysicsProp"}, "C_SingleplayRules": {"fields": {}, "metadata": [], "parent": "C_GameRules"}, "C_SkyCamera": {"fields": {"m_bUseAngles": 1532, "m_pNext": 1536, "m_skyboxData": 1384, "m_skyboxSlotToken": 1528}, "metadata": [{"name": "m_skyboxData", "type": "NetworkVarNames", "type_name": "sky3dparams_t"}, {"name": "m_skyboxSlotToken", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": "C_BaseEntity"}, "C_SmokeGrenade": {"fields": {}, "metadata": [], "parent": "C_BaseCSGrenade"}, "C_SmokeGrenadeProjectile": {"fields": {"m_VoxelFrameData": 4664, "m_bDidSmokeEffect": 4628, "m_bSmokeEffectSpawned": 4697, "m_bSmokeVolumeDataReceived": 4696, "m_nRandomSeed": 4632, "m_nSmokeEffectTickBegin": 4624, "m_nVoxelFrameDataSize": 4688, "m_nVoxelUpdate": 4692, "m_vSmokeColor": 4636, "m_vSmokeDetonationPos": 4648}, "metadata": [{"name": "m_nSmokeEffectTickBegin", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bDidSmokeEffect", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nRandomSeed", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_vSmokeColor", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vSmokeDetonationPos", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_VoxelFrameData", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nVoxelFrameDataSize", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoxelUpdate", "type": "NetworkVarNames", "type_name": "int"}], "parent": "C_BaseCSGrenadeProjectile"}, "C_SoundAreaEntityBase": {"fields": {"m_bDisabled": 1384, "m_bWasEnabled": 1392, "m_iszSoundAreaType": 1400, "m_vPos": 1408}, "metadata": [{"name": "m_bDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_iszSoundAreaType", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_vPos", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_BaseEntity"}, "C_SoundAreaEntityOrientedBox": {"fields": {"m_vMax": 1436, "m_vMin": 1424}, "metadata": [{"name": "m_vMin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMax", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundAreaEntityBase"}, "C_SoundAreaEntitySphere": {"fields": {"m_flRadius": 1424}, "metadata": [{"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_SoundAreaEntityBase"}, "C_SoundEventAABBEntity": {"fields": {"m_vMaxs": 1588, "m_vMins": 1576}, "metadata": [{"name": "m_vMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMaxs", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundEventEntity"}, "C_SoundEventEntity": {"fields": {"m_bClientSideOnly": 0, "m_bSaveRestore": 1387, "m_bSavedIsPlaying": 1388, "m_bStartOnSpawn": 1384, "m_bStopOnNew": 1386, "m_bToLocalPlayer": 1385, "m_flClientCullRadius": 1496, "m_flSavedElapsedTime": 1392, "m_hSource": 1560, "m_iszAttachmentName": 1408, "m_iszSoundName": 1544, "m_iszSourceEntityName": 1400, "m_nEntityIndexSelection": 1564, "m_onGUIDChanged": 1416, "m_onSoundFinished": 1456}, "metadata": [], "parent": "C_BaseEntity"}, "C_SoundEventEntityAlias_snd_event_point": {"fields": {}, "metadata": [], "parent": "C_SoundEventEntity"}, "C_SoundEventOBBEntity": {"fields": {"m_vMaxs": 1588, "m_vMins": 1576}, "metadata": [{"name": "m_vMins", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vMaxs", "type": "NetworkVarNames", "type_name": "Vector"}], "parent": "C_SoundEventEntity"}, "C_SoundEventPathCornerEntity": {"fields": {"m_vecCornerPairsNetworked": 1576}, "metadata": [{"name": "m_vecCornerPairsNetworked", "type": "NetworkVarNames", "type_name": "SoundeventPathCornerPairNetworked_t"}], "parent": "C_SoundEventEntity"}, "C_SoundEventSphereEntity": {"fields": {"m_flRadius": 1576}, "metadata": [{"name": "m_flRadius", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_SoundEventEntity"}, "C_SoundOpvarSetAABBEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetAutoRoomEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetOBBEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetAABBEntity"}, "C_SoundOpvarSetOBBWindEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointBase"}, "C_SoundOpvarSetPathCornerEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointEntity"}, "C_SoundOpvarSetPointBase": {"fields": {"m_bUseAutoCompare": 1412, "m_iOpvarIndex": 1408, "m_iszOperatorName": 1392, "m_iszOpvarName": 1400, "m_iszStackName": 1384}, "metadata": [{"name": "m_iszStackName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOperatorName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszOpvarName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iOpvarIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bUseAutoCompare", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_SoundOpvarSetPointEntity": {"fields": {}, "metadata": [], "parent": "C_SoundOpvarSetPointBase"}, "C_SpotlightEnd": {"fields": {"m_Radius": 3372, "m_flLightScale": 3368}, "metadata": [{"name": "m_flLightScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_<PERSON><PERSON>", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_Sprite": {"fields": {"m_bWorldSpaceScale": 3424, "m_flBrightnessDuration": 3412, "m_flBrightnessTimeStart": 3464, "m_flDestScale": 3448, "m_flDieTime": 3392, "m_flFrame": 3388, "m_flGlowProxySize": 3428, "m_flHDRColorScale": 3432, "m_flLastTime": 3436, "m_flMaxFrame": 3440, "m_flScaleDuration": 3420, "m_flScaleTimeStart": 3452, "m_flSpriteFramerate": 3384, "m_flSpriteScale": 3416, "m_flStartScale": 3444, "m_hAttachedToEntity": 3376, "m_hOldSpriteMaterial": 3472, "m_hSpriteMaterial": 3368, "m_nAttachment": 3380, "m_nBrightness": 3408, "m_nDestBrightness": 3460, "m_nSpriteHeight": 3636, "m_nSpriteWidth": 3632, "m_nStartBrightness": 3456}, "metadata": [{"name": "m_hSpriteMaterial", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hAttachedToEntity", "type": "NetworkVarNames", "type_name": "CHandle<CBaseEntity>"}, {"name": "m_nAttachment", "type": "NetworkVarNames", "type_name": "AttachmentHandle_t"}, {"name": "m_flSpriteFramerate", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFrame", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nBrightness", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_flBrightnessDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flSpriteScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flScaleDuration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_bWorldSpaceScale", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flGlowProxySize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_StattrakModule": {"fields": {"m_bKnife": 3984}, "metadata": [], "parent": "C_CS2WeaponModuleBase"}, "C_Sun": {"fields": {"m_bOn": 3428, "m_bmaxColor": 3429, "m_clrOverlay": 3424, "m_fdistNormalize": 3376, "m_flAlphaHaze": 3448, "m_flAlphaHdr": 3456, "m_flAlphaScale": 3452, "m_flFarZScale": 3460, "m_flHDRColorScale": 3444, "m_flHazeScale": 3436, "m_flRotation": 3440, "m_flSize": 3432, "m_fxSSSunFlareEffectIndex": 3368, "m_fxSunFlareEffectIndex": 3372, "m_iszEffectName": 3408, "m_iszSSEffectName": 3416, "m_vDirection": 3392, "m_vSunPos": 3380}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkExcludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByUserGroup", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_vDirection", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_iszEffectName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_iszSSEffectName", "type": "NetworkVarNames", "type_name": "string_t"}, {"name": "m_clrOverlay", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "m_bOn", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bmaxColor", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flSize", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHazeScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flRotation", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flHDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flAlphaHaze", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flAlphaScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flAlphaHdr", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_flFarZScale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": "C_BaseModelEntity"}, "C_Team": {"fields": {"m_aPlayerControllers": 1384, "m_aPlayers": 1408, "m_iScore": 1432, "m_szTeamname": 1436}, "metadata": [{"name": "MNetworkIncludeByName", "type": "Unknown"}, {"name": "m_aPlayerControllers", "type": "NetworkVarNames", "type_name": "CHandle<CBasePlayerController>"}, {"name": "m_aPlayers", "type": "NetworkVarNames", "type_name": "CHandle<C_BasePlayerPawn>"}, {"name": "m_iScore", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_szTeamname", "type": "NetworkVarNames", "type_name": "char"}], "parent": "C_BaseEntity"}, "C_TeamRoundTimer": {"fields": {"m_bAutoCountdown": 1412, "m_bFire10SecRemain": 1440, "m_bFire1MinRemain": 1438, "m_bFire1SecRemain": 1445, "m_bFire2MinRemain": 1437, "m_bFire2SecRemain": 1444, "m_bFire30SecRemain": 1439, "m_bFire3MinRemain": 1436, "m_bFire3SecRemain": 1443, "m_bFire4MinRemain": 1435, "m_bFire4SecRemain": 1442, "m_bFire5MinRemain": 1434, "m_bFire5SecRemain": 1441, "m_bFireFinished": 1433, "m_bInCaptureWatchState": 1425, "m_bIsDisabled": 1396, "m_bShowInHUD": 1397, "m_bStartPaused": 1424, "m_bStopWatchTimer": 1432, "m_bTimerPaused": 1384, "m_flTimeRemaining": 1388, "m_flTimerEndTime": 1392, "m_flTotalTime": 1428, "m_nOldTimerLength": 1448, "m_nOldTimerState": 1452, "m_nSetupTimeLength": 1416, "m_nState": 1420, "m_nTimerInitialLength": 1404, "m_nTimerLength": 1400, "m_nTimerMaxLength": 1408}, "metadata": [{"name": "m_bTimerPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTimeRemaining", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTimerEndTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_bIsDisabled", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bShowInHUD", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nTimer<PERSON>ength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTimerInitialLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nTimerMaxLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bAutoCountdown", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_nSetupTimeLength", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nState", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bStartPaused", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bInCaptureWatchState", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flTotalTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bStopWatchTimer", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_TeamplayRules": {"fields": {}, "metadata": [], "parent": "C_MultiplayRules"}, "C_TextureBasedAnimatable": {"fields": {"m_bLoop": 3368, "m_flFPS": 3372, "m_flStartFrame": 3420, "m_flStartTime": 3416, "m_hPositionKeys": 3376, "m_hRotationKeys": 3384, "m_vAnimationBoundsMax": 3404, "m_vAnimationBoundsMin": 3392}, "metadata": [{"name": "m_bLoop", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_flFPS", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_hPositionKeys", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_hRotationKeys", "type": "NetworkVarNames", "type_name": "HRenderTextureStrong"}, {"name": "m_vAnimationBoundsMin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vAnimationBoundsMax", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_flStartTime", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flStartFrame", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseModelEntity"}, "C_TintController": {"fields": {}, "metadata": [], "parent": "C_BaseEntity"}, "C_TonemapController2": {"fields": {"m_flAutoExposureMax": 1388, "m_flAutoExposureMin": 1384, "m_flExposureAdaptationSpeedDown": 1408, "m_flExposureAdaptationSpeedUp": 1404, "m_flTonemapEVSmoothingRange": 1412, "m_flTonemapMinAvgLum": 1400, "m_flTonemapPercentBrightPixels": 1396, "m_flTonemapPercentTarget": 1392}, "metadata": [{"name": "m_flAutoExposureMin", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flAutoExposureMax", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapPercentTarget", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapPercentBrightPixels", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapMinAvgLum", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureAdaptationSpeedUp", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flExposureAdaptationSpeedDown", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flTonemapEVSmoothingRange", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseEntity"}, "C_TonemapController2Alias_env_tonemap_controller2": {"fields": {}, "metadata": [], "parent": "C_TonemapController2"}, "C_TriggerBuoyancy": {"fields": {"m_BuoyancyHelper": 3376, "m_flFluidDensity": 3504}, "metadata": [{"name": "m_flFluidDensity", "type": "NetworkVarNames", "type_name": "float"}], "parent": "C_BaseTrigger"}, "C_TriggerLerpObject": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "C_TriggerMultiple": {"fields": {}, "metadata": [], "parent": "C_BaseTrigger"}, "C_TriggerPhysics": {"fields": {"m_angularDamping": 3392, "m_angularLimit": 3388, "m_bCollapseToForcePoint": 3420, "m_bConvertToDebrisWhenPossible": 3448, "m_flDampingRatio": 3404, "m_flFrequency": 3400, "m_gravityScale": 3376, "m_linearDamping": 3384, "m_linearForce": 3396, "m_linearLimit": 3380, "m_vecLinearForceDirection": 3436, "m_vecLinearForcePointAt": 3408, "m_vecLinearForcePointAtWorld": 3424}, "metadata": [{"name": "m_gravityScale", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearLimit", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearDamping", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_angularLimit", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_angularDamping", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_linearForce", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flFrequency", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_flDampingRatio", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_vecLinearForcePointAt", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bCollapseToForcePoint", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_vecLinearForcePointAtWorld", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_vecLinearForceDirection", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "m_bConvertToDebrisWhenPossible", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseTrigger"}, "C_TriggerVolume": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_ViewmodelAttachmentModel": {"fields": {"m_bCreatedLeftHanded": 3985, "m_bShouldFrontFaceCullLeftHanded": 3984}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_VoteController": {"fields": {"m_bIsYesNoVote": 1434, "m_bTypeDirty": 1433, "m_bVotesDirty": 1432, "m_iActiveIssueIndex": 1400, "m_iOnlyTeamToVote": 1404, "m_nPotentialVotes": 1428, "m_nVoteOptionCount": 1408}, "metadata": [{"name": "m_iActiveIssueIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_iOnlyTeamToVote", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nVoteOptionCount", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPotentialVotes", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bIsYesNoVote", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_BaseEntity"}, "C_WaterBullet": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_WeaponAWP": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponAug": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponBaseItem": {"fields": {"m_SequenceCompleteTimer": 6944, "m_bRedraw": 6968}, "metadata": [{"name": "m_SequenceCompleteTimer", "type": "NetworkVarNames", "type_name": "CountdownTimer"}, {"name": "m_bRedraw", "type": "NetworkVarNames", "type_name": "bool"}], "parent": "C_CSWeaponBase"}, "C_WeaponBizon": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponCZ75a": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponElite": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponFamas": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponFiveSeven": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponG3SG1": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponGalilAR": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponGlock": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponHKP2000": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM249": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM4A1": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponM4A1Silencer": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMAC10": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP5SD": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP7": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMP9": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponMag7": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponNOVA": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_WeaponNegev": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponP250": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponP90": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponRevolver": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSCAR20": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSG556": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSSG08": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponSawedoff": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_WeaponTaser": {"fields": {"m_fFireTime": 6976, "m_nLastAttackTick": 6980}, "metadata": [{"name": "m_fFireTime", "type": "NetworkVarNames", "type_name": "GameTime_t"}], "parent": "C_CSWeaponBaseGun"}, "C_WeaponTec9": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponUMP45": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponUSPSilencer": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBaseGun"}, "C_WeaponXM1014": {"fields": {}, "metadata": [], "parent": "C_CSWeaponBase"}, "C_World": {"fields": {}, "metadata": [], "parent": "C_BaseModelEntity"}, "C_WorldModelGloves": {"fields": {}, "metadata": [], "parent": "CBaseAnimGraph"}, "C_fogplayerparams_t": {"fields": {"m_NewColor": 40, "m_OldColor": 16, "m_flNewEnd": 48, "m_flNewFarZ": 60, "m_flNewHDRColorScale": 56, "m_flNewMaxDensity": 52, "m_flNewStart": 44, "m_flOldEnd": 24, "m_flOldFarZ": 36, "m_flOldHDRColorScale": 32, "m_flOldMaxDensity": 28, "m_flOldStart": 20, "m_flTransitionTime": 12, "m_hCtrl": 8}, "metadata": [{"name": "m_hCtrl", "type": "NetworkVarNames", "type_name": "CHandle<CFogController>"}], "parent": null}, "CountdownTimer": {"fields": {"m_duration": 8, "m_nWorldGroupId": 20, "m_timescale": 16, "m_timestamp": 12}, "metadata": [{"name": "m_duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_timescale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_nWorldGroupId", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": null}, "EngineCountdownTimer": {"fields": {"m_duration": 8, "m_timescale": 16, "m_timestamp": 12}, "metadata": [{"name": "m_duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "m_timescale", "type": "NetworkVarNames", "type_name": "float32"}], "parent": null}, "EntityRenderAttribute_t": {"fields": {"m_ID": 48, "m_Values": 52}, "metadata": [{"name": "m_ID", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}, {"name": "m_Values", "type": "NetworkVarNames", "type_name": "Vector4D"}], "parent": null}, "EntitySpottedState_t": {"fields": {"m_bSpotted": 8, "m_bSpottedByMask": 12}, "metadata": [{"name": "m_bSpotted", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bSpottedByMask", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": null}, "IntervalTimer": {"fields": {"m_nWorldGroupId": 12, "m_timestamp": 8}, "metadata": [{"name": "m_timestamp", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "m_nWorldGroupId", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": null}, "PhysicsRagdollPose_t": {"fields": {"m_Transforms": 8, "m_hOwner": 32}, "metadata": [{"name": "m_Transforms", "type": "NetworkVarNames", "type_name": "CTransform"}, {"name": "m_hOwner", "type": "NetworkVarNames", "type_name": "EHANDLE"}], "parent": null}, "PredictedDamageTag_t": {"fields": {"flFlinchModLarge": 56, "flFlinchModSmall": 52, "flFriendlyFireDamageReductionRatio": 60, "nTagTick": 48}, "metadata": [{"name": "nTagTick", "type": "NetworkVarNames", "type_name": "GameTick_t"}, {"name": "flFlinchModSmall", "type": "NetworkVarNames", "type_name": "float"}, {"name": "flFlinchModLarge", "type": "NetworkVarNames", "type_name": "float"}, {"name": "flFriendlyFireDamageReductionRatio", "type": "NetworkVarNames", "type_name": "float"}], "parent": null}, "SellbackPurchaseEntry_t": {"fields": {"m_bPrevHelmet": 60, "m_hItem": 64, "m_nCost": 52, "m_nPrevArmor": 56, "m_unDefIdx": 48}, "metadata": [{"name": "m_unDefIdx", "type": "NetworkVarNames", "type_name": "item_definition_index_t"}, {"name": "m_nCost", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_nPrevArmor", "type": "NetworkVarNames", "type_name": "int"}, {"name": "m_bPrevHelmet", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_hItem", "type": "NetworkVarNames", "type_name": "CEntityHandle"}], "parent": null}, "SequenceHistory_t": {"fields": {"m_flCyclesPerSecond": 20, "m_flPlaybackRate": 16, "m_flSeqFixedCycle": 8, "m_flSeqStartTime": 4, "m_hSequence": 0, "m_nSeqLoopMode": 12}, "metadata": [], "parent": null}, "ServerAuthoritativeWeaponSlot_t": {"fields": {"unClass": 40, "unItemDefIdx": 44, "unSlot": 42}, "metadata": [], "parent": null}, "VPhysicsCollisionAttribute_t": {"fields": {"m_nCollisionFunctionMask": 43, "m_nCollisionGroup": 42, "m_nEntityId": 32, "m_nHierarchyId": 40, "m_nInteractsAs": 8, "m_nInteractsExclude": 24, "m_nInteractsWith": 16, "m_nOwnerId": 36}, "metadata": [{"name": "m_nInteractsAs", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nInteractsWith", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nInteractsExclude", "type": "NetworkVarNames", "type_name": "uint64"}, {"name": "m_nEntityId", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nOwnerId", "type": "NetworkVarNames", "type_name": "uint32"}, {"name": "m_nHierarchyId", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nCollisionGroup", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "m_nCollisionFunctionMask", "type": "NetworkVarNames", "type_name": "uint8"}], "parent": null}, "ViewAngleServerChange_t": {"fields": {"nIndex": 64, "nType": 48, "qAngle": 52}, "metadata": [{"name": "nType", "type": "NetworkVarNames", "type_name": "FixAngleSet_t"}, {"name": "qAngle", "type": "NetworkVarNames", "type_name": "QAngle"}, {"name": "nIndex", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": null}, "WeaponPurchaseCount_t": {"fields": {"m_nCount": 50, "m_nItemDefIndex": 48}, "metadata": [{"name": "m_nItemDefIndex", "type": "NetworkVarNames", "type_name": "uint16"}, {"name": "m_nCount", "type": "NetworkVarNames", "type_name": "uint16"}], "parent": null}, "WeaponPurchaseTracker_t": {"fields": {"m_weaponPurchases": 8}, "metadata": [{"name": "m_weaponPurchases", "type": "NetworkVarNames", "type_name": "WeaponPurchaseCount_t"}], "parent": null}, "audioparams_t": {"fields": {"localBits": 108, "localSound": 8, "soundEventHash": 116, "soundscapeEntityListIndex": 112, "soundscapeIndex": 104}, "metadata": [{"name": "localSound", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "soundscapeIndex", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "localBits", "type": "NetworkVarNames", "type_name": "uint8"}, {"name": "soundscapeEntityListIndex", "type": "NetworkVarNames", "type_name": "int"}, {"name": "soundEventHash", "type": "NetworkVarNames", "type_name": "uint32"}], "parent": null}, "fogparams_t": {"fields": {"HDRColorScale": 56, "blend": 101, "blendtobackground": 88, "colorPrimary": 20, "colorPrimaryLerpTo": 28, "colorSecondary": 24, "colorSecondaryLerpTo": 32, "dirPrimary": 8, "duration": 84, "enable": 100, "end": 40, "endLerpTo": 72, "exponent": 52, "farz": 44, "lerptime": 80, "locallightscale": 96, "m_bNoReflectionFog": 102, "m_bPadding": 103, "maxdensity": 48, "maxdensityLerpTo": 76, "scattering": 92, "skyboxFogFactor": 60, "skyboxFogFactorLerpTo": 64, "start": 36, "startLerpTo": 68}, "metadata": [{"name": "dirPrimary", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "colorPrimary", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorSecondary", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorPrimaryLerpTo", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "colorSecondaryLerpTo", "type": "NetworkVarNames", "type_name": "Color"}, {"name": "start", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "end", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "farz", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "maxdensity", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "exponent", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "HDRColorScale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "skyboxFogFactor", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "skyboxFogFactorLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "startLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "endLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "maxdensityLerpTo", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "lerptime", "type": "NetworkVarNames", "type_name": "GameTime_t"}, {"name": "duration", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "blendtobackground", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "scattering", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "locallightscale", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "enable", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "blend", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bNoReflectionFog", "type": "NetworkVarNames", "type_name": "bool"}], "parent": null}, "shard_model_desc_t": {"fields": {"m_SurfacePropStringToken": 120, "m_bHasParent": 116, "m_bParentFrozen": 117, "m_flGlassHalfThickness": 112, "m_hMaterialBase": 16, "m_hMaterialDamageOverlay": 24, "m_nModelID": 8, "m_solid": 32, "m_vInitialPanelVertices": 88, "m_vecPanelSize": 36, "m_vecPanelVertices": 64, "m_vecStressPositionA": 44, "m_vecStressPositionB": 52}, "metadata": [{"name": "m_nModelID", "type": "NetworkVarNames", "type_name": "int32"}, {"name": "m_hMaterialBase", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_hMaterialDamageOverlay", "type": "NetworkVarNames", "type_name": "HMaterialStrong"}, {"name": "m_solid", "type": "NetworkVarNames", "type_name": "ShardSolid_t"}, {"name": "m_vecPanelSize", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecStressPositionA", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecStressPositionB", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vecPanelVertices", "type": "NetworkVarNames", "type_name": "Vector2D"}, {"name": "m_vInitialPanelVertices", "type": "NetworkVarNames", "type_name": "Vector4D"}, {"name": "m_flGlassHalfThickness", "type": "NetworkVarNames", "type_name": "float"}, {"name": "m_bHasParent", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_bParentFrozen", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "m_SurfacePropStringToken", "type": "NetworkVarNames", "type_name": "CUtlStringToken"}], "parent": null}, "sky3dparams_t": {"fields": {"bClip3DSkyBoxNearToWorldFar": 24, "flClip3DSkyBoxNearToWorldFarOffset": 28, "fog": 32, "m_nWorldGroupID": 136, "origin": 12, "scale": 8}, "metadata": [{"name": "scale", "type": "NetworkVarNames", "type_name": "int16"}, {"name": "origin", "type": "NetworkVarNames", "type_name": "Vector"}, {"name": "bClip3DSkyBoxNearToWorldFar", "type": "NetworkVarNames", "type_name": "bool"}, {"name": "flClip3DSkyBoxNearToWorldFarOffset", "type": "NetworkVarNames", "type_name": "float32"}, {"name": "fog", "type": "NetworkVarNames", "type_name": "fogparams_t"}, {"name": "m_nWorldGroupID", "type": "NetworkVarNames", "type_name": "WorldGroupId_t"}], "parent": null}}, "enums": {"CompMatPropertyMutatorConditionType_t": {"alignment": 4, "members": {"COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_EXISTS": 0, "COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_VALUE_EQUALS": 2, "COMP_MAT_MUTATOR_CONDITION_INPUT_CONTAINER_VALUE_EXISTS": 1}, "type": "uint32"}, "CompMatPropertyMutatorType_t": {"alignment": 4, "members": {"COMP_MAT_PROPERTY_MUTATOR_CONDITIONAL_MUTATORS": 6, "COMP_MAT_PROPERTY_MUTATOR_COPY_KEYS_WITH_SUFFIX": 2, "COMP_MAT_PROPERTY_MUTATOR_COPY_MATCHING_KEYS": 1, "COMP_MAT_PROPERTY_MUTATOR_COPY_PROPERTY": 3, "COMP_MAT_PROPERTY_MUTATOR_DRAW_TEXT": 8, "COMP_MAT_PROPERTY_MUTATOR_GENERATE_TEXTURE": 5, "COMP_MAT_PROPERTY_MUTATOR_INIT": 0, "COMP_MAT_PROPERTY_MUTATOR_POP_INPUT_QUEUE": 7, "COMP_MAT_PROPERTY_MUTATOR_RANDOM_ROLL_INPUT_VARIABLES": 9, "COMP_MAT_PROPERTY_MUTATOR_SET_VALUE": 4}, "type": "uint32"}, "CompositeMaterialInputContainerSourceType_t": {"alignment": 4, "members": {"CONTAINER_SOURCE_TYPE_LOOSE_VARIABLES": 3, "CONTAINER_SOURCE_TYPE_MATERIAL_FROM_TARGET_ATTR": 1, "CONTAINER_SOURCE_TYPE_SPECIFIC_MATERIAL": 2, "CONTAINER_SOURCE_TYPE_TARGET_INSTANCE_MATERIAL": 5, "CONTAINER_SOURCE_TYPE_TARGET_MATERIAL": 0, "CONTAINER_SOURCE_TYPE_VARIABLE_FROM_TARGET_ATTR": 4}, "type": "uint32"}, "CompositeMaterialInputLooseVariableType_t": {"alignment": 4, "members": {"LOOSE_VARIABLE_TYPE_BOOLEAN": 0, "LOOSE_VARIABLE_TYPE_COLOR4": 9, "LOOSE_VARIABLE_TYPE_FLOAT1": 5, "LOOSE_VARIABLE_TYPE_FLOAT2": 6, "LOOSE_VARIABLE_TYPE_FLOAT3": 7, "LOOSE_VARIABLE_TYPE_FLOAT4": 8, "LOOSE_VARIABLE_TYPE_INTEGER1": 1, "LOOSE_VARIABLE_TYPE_INTEGER2": 2, "LOOSE_VARIABLE_TYPE_INTEGER3": 3, "LOOSE_VARIABLE_TYPE_INTEGER4": 4, "LOOSE_VARIABLE_TYPE_PANORAMA_RENDER": 14, "LOOSE_VARIABLE_TYPE_RESOURCE_MATERIAL": 12, "LOOSE_VARIABLE_TYPE_RESOURCE_TEXTURE": 13, "LOOSE_VARIABLE_TYPE_STRING": 10, "LOOSE_VARIABLE_TYPE_SYSTEMVAR": 11}, "type": "uint32"}, "CompositeMaterialInputTextureType_t": {"alignment": 4, "members": {"INPUT_TEXTURE_TYPE_AO": 6, "INPUT_TEXTURE_TYPE_COLOR": 2, "INPUT_TEXTURE_TYPE_DEFAULT": 0, "INPUT_TEXTURE_TYPE_MASKS": 3, "INPUT_TEXTURE_TYPE_NORMALMAP": 1, "INPUT_TEXTURE_TYPE_PEARLESCENCE_MASK": 5, "INPUT_TEXTURE_TYPE_ROUGHNESS": 4}, "type": "uint32"}, "CompositeMaterialMatchFilterType_t": {"alignment": 4, "members": {"MATCH_FILTER_MATERIAL_ATTRIBUTE_EQUALS": 3, "MATCH_FILTER_MATERIAL_ATTRIBUTE_EXISTS": 0, "MATCH_FILTER_MATERIAL_NAME_SUBSTR": 2, "MATCH_FILTER_MATERIAL_PROPERTY_EQUALS": 5, "MATCH_FILTER_MATERIAL_PROPERTY_EXISTS": 4, "MATCH_FILTER_MATERIAL_SHADER": 1}, "type": "uint32"}, "CompositeMaterialVarSystemVar_t": {"alignment": 4, "members": {"COMPMATSYSVAR_COMPOSITETIME": 0, "COMPMATSYSVAR_EMPTY_RESOURCE_SPACER": 1}, "type": "uint32"}}}}