#include "pch.h"
#include "process.h"
#include "utils.h"

// Static member definitions
HMODULE ProcessManager::ntdllModule = nullptr;
ProcessManager::pNtQueryInformationProcess ProcessManager::NtQueryInformationProcess = nullptr;
ProcessManager::pNtReadVirtualMemory ProcessManager::NtReadVirtualMemory = nullptr;
ProcessManager::pNtWriteVirtualMemory ProcessManager::NtWriteVirtualMemory = nullptr;
ProcessManager::pNtAllocateVirtualMemory ProcessManager::NtAllocateVirtualMemory = nullptr;
ProcessManager::pNtFreeVirtualMemory ProcessManager::NtFreeVirtualMemory = nullptr;
ProcessManager::pNtProtectVirtualMemory ProcessManager::NtProtectVirtualMemory = nullptr;

std::vector<ProcessInfo> ProcessManager::GetProcessList() {
    std::vector<ProcessInfo> processes;
    
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return processes;
    }
    
    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            ProcessInfo info;
            info.pid = pe32.th32ProcessID;
            info.name = pe32.szExeFile;
            info.path = GetProcessPath(pe32.th32ProcessID);
            info.arch = GetProcessArchitecture(pe32.th32ProcessID);
            
            HANDLE hProcess = ::OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, pe32.th32ProcessID);
            if (hProcess) {
                info.isElevated = IsProcessElevated(hProcess);
                info.isWow64 = IsWow64Process(hProcess);
                CloseHandle(hProcess);
            }
            
            processes.push_back(info);
        } while (Process32NextW(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    return processes;
}

std::vector<DWORD> ProcessManager::FindProcessByName(const std::wstring& processName) {
    std::vector<DWORD> processIds;
    
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return processIds;
    }
    
    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);
    
    std::wstring lowerProcessName = Utils::ToLower(processName);
    
    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            std::wstring currentName = Utils::ToLower(pe32.szExeFile);
            if (currentName == lowerProcessName) {
                processIds.push_back(pe32.th32ProcessID);
            }
        } while (Process32NextW(hSnapshot, &pe32));
    }
    
    CloseHandle(hSnapshot);
    return processIds;
}

ProcessInfo ProcessManager::GetProcessInfo(DWORD processId) {
    ProcessInfo info;
    info.pid = processId;
    
    HANDLE hProcess = ::OpenProcess(PROCESS_QUERY_INFORMATION | PROCESS_VM_READ, FALSE, processId);
    if (hProcess) {
        info.name = GetProcessNameFromId(processId);
        info.path = GetProcessPath(hProcess);
        info.arch = GetProcessArchitecture(hProcess);
        info.isElevated = IsProcessElevated(hProcess);
        info.isWow64 = IsWow64Process(hProcess);
        CloseHandle(hProcess);
    }
    
    return info;
}

ProcessInfo ProcessManager::GetProcessInfo(const std::wstring& processName) {
    auto processIds = FindProcessByName(processName);
    if (!processIds.empty()) {
        return GetProcessInfo(processIds[0]);
    }
    
    return ProcessInfo{};
}

HANDLE ProcessManager::OpenProcess(DWORD processId, DWORD desiredAccess) {
    return ::OpenProcess(desiredAccess, FALSE, processId);
}

bool ProcessManager::IsProcessRunning(DWORD processId) {
    HANDLE hProcess = OpenProcess(processId, PROCESS_QUERY_INFORMATION);
    if (hProcess) {
        DWORD exitCode;
        bool running = GetExitCodeProcess(hProcess, &exitCode) && exitCode == STILL_ACTIVE;
        CloseHandle(hProcess);
        return running;
    }
    return false;
}

bool ProcessManager::IsProcessRunning(const std::wstring& processName) {
    auto processIds = FindProcessByName(processName);
    return !processIds.empty();
}

bool ProcessManager::WaitForProcess(const std::wstring& processName, DWORD timeout) {
    DWORD startTime = GetTickCount();
    
    while (GetTickCount() - startTime < timeout) {
        if (IsProcessRunning(processName)) {
            return true;
        }
        Sleep(100);
    }
    
    return false;
}

bool ProcessManager::TerminateProcess(DWORD processId) {
    HANDLE hProcess = OpenProcess(processId, PROCESS_TERMINATE);
    if (hProcess) {
        bool result = ::TerminateProcess(hProcess, 0);
        CloseHandle(hProcess);
        return result;
    }
    return false;
}

ProcessArchitecture ProcessManager::GetProcessArchitecture(HANDLE hProcess) {
    if (!hProcess) return ProcessArchitecture::UNKNOWN;
    
    BOOL isWow64 = FALSE;
    if (::IsWow64Process(hProcess, &isWow64)) {
        if (isWow64) {
            return ProcessArchitecture::X86;
        } else {
            // Check if we're on 64-bit system
            if (Utils::IsWindows64Bit()) {
                return ProcessArchitecture::X64;
            } else {
                return ProcessArchitecture::X86;
            }
        }
    }
    
    return ProcessArchitecture::UNKNOWN;
}

ProcessArchitecture ProcessManager::GetProcessArchitecture(DWORD processId) {
    HANDLE hProcess = OpenProcess(processId, PROCESS_QUERY_INFORMATION);
    if (hProcess) {
        ProcessArchitecture arch = GetProcessArchitecture(hProcess);
        CloseHandle(hProcess);
        return arch;
    }
    return ProcessArchitecture::UNKNOWN;
}

bool ProcessManager::IsProcess64Bit(HANDLE hProcess) {
    return GetProcessArchitecture(hProcess) == ProcessArchitecture::X64;
}

bool ProcessManager::IsProcessElevated(HANDLE hProcess) {
    HANDLE hToken = nullptr;
    if (!OpenProcessToken(hProcess, TOKEN_QUERY, &hToken)) {
        return false;
    }
    
    TOKEN_ELEVATION elevation;
    DWORD size;
    bool elevated = false;
    
    if (GetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &size)) {
        elevated = elevation.TokenIsElevated != 0;
    }
    
    CloseHandle(hToken);
    return elevated;
}

std::wstring ProcessManager::GetProcessPath(HANDLE hProcess) {
    wchar_t path[MAX_PATH];
    DWORD size = MAX_PATH;
    
    if (QueryFullProcessImageNameW(hProcess, 0, path, &size)) {
        return std::wstring(path);
    }
    
    return L"";
}

std::wstring ProcessManager::GetProcessPath(DWORD processId) {
    HANDLE hProcess = OpenProcess(processId, PROCESS_QUERY_INFORMATION);
    if (hProcess) {
        std::wstring path = GetProcessPath(hProcess);
        CloseHandle(hProcess);
        return path;
    }
    return L"";
}

std::vector<HMODULE> ProcessManager::GetProcessModules(HANDLE hProcess) {
    std::vector<HMODULE> modules;
    HMODULE hMods[1024];
    DWORD cbNeeded;
    
    if (EnumProcessModules(hProcess, hMods, sizeof(hMods), &cbNeeded)) {
        DWORD moduleCount = cbNeeded / sizeof(HMODULE);
        for (DWORD i = 0; i < moduleCount; ++i) {
            modules.push_back(hMods[i]);
        }
    }
    
    return modules;
}

HMODULE ProcessManager::GetModuleHandle(HANDLE hProcess, const std::wstring& moduleName) {
    auto modules = GetProcessModules(hProcess);
    std::wstring lowerModuleName = Utils::ToLower(moduleName);
    
    for (HMODULE hModule : modules) {
        std::wstring currentModulePath = GetModulePath(hProcess, hModule);
        std::wstring currentModuleName = currentModulePath.substr(currentModulePath.find_last_of(L"\\") + 1);
        
        if (Utils::ToLower(currentModuleName) == lowerModuleName) {
            return hModule;
        }
    }
    
    return nullptr;
}

LPVOID ProcessManager::GetModuleBaseAddress(HANDLE hProcess, const std::wstring& moduleName) {
    return (LPVOID)GetModuleHandle(hProcess, moduleName);
}

SIZE_T ProcessManager::GetModuleSize(HANDLE hProcess, HMODULE hModule) {
    MODULEINFO modInfo;
    if (GetModuleInformation(hProcess, hModule, &modInfo, sizeof(modInfo))) {
        return modInfo.SizeOfImage;
    }
    return 0;
}

std::wstring ProcessManager::GetModulePath(HANDLE hProcess, HMODULE hModule) {
    wchar_t path[MAX_PATH];
    if (GetModuleFileNameExW(hProcess, hModule, path, MAX_PATH)) {
        return std::wstring(path);
    }
    return L"";
}

std::vector<DWORD> ProcessManager::GetProcessThreads(DWORD processId) {
    std::vector<DWORD> threadIds;
    
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPTHREAD, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return threadIds;
    }
    
    THREADENTRY32 te32;
    te32.dwSize = sizeof(THREADENTRY32);
    
    if (Thread32First(hSnapshot, &te32)) {
        do {
            if (te32.th32OwnerProcessID == processId) {
                threadIds.push_back(te32.th32ThreadID);
            }
        } while (Thread32Next(hSnapshot, &te32));
    }
    
    CloseHandle(hSnapshot);
    return threadIds;
}

HANDLE ProcessManager::OpenThread(DWORD threadId, DWORD desiredAccess) {
    return ::OpenThread(desiredAccess, FALSE, threadId);
}

bool ProcessManager::SuspendThread(DWORD threadId) {
    HANDLE hThread = OpenThread(threadId, THREAD_SUSPEND_RESUME);
    if (hThread) {
        DWORD result = ::SuspendThread(hThread);
        CloseHandle(hThread);
        return result != -1;
    }
    return false;
}

bool ProcessManager::ResumeThread(DWORD threadId) {
    HANDLE hThread = OpenThread(threadId, THREAD_SUSPEND_RESUME);
    if (hThread) {
        DWORD result = ::ResumeThread(hThread);
        CloseHandle(hThread);
        return result != -1;
    }
    return false;
}

CONTEXT ProcessManager::GetThreadContext(HANDLE hThread) {
    CONTEXT context = {};
    context.ContextFlags = CONTEXT_FULL;
    ::GetThreadContext(hThread, &context);
    return context;
}

bool ProcessManager::SetThreadContext(HANDLE hThread, const CONTEXT& context) {
    return ::SetThreadContext(hThread, &context);
}

bool ProcessManager::ReadMemory(HANDLE hProcess, LPVOID address, LPVOID buffer, SIZE_T size) {
    SIZE_T bytesRead;
    return ReadProcessMemory(hProcess, address, buffer, size, &bytesRead) && bytesRead == size;
}

bool ProcessManager::WriteMemory(HANDLE hProcess, LPVOID address, LPCVOID data, SIZE_T size) {
    SIZE_T bytesWritten;
    return WriteProcessMemory(hProcess, address, data, size, &bytesWritten) && bytesWritten == size;
}

LPVOID ProcessManager::AllocateMemory(HANDLE hProcess, SIZE_T size, DWORD allocationType, DWORD protection) {
    return VirtualAllocEx(hProcess, nullptr, size, allocationType, protection);
}

bool ProcessManager::FreeMemory(HANDLE hProcess, LPVOID address, SIZE_T size) {
    return VirtualFreeEx(hProcess, address, size, MEM_RELEASE);
}

bool ProcessManager::ProtectMemory(HANDLE hProcess, LPVOID address, SIZE_T size, DWORD newProtection, DWORD* oldProtection) {
    return VirtualProtectEx(hProcess, address, size, newProtection, oldProtection);
}

bool ProcessManager::InjectShellcode(HANDLE hProcess, const std::vector<BYTE>& shellcode, LPVOID& allocatedAddress) {
    allocatedAddress = AllocateMemory(hProcess, shellcode.size(), MEM_COMMIT | MEM_RESERVE, PAGE_EXECUTE_READWRITE);
    if (!allocatedAddress) {
        return false;
    }

    if (!WriteMemory(hProcess, allocatedAddress, shellcode.data(), shellcode.size())) {
        FreeMemory(hProcess, allocatedAddress);
        allocatedAddress = nullptr;
        return false;
    }

    return true;
}

bool ProcessManager::CreateRemoteThread(HANDLE hProcess, LPTHREAD_START_ROUTINE startAddress, LPVOID parameter, HANDLE* hThread) {
    HANDLE thread = ::CreateRemoteThread(hProcess, nullptr, 0, startAddress, parameter, 0, nullptr);
    if (thread) {
        if (hThread) {
            *hThread = thread;
        } else {
            CloseHandle(thread);
        }
        return true;
    }
    return false;
}

bool ProcessManager::QueueUserAPC(HANDLE hThread, PAPCFUNC apcFunction, ULONG_PTR parameter) {
    return ::QueueUserAPC(apcFunction, hThread, parameter) != 0;
}

bool ProcessManager::EnablePrivilege(const std::wstring& privilegeName) {
    HANDLE hToken;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
        return false;
    }

    LUID luid;
    if (!LookupPrivilegeValueW(nullptr, privilegeName.c_str(), &luid)) {
        CloseHandle(hToken);
        return false;
    }

    TOKEN_PRIVILEGES tp;
    tp.PrivilegeCount = 1;
    tp.Privileges[0].Luid = luid;
    tp.Privileges[0].Attributes = SE_PRIVILEGE_ENABLED;

    bool result = AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), nullptr, nullptr);
    CloseHandle(hToken);

    return result && GetLastError() == ERROR_SUCCESS;
}

bool ProcessManager::DisablePrivilege(const std::wstring& privilegeName) {
    HANDLE hToken;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_ADJUST_PRIVILEGES | TOKEN_QUERY, &hToken)) {
        return false;
    }

    LUID luid;
    if (!LookupPrivilegeValueW(nullptr, privilegeName.c_str(), &luid)) {
        CloseHandle(hToken);
        return false;
    }

    TOKEN_PRIVILEGES tp;
    tp.PrivilegeCount = 1;
    tp.Privileges[0].Luid = luid;
    tp.Privileges[0].Attributes = 0;

    bool result = AdjustTokenPrivileges(hToken, FALSE, &tp, sizeof(tp), nullptr, nullptr);
    CloseHandle(hToken);

    return result && GetLastError() == ERROR_SUCCESS;
}

bool ProcessManager::HasPrivilege(const std::wstring& privilegeName) {
    HANDLE hToken;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken)) {
        return false;
    }

    LUID luid;
    if (!LookupPrivilegeValueW(nullptr, privilegeName.c_str(), &luid)) {
        CloseHandle(hToken);
        return false;
    }

    PRIVILEGE_SET ps;
    ps.PrivilegeCount = 1;
    ps.Control = PRIVILEGE_SET_ALL_NECESSARY;
    ps.Privilege[0].Luid = luid;
    ps.Privilege[0].Attributes = SE_PRIVILEGE_ENABLED;

    BOOL result;
    bool hasPrivilege = PrivilegeCheck(hToken, &ps, &result) && result;
    CloseHandle(hToken);

    return hasPrivilege;
}

bool ProcessManager::IsRunningAsAdmin() {
    HANDLE hToken;
    if (!OpenProcessToken(GetCurrentProcess(), TOKEN_QUERY, &hToken)) {
        return false;
    }

    TOKEN_ELEVATION elevation;
    DWORD size;
    bool isAdmin = false;

    if (GetTokenInformation(hToken, TokenElevation, &elevation, sizeof(elevation), &size)) {
        isAdmin = elevation.TokenIsElevated != 0;
    }

    CloseHandle(hToken);
    return isAdmin;
}

bool ProcessManager::ElevateProcess() {
    // This would require UAC elevation - simplified implementation
    return IsRunningAsAdmin();
}

bool ProcessManager::IsProcessBeingDebugged(HANDLE hProcess) {
    BOOL isDebugged = FALSE;
    CheckRemoteDebuggerPresent(hProcess, &isDebugged);
    return isDebugged;
}

bool ProcessManager::IsRemoteDebuggerPresent(HANDLE hProcess) {
    return IsProcessBeingDebugged(hProcess);
}

bool ProcessManager::CheckForHooks(HANDLE hProcess) {
    // Simplified implementation - check for common hooks
    // In a real implementation, you'd scan for hook patterns
    return false;
}

bool ProcessManager::IsProcessInSandbox(HANDLE hProcess) {
    // Check for sandbox indicators
    return Utils::IsRunningInSandbox();
}

bool ProcessManager::IsProcessVirtualized(HANDLE hProcess) {
    // Check for virtualization indicators
    return Utils::IsRunningInVM();
}

bool ProcessManager::EnableDebugPrivilege() {
    return EnablePrivilege(L"SeDebugPrivilege");
}

DWORD ProcessManager::GetProcessIdFromHandle(HANDLE hProcess) {
    return GetProcessId(hProcess);
}

std::wstring ProcessManager::GetProcessNameFromId(DWORD processId) {
    HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
    if (hSnapshot == INVALID_HANDLE_VALUE) {
        return L"";
    }

    PROCESSENTRY32W pe32;
    pe32.dwSize = sizeof(PROCESSENTRY32W);

    if (Process32FirstW(hSnapshot, &pe32)) {
        do {
            if (pe32.th32ProcessID == processId) {
                CloseHandle(hSnapshot);
                return pe32.szExeFile;
            }
        } while (Process32NextW(hSnapshot, &pe32));
    }

    CloseHandle(hSnapshot);
    return L"";
}

bool ProcessManager::IsWow64Process(HANDLE hProcess) {
    BOOL isWow64 = FALSE;
    ::IsWow64Process(hProcess, &isWow64);
    return isWow64;
}

bool ProcessManager::InitializeNtFunctions() {
    if (ntdllModule) return true;

    ntdllModule = ::GetModuleHandleW(L"ntdll.dll");
    if (!ntdllModule) return false;

    NtQueryInformationProcess = (pNtQueryInformationProcess)GetProcAddress(ntdllModule, "NtQueryInformationProcess");
    NtReadVirtualMemory = (pNtReadVirtualMemory)GetProcAddress(ntdllModule, "NtReadVirtualMemory");
    NtWriteVirtualMemory = (pNtWriteVirtualMemory)GetProcAddress(ntdllModule, "NtWriteVirtualMemory");
    NtAllocateVirtualMemory = (pNtAllocateVirtualMemory)GetProcAddress(ntdllModule, "NtAllocateVirtualMemory");
    NtFreeVirtualMemory = (pNtFreeVirtualMemory)GetProcAddress(ntdllModule, "NtFreeVirtualMemory");
    NtProtectVirtualMemory = (pNtProtectVirtualMemory)GetProcAddress(ntdllModule, "NtProtectVirtualMemory");

    return NtQueryInformationProcess && NtReadVirtualMemory && NtWriteVirtualMemory &&
           NtAllocateVirtualMemory && NtFreeVirtualMemory && NtProtectVirtualMemory;
}
