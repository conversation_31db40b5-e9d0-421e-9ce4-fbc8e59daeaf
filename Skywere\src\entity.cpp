#include "pch.h"

// Static member definitions
uintptr_t EntityManager::entityListBase = 0;
C_CSPlayerPawn* EntityManager::localPlayer = nullptr;
C_CSPlayerController* EntityManager::localPlayerController = nullptr;
std::vector<C_CSPlayerPawn*> EntityManager::players;
std::mutex EntityManager::playersMutex;

// C_BaseEntity implementation
int C_BaseEntity::GetHealth() const {
    return GetValue<int>(Offsets::Entity::m_iHealth);
}

int C_BaseEntity::GetMaxHealth() const {
    return GetValue<int>(Offsets::Entity::m_iMaxHealth);
}

int C_BaseEntity::GetTeamNum() const {
    return GetValue<int>(Offsets::Entity::m_nTeamNum);
}

Vector3 C_BaseEntity::GetOrigin() const {
    uintptr_t sceneNode = GetValue<uintptr_t>(Offsets::Entity::m_pGameSceneNode);
    if (!Memory::IsValidAddress(sceneNode)) return Vector3();
    
    return Memory::Read<Vector3>(sceneNode + Offsets::Entity::m_vecAbsOrigin);
}

QAngle C_BaseEntity::GetAngles() const {
    uintptr_t sceneNode = GetValue<uintptr_t>(Offsets::Entity::m_pGameSceneNode);
    if (!Memory::IsValidAddress(sceneNode)) return QAngle();
    
    return Memory::Read<QAngle>(sceneNode + Offsets::Entity::m_angAbsRotation);
}

Vector3 C_BaseEntity::GetVelocity() const {
    return GetValue<Vector3>(Offsets::Entity::m_vecVelocity);
}

bool C_BaseEntity::IsAlive() const {
    return GetHealth() > 0 && GetValue<uint8_t>(Offsets::Entity::m_lifeState) == 0;
}

bool C_BaseEntity::IsDormant() const {
    uintptr_t sceneNode = GetValue<uintptr_t>(Offsets::Entity::m_pGameSceneNode);
    if (!Memory::IsValidAddress(sceneNode)) return true;
    
    return Memory::Read<bool>(sceneNode + Offsets::Entity::m_bDormant);
}

EntityHandle C_BaseEntity::GetOwnerHandle() const {
    return EntityHandle(GetValue<uint32_t>(Offsets::Entity::m_hOwnerEntity));
}

C_BaseEntity* C_BaseEntity::GetOwner() const {
    EntityHandle handle = GetOwnerHandle();
    return EntityManager::GetEntityByHandle(handle);
}

float C_BaseEntity::DistanceTo(const Vector3& point) const {
    return (GetOrigin() - point).Length();
}

float C_BaseEntity::DistanceTo(const C_BaseEntity* other) const {
    if (!other) return FLT_MAX;
    return DistanceTo(other->GetOrigin());
}

// C_BasePlayerPawn implementation
EntityHandle C_BasePlayerPawn::GetControllerHandle() const {
    return EntityHandle(GetValue<uint32_t>(Offsets::Entity::m_hController));
}

C_CSPlayerController* C_BasePlayerPawn::GetController() const {
    EntityHandle handle = GetControllerHandle();
    return reinterpret_cast<C_CSPlayerController*>(EntityManager::GetEntityByHandle(handle));
}

EntityHandle C_BasePlayerPawn::GetActiveWeaponHandle() const {
    uintptr_t weaponServices = GetValue<uintptr_t>(Offsets::Entity::m_pWeaponServices);
    if (!Memory::IsValidAddress(weaponServices)) return EntityHandle();
    
    // This offset might need adjustment based on the actual weapon services structure
    return EntityHandle(Memory::Read<uint32_t>(weaponServices + 0x40));
}

C_CSWeaponBase* C_BasePlayerPawn::GetActiveWeapon() const {
    EntityHandle handle = GetActiveWeaponHandle();
    return reinterpret_cast<C_CSWeaponBase*>(EntityManager::GetEntityByHandle(handle));
}

Vector3 C_BasePlayerPawn::GetEyePosition() const {
    Vector3 origin = GetOrigin();
    uintptr_t cameraServices = GetValue<uintptr_t>(Offsets::Entity::m_pCameraServices);
    if (!Memory::IsValidAddress(cameraServices)) {
        // Default eye height if camera services not available
        origin.z += 64.0f;
        return origin;
    }
    
    // Read eye offset from camera services
    Vector3 eyeOffset = Memory::Read<Vector3>(cameraServices + 0x8);
    return origin + eyeOffset;
}

QAngle C_BasePlayerPawn::GetViewAngles() const {
    // View angles are typically stored in the client input or camera services
    uintptr_t clientBase = Memory::clientBase;
    if (!clientBase) return QAngle();
    
    return Memory::Read<QAngle>(clientBase + Offsets::Client::dwViewAngles);
}

void C_BasePlayerPawn::SetViewAngles(const QAngle& angles) {
    uintptr_t clientBase = Memory::clientBase;
    if (!clientBase) return;
    
    Memory::Write<QAngle>(clientBase + Offsets::Client::dwViewAngles, angles);
}

int C_BasePlayerPawn::GetFlags() const {
    return GetValue<int>(Offsets::Entity::m_fFlags);
}

bool C_BasePlayerPawn::IsOnGround() const {
    return (GetFlags() & FL_ONGROUND) != 0;
}

bool C_BasePlayerPawn::IsInAir() const {
    return !IsOnGround();
}

bool C_BasePlayerPawn::IsDucking() const {
    return (GetFlags() & FL_DUCKING) != 0;
}

// C_CSPlayerPawn implementation
bool C_CSPlayerPawn::IsScoped() const {
    return GetValue<bool>(Offsets::Entity::m_bIsScoped);
}

bool C_CSPlayerPawn::IsDefusing() const {
    return GetValue<bool>(Offsets::Entity::m_bIsDefusing);
}

bool C_CSPlayerPawn::IsGrabbingHostage() const {
    return GetValue<bool>(Offsets::Entity::m_bIsGrabbingHostage);
}

int C_CSPlayerPawn::GetShotsFired() const {
    return GetValue<int>(Offsets::Entity::m_iShotsFired);
}

int C_CSPlayerPawn::GetArmorValue() const {
    return GetValue<int>(Offsets::Entity::m_ArmorValue);
}

bool C_CSPlayerPawn::HasDefuser() const {
    return GetValue<bool>(Offsets::Entity::m_bHasDefuser);
}

bool C_CSPlayerPawn::HasHelmet() const {
    return GetValue<bool>(Offsets::Entity::m_bHasHelmet);
}

bool C_CSPlayerPawn::IsInBombZone() const {
    return GetValue<bool>(Offsets::Entity::m_bInBombZone);
}

bool C_CSPlayerPawn::IsInBuyZone() const {
    return GetValue<bool>(Offsets::Entity::m_bInBuyZone);
}

float C_CSPlayerPawn::GetFlashDuration() const {
    return GetValue<float>(Offsets::Entity::m_flFlashDuration);
}

float C_CSPlayerPawn::GetFlashMaxAlpha() const {
    return GetValue<float>(Offsets::Entity::m_flFlashMaxAlpha);
}

bool C_CSPlayerPawn::IsFlashed() const {
    return GetFlashDuration() > 0.0f;
}

bool C_CSPlayerPawn::IsEnemy(const C_CSPlayerPawn* other) const {
    if (!other) return false;
    return GetTeamNum() != other->GetTeamNum();
}

bool C_CSPlayerPawn::IsVisible() const {
    // This would require a more complex visibility check
    // For now, just check if not dormant
    return !IsDormant();
}

Vector3 C_CSPlayerPawn::GetBonePosition(int boneId) const {
    // Bone matrix calculation would go here
    // This is a simplified version
    Vector3 origin = GetOrigin();
    
    switch (boneId) {
    case BONE_HEAD:
        origin.z += 72.0f;
        break;
    case BONE_NECK:
        origin.z += 64.0f;
        break;
    case BONE_SPINE:
        origin.z += 48.0f;
        break;
    case BONE_PELVIS:
        origin.z += 32.0f;
        break;
    default:
        break;
    }
    
    return origin;
}

Vector3 C_CSPlayerPawn::GetHeadPosition() const {
    return GetBonePosition(BONE_HEAD);
}

// C_CSPlayerController implementation
std::string C_CSPlayerController::GetPlayerName() const {
    uintptr_t namePtr = GetValue<uintptr_t>(Offsets::Entity::m_sSanitizedPlayerName);
    if (!Memory::IsValidAddress(namePtr)) return "Unknown";

    return Memory::ReadString(namePtr, 64);
}

EntityHandle C_CSPlayerController::GetPawnHandle() const {
    return EntityHandle(GetValue<uint32_t>(Offsets::Entity::m_hPlayerPawn));
}

C_CSPlayerPawn* C_CSPlayerController::GetPawn() const {
    EntityHandle handle = GetPawnHandle();
    return reinterpret_cast<C_CSPlayerPawn*>(EntityManager::GetEntityByHandle(handle));
}

bool C_CSPlayerController::IsPawnAlive() const {
    return GetValue<bool>(Offsets::Entity::m_bPawnIsAlive);
}

int C_CSPlayerController::GetPawnHealth() const {
    return GetValue<int>(Offsets::Entity::m_iPawnHealth);
}

int C_CSPlayerController::GetPawnArmor() const {
    return GetValue<int>(Offsets::Entity::m_iPawnArmor);
}

bool C_CSPlayerController::PawnHasDefuser() const {
    return GetValue<bool>(Offsets::Entity::m_bPawnHasDefuser);
}

bool C_CSPlayerController::PawnHasHelmet() const {
    return GetValue<bool>(Offsets::Entity::m_bPawnHasHelmet);
}

bool C_CSPlayerController::IsValid() const {
    return GetAddress() != 0 && !IsDormant();
}

bool C_CSPlayerController::IsBot() const {
    // Check if this is a bot - this might need adjustment based on actual implementation
    return (GetValue<int>(0x6F0) & FL_FAKECLIENT) != 0;
}

// C_CSWeaponBase implementation
int C_CSWeaponBase::GetClip1() const {
    return GetValue<int>(Offsets::Entity::m_iClip1);
}

int C_CSWeaponBase::GetClip2() const {
    return GetValue<int>(Offsets::Entity::m_iClip2);
}

int C_CSWeaponBase::GetReserveAmmo() const {
    return GetValue<int>(Offsets::Entity::m_pReserveAmmo);
}

int C_CSWeaponBase::GetNextPrimaryAttackTick() const {
    return GetValue<int>(Offsets::Entity::m_nNextPrimaryAttackTick);
}

float C_CSWeaponBase::GetNextPrimaryAttackTickRatio() const {
    return GetValue<float>(Offsets::Entity::m_flNextPrimaryAttackTickRatio);
}

int C_CSWeaponBase::GetNextSecondaryAttackTick() const {
    return GetValue<int>(Offsets::Entity::m_nNextSecondaryAttackTick);
}

float C_CSWeaponBase::GetNextSecondaryAttackTickRatio() const {
    return GetValue<float>(Offsets::Entity::m_flNextSecondaryAttackTickRatio);
}

bool C_CSWeaponBase::CanShoot() const {
    // Simplified check - would need actual tick count comparison
    return GetClip1() > 0;
}

bool C_CSWeaponBase::IsReloading() const {
    // This would need more complex logic to determine reload state
    return false;
}

std::string C_CSWeaponBase::GetWeaponName() const {
    // This would require reading weapon data or class name
    return "Unknown";
}

// EntityManager implementation
bool EntityManager::Initialize() {
    LOG("Initializing entity manager...");

    if (!Memory::clientBase) {
        LOG("Client base not available!");
        return false;
    }

    entityListBase = Memory::clientBase + Offsets::Client::dwEntityList;

    if (!Memory::IsValidAddress(entityListBase)) {
        LOG("Invalid entity list address!");
        return false;
    }

    LOGF("Entity list base: 0x%llX", entityListBase);

    LOG("Entity manager initialized successfully!");
    return true;
}

void EntityManager::Update() {
    UpdateLocalPlayer();
    UpdatePlayers();
}

C_CSPlayerPawn* EntityManager::GetLocalPlayer() {
    return localPlayer;
}

C_CSPlayerController* EntityManager::GetLocalPlayerController() {
    return localPlayerController;
}

std::vector<C_CSPlayerPawn*> EntityManager::GetPlayers() {
    std::lock_guard<std::mutex> lock(playersMutex);
    return players;
}

std::vector<C_CSPlayerPawn*> EntityManager::GetEnemies() {
    std::vector<C_CSPlayerPawn*> enemies;
    if (!localPlayer) return enemies;

    int localTeam = localPlayer->GetTeamNum();

    std::lock_guard<std::mutex> lock(playersMutex);
    for (auto* player : players) {
        if (player && player != localPlayer && player->GetTeamNum() != localTeam) {
            enemies.push_back(player);
        }
    }

    return enemies;
}

std::vector<C_CSPlayerPawn*> EntityManager::GetTeammates() {
    std::vector<C_CSPlayerPawn*> teammates;
    if (!localPlayer) return teammates;

    int localTeam = localPlayer->GetTeamNum();

    std::lock_guard<std::mutex> lock(playersMutex);
    for (auto* player : players) {
        if (player && player != localPlayer && player->GetTeamNum() == localTeam) {
            teammates.push_back(player);
        }
    }

    return teammates;
}

C_BaseEntity* EntityManager::GetEntityByIndex(int index) {
    if (index < 0 || index > GetHighestEntityIndex()) return nullptr;

    uintptr_t listEntry = entityListBase + (index * 0x78);
    return GetEntityFromListEntry(listEntry);
}

C_BaseEntity* EntityManager::GetEntityByHandle(EntityHandle handle) {
    if (!handle.IsValid()) return nullptr;

    return GetEntityByIndex(handle.GetIndex());
}

int EntityManager::GetHighestEntityIndex() {
    uintptr_t gameEntitySystem = Memory::clientBase + Offsets::Client::dwGameEntitySystem;
    if (!Memory::IsValidAddress(gameEntitySystem)) return 0;

    return Memory::Read<int>(gameEntitySystem + Offsets::Client::dwGameEntitySystem_highestEntityIndex);
}

bool EntityManager::IsValidEntity(C_BaseEntity* entity) {
    if (!entity) return false;
    if (!Memory::IsValidAddress(entity->GetAddress())) return false;
    if (entity->IsDormant()) return false;

    return true;
}

bool EntityManager::IsPlayer(C_BaseEntity* entity) {
    if (!IsValidEntity(entity)) return false;

    // Check if entity is a player by examining class info or other identifiers
    // This is a simplified check
    uintptr_t address = entity->GetAddress();

    // Players typically have specific patterns in their vtable or class structure
    // This would need more sophisticated checking in a real implementation
    return true; // Simplified for now
}

void EntityManager::UpdateLocalPlayer() {
    uintptr_t localPlayerPawn = Memory::clientBase + Offsets::Client::dwLocalPlayerPawn;
    if (!Memory::IsValidAddress(localPlayerPawn)) {
        localPlayer = nullptr;
        return;
    }

    uintptr_t playerAddress = Memory::Read<uintptr_t>(localPlayerPawn);
    localPlayer = reinterpret_cast<C_CSPlayerPawn*>(playerAddress);

    // Update local player controller
    uintptr_t localPlayerController = Memory::clientBase + Offsets::Client::dwLocalPlayerController;
    if (Memory::IsValidAddress(localPlayerController)) {
        uintptr_t controllerAddress = Memory::Read<uintptr_t>(localPlayerController);
        EntityManager::localPlayerController = reinterpret_cast<C_CSPlayerController*>(controllerAddress);
    }
}

void EntityManager::UpdatePlayers() {
    std::vector<C_CSPlayerPawn*> newPlayers;

    int highestIndex = GetHighestEntityIndex();
    for (int i = 1; i <= highestIndex; ++i) {
        C_BaseEntity* entity = GetEntityByIndex(i);
        if (!IsValidEntity(entity)) continue;

        // Check if this is a player entity
        if (IsPlayer(entity)) {
            C_CSPlayerPawn* player = reinterpret_cast<C_CSPlayerPawn*>(entity);
            if (player->IsAlive()) {
                newPlayers.push_back(player);
            }
        }
    }

    std::lock_guard<std::mutex> lock(playersMutex);
    players = std::move(newPlayers);
}

C_BaseEntity* EntityManager::GetEntityFromListEntry(uintptr_t listEntry) {
    if (!Memory::IsValidAddress(listEntry)) return nullptr;

    uintptr_t entityAddress = Memory::Read<uintptr_t>(listEntry);
    if (!Memory::IsValidAddress(entityAddress)) return nullptr;

    return reinterpret_cast<C_BaseEntity*>(entityAddress);
}
