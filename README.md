# Skywere CS2 Cheat

Um cheat funcional para Counter-Strike 2 desenvolvido em C++ com interface ImGui.

## ⚠️ AVISO LEGAL

Este projeto é apenas para fins educacionais e de pesquisa. O uso de cheats em jogos online pode resultar em banimento permanente da sua conta. Use por sua própria conta e risco.

## 🚀 Características

### Aimbot
- FOV configurável
- Suavização de mira
- Seleção de hitbox (cabeça, pescoço, peito, etc.)
- Tiro automático
- Controle de recoil (RCS)
- Predição de movimento
- Verificação de visibilidade
- Verificação de equipe

### ESP (Wallhack)
- Caixas ao redor dos jogadores
- Nomes dos jogadores
- Vida e armadura
- Distância
- Esqueleto
- Linhas de mira
- Armas e bombas
- Cores configuráveis para inimigos/aliados

### Triggerbot
- Tiro automático quando mira em inimigo
- Delay configurável
- Verificação de visibilidade
- Verificação de equipe
- Auto wall (através de paredes)

### Misc
- Bunny Hop automático
- Auto Strafe
- No Flash (remove efeito de flash)
- No Smoke (remove fumaça)
- Radar Hack
- Lista de espectadores
- Marca d'água
- Contador de FPS

### Glow
- Brilho em jogadores
- Brilho em armas
- Brilho em bombas
- Cores configuráveis
- Estilos diferentes (contorno, completo, pulsante)

## 🛠️ Compilação

### Pré-requisitos
- Visual Studio 2022
- Windows SDK
- DirectX 11 SDK

### Dependências
- ImGui (incluído)
- DirectX 11
- Windows API

### Passos para compilar
1. Abra o arquivo `Skywere.sln` no Visual Studio 2022
2. Selecione a configuração `Release` e plataforma `x64`
3. Compile o projeto (Build > Build Solution)
4. O arquivo DLL será gerado em `x64/Release/Skywere.dll`

## 📁 Estrutura do Projeto

```
Skywere/
├── include/           # Arquivos de cabeçalho
│   ├── pch.h         # Cabeçalho pré-compilado
│   ├── offsets.h     # Offsets do CS2
│   ├── memory.h      # Sistema de memória
│   ├── entity.h      # Sistema de entidades
│   ├── math.h        # Funções matemáticas
│   ├── features.h    # Características do cheat
│   ├── gui.h         # Interface gráfica
│   └── config.h      # Sistema de configuração
├── src/              # Arquivos de implementação
│   ├── dllmain.cpp   # Ponto de entrada da DLL
│   ├── memory.cpp    # Implementação do sistema de memória
│   ├── entity.cpp    # Implementação do sistema de entidades
│   ├── math.cpp      # Implementação das funções matemáticas
│   ├── features.cpp  # Implementação das características
│   ├── gui.cpp       # Implementação da interface
│   └── config.cpp    # Implementação do sistema de configuração
└── output/           # Offsets atualizados do CS2
    ├── offsets.hpp
    ├── client_dll.hpp
    ├── buttons.hpp
    └── info.json
```

## 🎮 Como Usar

1. **Injeção da DLL:**
   - Use um injetor de DLL para injetar `Skywere.dll` no processo `cs2.exe`
   - Recomenda-se usar injetores que suportam manual mapping

2. **Controles:**
   - `INSERT` - Abrir/fechar menu
   - `DELETE` - Modo pânico (desativa todas as funções)
   - `END` - Descarregar o cheat

3. **Configuração:**
   - Use o menu para configurar todas as características
   - Salve/carregue configurações através da aba "Config"
   - Configurações são salvas em arquivos JSON

## ⚙️ Configurações

### Aimbot
- **FOV:** Campo de visão para detecção de alvos (1-20°)
- **Smoothness:** Suavidade da mira (0.1-10)
- **Hitbox:** Parte do corpo para mirar
- **Auto Shoot:** Atirar automaticamente
- **RCS:** Controle de recoil
- **Prediction:** Predição de movimento

### ESP
- **Players:** Mostrar jogadores
- **Boxes:** Caixas ao redor dos jogadores
- **Names:** Nomes dos jogadores
- **Health:** Vida dos jogadores
- **Armor:** Armadura dos jogadores
- **Distance:** Distância dos jogadores

### Misc
- **Bunny Hop:** Pulo automático (tecla: SPACE)
- **No Flash:** Remove efeito de flash
- **Radar Hack:** Mostra inimigos no radar

## 🔧 Offsets

Os offsets são atualizados automaticamente através do cs2-dumper. Os arquivos na pasta `output/` contêm os offsets mais recentes para:

- **Build:** 14084
- **Data:** 2025-06-30 21:10:27
- **Offsets principais:**
  - `dwLocalPlayerPawn`: 0x18560D0
  - `dwEntityList`: 0x1A020A8
  - `dwViewMatrix`: 0x1A6B230

## 🛡️ Detecção

Este cheat implementa várias técnicas para evitar detecção:

- Leitura de memória externa
- Sem modificação de arquivos do jogo
- Hooks mínimos
- Randomização de delays
- Modo pânico para desativação rápida

## 📝 Notas Técnicas

- **Arquitetura:** x64
- **Linguagem:** C++20
- **Renderização:** DirectX 11 + ImGui
- **Injeção:** DLL Injection
- **Padrão:** Manual Mapping recomendado

## 🤝 Contribuição

Este projeto é apenas para fins educacionais. Contribuições são bem-vindas para melhorar a qualidade do código e adicionar novas funcionalidades educativas.

## 📄 Licença

Este projeto é fornecido "como está" sem garantias. Use por sua própria conta e risco.

## ⚠️ Disclaimer

- Este software é apenas para fins educacionais
- O uso em servidores oficiais pode resultar em banimento
- Os desenvolvedores não se responsabilizam pelo uso indevido
- Sempre respeite os termos de serviço dos jogos
