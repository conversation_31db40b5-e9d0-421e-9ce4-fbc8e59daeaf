#pragma once
#include <windows.h>
#include <string>
#include <vector>
#include <random>
#include <fstream>
#include <wininet.h>
#include <lmcons.h>

class Utils {
public:
    // String utilities
    static std::wstring StringToWString(const std::string& str);
    static std::string WStringToString(const std::wstring& wstr);
    static std::string ToLower(const std::string& str);
    static std::wstring ToLower(const std::wstring& wstr);
    static bool EndsWith(const std::wstring& str, const std::wstring& suffix);
    static bool StartsWith(const std::wstring& str, const std::wstring& prefix);
    
    // File utilities
    static bool FileExists(const std::wstring& filePath);
    static bool DirectoryExists(const std::wstring& dirPath);
    static std::wstring GetCurrentDirectory();
    static std::wstring GetExecutableDirectory();
    static std::wstring GetTempDirectory();
    static SIZE_T GetFileSize(const std::wstring& filePath);
    static std::vector<BYTE> ReadFile(const std::wstring& filePath);
    static bool WriteFile(const std::wstring& filePath, const std::vector<BYTE>& data);
    static bool DeleteFile(const std::wstring& filePath);
    
    // System utilities
    static std::wstring GetSystemDirectory();
    static std::wstring GetWindowsDirectory();
    static bool IsWindows64Bit();
    static DWORD GetWindowsVersion();
    static std::wstring GetComputerName();
    static std::wstring GetUserName();
    
    // Cryptography utilities
    static std::vector<BYTE> GenerateRandomBytes(SIZE_T size);
    static DWORD GenerateRandomDWORD();
    static std::string GenerateRandomString(SIZE_T length);
    static std::vector<BYTE> XOREncrypt(const std::vector<BYTE>& data, const std::vector<BYTE>& key);
    static std::vector<BYTE> XORDecrypt(const std::vector<BYTE>& data, const std::vector<BYTE>& key);
    static DWORD CalculateCRC32(const std::vector<BYTE>& data);
    static std::string CalculateMD5(const std::vector<BYTE>& data);
    
    // Time utilities
    static DWORD GetCurrentTimestamp();
    static std::string GetCurrentTimeString();
    static void Sleep(DWORD milliseconds);
    static void RandomSleep(DWORD minMs, DWORD maxMs);
    
    // Registry utilities
    static bool ReadRegistryString(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, std::wstring& value);
    static bool WriteRegistryString(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, const std::wstring& value);
    static bool ReadRegistryDWORD(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, DWORD& value);
    static bool WriteRegistryDWORD(HKEY hKey, const std::wstring& subKey, const std::wstring& valueName, DWORD value);
    static bool DeleteRegistryKey(HKEY hKey, const std::wstring& subKey);
    
    // Network utilities
    static bool IsInternetConnected();
    static std::string DownloadString(const std::string& url);
    static std::vector<BYTE> DownloadFile(const std::string& url);
    static std::string GetPublicIP();
    
    // Hardware utilities
    static std::string GetCPUInfo();
    static std::string GetGPUInfo();
    static DWORD GetRAMSize();
    static std::string GetHardDriveSerial();
    static std::string GetMACAddress();
    static std::string GetHWID();
    static bool IsRunningAsAdmin();
    
    // Anti-detection utilities
    static bool IsRunningInVM();
    static bool IsRunningInSandbox();
    static bool IsBeingDebugged();
    static bool IsAnalysisToolPresent();
    static std::vector<std::wstring> GetRunningProcesses();
    static bool IsBlacklistedProcessRunning();
    
    // Memory utilities
    static void SecureZeroMemory(void* ptr, SIZE_T size);
    static bool IsValidPointer(const void* ptr);
    static LPVOID AlignToPage(LPVOID address);
    static SIZE_T AlignToPage(SIZE_T size);
    static DWORD GetPageSize();
    
    // Error handling
    static std::string GetLastErrorString();
    static std::string GetErrorString(DWORD errorCode);
    static void LogError(const std::string& function, DWORD errorCode = GetLastError());
    
    // Console utilities
    static void SetConsoleTitle(const std::wstring& title);
    static void SetConsoleColor(WORD color);
    static void ClearConsole();
    static bool AllocateConsole();
    static void FreeConsole();
    
private:
    // Internal utilities
    static std::random_device randomDevice;
    static std::mt19937 randomGenerator;
    static bool randomInitialized;
    
    static void InitializeRandom();
    static std::vector<std::wstring> blacklistedProcesses;
    static std::vector<std::wstring> vmProcesses;
    static std::vector<std::wstring> sandboxProcesses;
    
    // Crypto helpers
    static const DWORD crc32Table[256];
    static void InitializeCRC32Table();
};

// Configuration manager
class ConfigManager {
public:
    static bool LoadConfig(const std::wstring& configPath);
    static bool SaveConfig(const std::wstring& configPath);
    static void SetDefaultConfig();
    
    // Getters
    static InjectionMethod GetInjectionMethod();
    static bool GetRandomizeHeaders();
    static bool GetEraseHeaders();
    static bool GetHideFromPEB();
    static bool GetAntiDebug();
    static DWORD GetDelayMs();
    static bool GetScrambleImports();
    static bool GetUseNtApi();
    
    // Setters
    static void SetInjectionMethod(InjectionMethod method);
    static void SetRandomizeHeaders(bool enable);
    static void SetEraseHeaders(bool enable);
    static void SetHideFromPEB(bool enable);
    static void SetAntiDebug(bool enable);
    static void SetDelayMs(DWORD delay);
    static void SetScrambleImports(bool enable);
    static void SetUseNtApi(bool enable);
    
private:
    static InjectionConfig config;
    static bool configLoaded;
};

// Logger class
class Logger {
public:
    enum class LogLevel {
        INFO,
        WARNING,
        ERROR,
        DEBUG
    };
    
    static void Initialize(const std::wstring& logPath = L"");
    static void Shutdown();
    static void Log(LogLevel level, const std::string& message);
    static void LogInfo(const std::string& message);
    static void LogWarning(const std::string& message);
    static void LogError(const std::string& message);
    static void LogDebug(const std::string& message);
    
private:
    static std::ofstream logFile;
    static bool initialized;
    static std::string GetLogLevelString(LogLevel level);
    static std::string GetTimestamp();
};
