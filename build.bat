@echo off
echo Building Skywere CS2 Cheat...

REM Check if Visual Studio is installed
where msbuild >nul 2>nul
if %errorlevel% neq 0 (
    echo Error: MSBuild not found. Please install Visual Studio 2022.
    pause
    exit /b 1
)

REM Build the project
echo Building Release x64...
msbuild Skywere\Skywere.vcxproj /p:Configuration=Release /p:Platform=x64 /p:PlatformToolset=v143

if %errorlevel% equ 0 (
    echo.
    echo Build successful!
    echo Output: Skywere\x64\Release\Skywere.dll
    echo.
    echo IMPORTANT: This cheat is for educational purposes only.
    echo Use at your own risk. Using cheats may result in account bans.
) else (
    echo.
    echo Build failed! Check the error messages above.
)

pause
