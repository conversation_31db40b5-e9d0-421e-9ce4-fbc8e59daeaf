# Skywere Advanced DLL Injector

Um injetor de DLL avançado e indetectável desenvolvido para uso educacional com o cheat Skywere CS2.

## Características

### Métodos de Injeção
- **LoadLibrary Injection**: Método tradicional usando LoadLibraryW
- **Manual Mapping**: Mapeamento manual de DLL para máxima discrição
- **Thread Hijacking**: Sequestro de thread para execução stealth
- **Process Hollowing**: Substituição de processo para evasão avançada

### Recursos Anti-Detecção
- **Randomização de Headers**: Randomiza cabeçalhos PE para evitar assinaturas
- **Apagamento de Headers**: Remove cabeçalhos após injeção
- **Ocultação do PEB**: Esconde módulo da Process Environment Block
- **Scrambling de Imports**: Embaralha tabela de importações
- **Anti-Debug**: Detecta e evita debuggers
- **Detecção de VM/Sandbox**: Identifica ambientes virtualizados

### Técnicas Avançadas
- **Syscall Proxying**: Usa chamadas NTDLL para evasão
- **Reflective DLL Loading**: Carregamento reflexivo de DLL
- **Stealth Mapping**: Mapeamento com técnicas anti-análise
- **Handle Duplication**: Duplicação de handles para acesso stealth
- **Memory Encryption**: Criptografia de seções em memória

## Compilação

### Requisitos
- Visual Studio 2022 ou superior
- Windows SDK 10.0 ou superior
- C++20 Standard

### Instruções
1. Abra `Injector.sln` no Visual Studio
2. Selecione a configuração `Release` e plataforma `x64`
3. Compile o projeto (Build → Build Solution)
4. O executável será gerado em `x64/Release/Injector.exe`

## Uso

### Sintaxe Básica
```cmd
Injector.exe [opções] <nome_processo> <caminho_dll>
```

### Opções
- `-m, --method <método>`: Método de injeção (loadlibrary, manualmap, hijack, hollow)
- `-p, --pid <process_id>`: ID do processo alvo ao invés do nome
- `-w, --wait`: Aguarda o processo se não estiver executando
- `-s, --stealth`: Ativa todos os recursos stealth
- `-d, --delay <ms>`: Atraso antes da injeção (milissegundos)
- `-v, --verbose`: Saída detalhada
- `-h, --help`: Mostra ajuda

### Exemplos

#### Injeção Básica
```cmd
Injector.exe cs2.exe Skywere.dll
```

#### Injeção Manual Map com Stealth
```cmd
Injector.exe -m manualmap -s cs2.exe Skywere.dll
```

#### Injeção por PID com Atraso
```cmd
Injector.exe -p 1234 -d 5000 Skywere.dll
```

#### Aguardar Processo
```cmd
Injector.exe -w -v notepad.exe test.dll
```

## Configuração

### Arquivo de Configuração
O injector suporta configuração via arquivo JSON:

```json
{
    "injection_method": "manualmap",
    "randomize_headers": true,
    "erase_headers": true,
    "hide_from_peb": true,
    "anti_debug": true,
    "delay_execution": false,
    "delay_ms": 0,
    "scramble_imports": true,
    "use_nt_api": true
}
```

### Variáveis de Ambiente
- `INJECTOR_VERBOSE`: Ativa modo verbose
- `INJECTOR_STEALTH`: Ativa modo stealth por padrão
- `INJECTOR_METHOD`: Define método padrão de injeção

## Segurança

### Detecção de Ambiente
O injector detecta automaticamente:
- Máquinas virtuais (VMware, VirtualBox, QEMU, etc.)
- Ambientes sandbox (Cuckoo, Anubis, etc.)
- Debuggers (OllyDbg, x64dbg, WinDbg, etc.)
- Ferramentas de análise (Process Hacker, ProcMon, etc.)

### Evasão
- Usa APIs NTDLL para evitar hooks userland
- Randomiza endereços de memória
- Aplica delays aleatórios
- Verifica integridade do processo alvo
- Detecta presença de anti-cheat

## Compatibilidade

### Sistemas Operacionais
- Windows 10 (1903+)
- Windows 11
- Windows Server 2019/2022

### Arquiteturas
- x64 (recomendado)
- x86 (suporte limitado)

### Processos Alvo
- Aplicações 64-bit (para injector x64)
- Aplicações 32-bit (para injector x86)
- Processos com/sem privilégios elevados

## Limitações

### Restrições
- Requer privilégios administrativos para alguns métodos
- Pode ser detectado por anti-cheats avançados
- Funcionalidade limitada em ambientes altamente monitorados
- Alguns métodos podem não funcionar com CFG/CET ativado

### Processos Protegidos
- Processos do sistema críticos
- Processos com proteção PPL
- Aplicações com anti-tamper avançado

## Troubleshooting

### Problemas Comuns

#### "Access Denied"
- Execute como administrador
- Verifique se o processo alvo não está protegido
- Desative temporariamente o antivírus

#### "DLL not found"
- Verifique o caminho da DLL
- Certifique-se de que a DLL existe
- Use caminhos absolutos

#### "Architecture mismatch"
- Use injector x64 para processos x64
- Use injector x86 para processos x86
- Verifique a arquitetura do processo alvo

#### "Injection timeout"
- Aumente o timeout de injeção
- Verifique se o processo não está suspenso
- Tente um método de injeção diferente

### Logs
O injector gera logs detalhados quando executado com `-v`:
- Status de inicialização
- Detecção de ambiente
- Progresso da injeção
- Erros e avisos

## Desenvolvimento

### Estrutura do Projeto
```
Injector/
├── include/          # Headers
│   ├── pch.h        # Precompiled header
│   ├── injector.h   # Classe principal
│   ├── manual_map.h # Manual mapping
│   ├── process.h    # Gerenciamento de processos
│   └── utils.h      # Utilitários
├── src/             # Implementações
│   ├── main.cpp     # Ponto de entrada
│   ├── injector.cpp # Lógica principal
│   ├── manual_map.cpp
│   ├── process.cpp
│   └── utils.cpp
└── Injector.vcxproj # Projeto Visual Studio
```

### Extensibilidade
O código foi projetado para ser facilmente extensível:
- Novos métodos de injeção podem ser adicionados
- Técnicas anti-detecção podem ser implementadas
- Suporte para novos formatos de arquivo

## Aviso Legal

Este software é fornecido apenas para fins educacionais e de pesquisa. O uso deste software para atividades maliciosas ou ilegais é estritamente proibido. Os desenvolvedores não se responsabilizam pelo uso indevido deste software.

## Licença

Este projeto é licenciado sob a licença MIT. Veja o arquivo LICENSE para detalhes.

## Contribuição

Contribuições são bem-vindas! Por favor:
1. Faça fork do projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## Suporte

Para suporte técnico ou dúvidas:
- Abra uma issue no GitHub
- Consulte a documentação
- Verifique os logs de erro

---

**Desenvolvido para uso educacional com o Skywere CS2 Cheat**
