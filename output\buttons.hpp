// Generated using https://github.com/a2x/cs2-dumper
// 2025-06-30 21:10:27.187280500 UTC

#pragma once

#include <cstddef>

namespace cs2_dumper {
    // Module: client.dll
    namespace buttons {
        constexpr std::ptrdiff_t attack = 0x184E8F0;
        constexpr std::ptrdiff_t attack2 = 0x184E980;
        constexpr std::ptrdiff_t back = 0x184EBC0;
        constexpr std::ptrdiff_t duck = 0x184EE90;
        constexpr std::ptrdiff_t forward = 0x184EB30;
        constexpr std::ptrdiff_t jump = 0x184EE00;
        constexpr std::ptrdiff_t left = 0x184EC50;
        constexpr std::ptrdiff_t lookatweapon = 0x1A75170;
        constexpr std::ptrdiff_t reload = 0x184E860;
        constexpr std::ptrdiff_t right = 0x184ECE0;
        constexpr std::ptrdiff_t showscores = 0x1A75050;
        constexpr std::ptrdiff_t sprint = 0x184E7D0;
        constexpr std::ptrdiff_t turnleft = 0x184EA10;
        constexpr std::ptrdiff_t turnright = 0x184EAA0;
        constexpr std::ptrdiff_t use = 0x184ED70;
        constexpr std::ptrdiff_t zoom = 0x1A750E0;
    }
}
